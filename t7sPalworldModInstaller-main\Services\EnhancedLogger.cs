using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace ModInstallerApp.Services
{
    public class EnhancedLogger : IDisposable
    {
        private readonly string _logDirectory;
        private readonly string _sessionLogPath;
        private readonly ConcurrentQueue<LogEntry> _sessionLogs = new();
        private readonly object _lockObject = new();
        private readonly ConcurrentQueue<PerformanceMetric> _performanceMetrics = new();
        private readonly ConcurrentDictionary<string, Stopwatch> _activeOperations = new();
        private readonly Timer _logRotationTimer;
        private readonly Timer _logFlushTimer;
        private readonly SemaphoreSlim _fileSemaphore = new(1, 1);
        private readonly ConcurrentQueue<string> _pendingLogEntries = new();
        private bool _disposed = false;

        // ✅ Configuration for improved logging
        private readonly LoggerConfiguration _config;
        private volatile int _sessionLogCount = 0;
        private const int MAX_SESSION_LOGS = 10000;
        private const int MAX_PERFORMANCE_METRICS = 1000;
        private const long MAX_LOG_FILE_SIZE = 50 * 1024 * 1024; // 50MB

        // ✅ Sensitive data filters
        private static readonly HashSet<string> SensitiveKeys = new(StringComparer.OrdinalIgnoreCase)
        {
            "password", "token", "secret", "key", "auth", "credential", 
            "apikey", "api_key", "access_token", "refresh_token", "sessionid"
        };

        public event Action<LogEntry>? LogEntryAdded;

        public EnhancedLogger(string appDataPath, LoggerConfiguration? config = null)
        {
            _config = config ?? new LoggerConfiguration();
            _logDirectory = Path.GetFullPath(Path.Combine(appDataPath, "Logs"));
            Directory.CreateDirectory(_logDirectory);
            
            var sessionId = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
            _sessionLogPath = Path.Combine(_logDirectory, $"session_{sessionId}.log");
            
            // ✅ Setup log rotation timer (daily)
            _logRotationTimer = new Timer(PerformLogRotation, null, TimeSpan.FromDays(1), TimeSpan.FromDays(1));
            
            // ✅ Setup log flush timer (every 30 seconds)
            _logFlushTimer = new Timer(FlushPendingLogs, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
            
            // Clean old logs on startup
            CleanOldLogs();
            
            // Log session start with structured data
            LogInfo("Session started", "System", new { 
                SessionId = sessionId,
                AppDataPath = appDataPath,
                LogLevel = _config.MinimumLevel.ToString(),
                Version = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version?.ToString()
            });
        }

        public void LogInfo(string message, string component = "General", object? data = null)
        {
            Log(LogLevel.Info, message, component, data);
        }

        public void LogWarning(string message, string component = "General", object? data = null)
        {
            Log(LogLevel.Warning, message, component, data);
        }

        public void LogError(string message, string component = "General", Exception? exception = null, object? data = null)
        {
            var logData = data;
            if (exception != null)
            {
                logData = new { 
                    Data = data, 
                    Exception = new { 
                        exception.Message, 
                        exception.StackTrace,
                        Type = exception.GetType().Name,
                        Source = exception.Source
                    } 
                };
            }
            Log(LogLevel.Error, message, component, logData);
        }

        public void LogDebug(string message, string component = "General", object? data = null)
        {
            if (_config.MinimumLevel <= LogLevel.Debug)
            {
                Log(LogLevel.Debug, message, component, data);
            }
        }

        public void LogCritical(string message, string component = "General", object? data = null)
        {
            Log(LogLevel.Critical, message, component, data);
        }

        public void LogOperation(string operationName, string message, object? data = null)
        {
            Log(LogLevel.Operation, message, operationName, data);
        }

        public void StartPerformanceTracking(string operationName, string description = "")
        {
            if (_activeOperations.TryAdd(operationName, Stopwatch.StartNew()))
            {
                LogDebug($"Started tracking: {operationName}", "Performance", new { 
                    Description = description,
                    StartTime = DateTime.Now
                });
            }
        }

        public void StopPerformanceTracking(string operationName, object? additionalData = null)
        {
            if (_activeOperations.TryRemove(operationName, out var stopwatch))
            {
                stopwatch.Stop();
                
                var metric = new PerformanceMetric
                {
                    OperationName = operationName,
                    Duration = stopwatch.Elapsed,
                    Timestamp = DateTime.Now,
                    AdditionalData = SanitizeData(additionalData)
                };
                
                _performanceMetrics.Enqueue(metric);
                
                LogInfo($"Completed: {operationName} ({stopwatch.ElapsedMilliseconds}ms)", "Performance", 
                    SanitizeData(additionalData));
                
                // Keep only last N metrics in memory
                while (_performanceMetrics.Count > MAX_PERFORMANCE_METRICS)
                {
                    _performanceMetrics.TryDequeue(out _);
                }
            }
        }

        public TimeSpan MeasureOperation(string operationName, Action operation, object? data = null)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                LogDebug($"Starting: {operationName}", "Performance", SanitizeData(data));
                operation();
                stopwatch.Stop();
                LogInfo($"Completed: {operationName} ({stopwatch.ElapsedMilliseconds}ms)", "Performance", SanitizeData(data));
                return stopwatch.Elapsed;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                LogError($"Failed: {operationName} ({stopwatch.ElapsedMilliseconds}ms)", "Performance", ex, SanitizeData(data));
                throw;
            }
        }

        public async Task<TimeSpan> MeasureOperationAsync(string operationName, Func<Task> operation, object? data = null)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                LogDebug($"Starting: {operationName}", "Performance", SanitizeData(data));
                await operation();
                stopwatch.Stop();
                LogInfo($"Completed: {operationName} ({stopwatch.ElapsedMilliseconds}ms)", "Performance", SanitizeData(data));
                return stopwatch.Elapsed;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                LogError($"Failed: {operationName} ({stopwatch.ElapsedMilliseconds}ms)", "Performance", ex, SanitizeData(data));
                throw;
            }
        }

        public List<LogEntry> GetSessionLogs()
        {
            return _sessionLogs.ToList();
        }

        public List<LogEntry> GetRecentLogs(TimeSpan timespan)
        {
            var cutoff = DateTime.Now - timespan;
            return _sessionLogs.Where(l => l.Timestamp >= cutoff).ToList();
        }

        public List<LogEntry> GetLogsByComponent(string component)
        {
            return _sessionLogs.Where(l => l.Component.Equals(component, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        public List<LogEntry> GetLogsByLevel(LogLevel level)
        {
            return _sessionLogs.Where(l => l.Level == level).ToList();
        }

        public List<PerformanceMetric> GetPerformanceMetrics()
        {
            return _performanceMetrics.ToList();
        }

        public DiagnosticReport GenerateDiagnosticReport()
        {
            var logs = _sessionLogs.ToList();
            var metrics = _performanceMetrics.ToList();
            
            var report = new DiagnosticReport
            {
                GeneratedAt = DateTime.Now,
                SessionStartTime = logs.FirstOrDefault()?.Timestamp ?? DateTime.Now,
                TotalLogEntries = logs.Count,
                ErrorCount = logs.Count(l => l.Level == LogLevel.Error),
                WarningCount = logs.Count(l => l.Level == LogLevel.Warning),
                CriticalCount = logs.Count(l => l.Level == LogLevel.Critical),
                PerformanceMetrics = metrics,
                RecentErrors = logs.Where(l => l.Level == LogLevel.Error).TakeLast(10).ToList(),
                RecentWarnings = logs.Where(l => l.Level == LogLevel.Warning).TakeLast(10).ToList(),
                ComponentActivity = logs.GroupBy(l => l.Component)
                                       .ToDictionary(g => g.Key, g => g.Count()),
                AverageOperationTimes = metrics.GroupBy(m => m.OperationName)
                                               .ToDictionary(g => g.Key, g => g.Average(m => m.Duration.TotalMilliseconds)),
                LogFileSize = GetLogFileSize(),
                ActiveOperations = _activeOperations.Keys.ToList()
            };

            return report;
        }

        public async Task ExportDiagnosticReport(string filePath)
        {
            try
            {
                var report = GenerateDiagnosticReport();
                var json = JsonSerializer.Serialize(report, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    Converters = { new TimeSpanJsonConverter() }
                });
                
                await File.WriteAllTextAsync(filePath, json);
                LogInfo($"Diagnostic report exported to: {filePath}", "Logger");
            }
            catch (Exception ex)
            {
                LogError("Failed to export diagnostic report", "Logger", ex);
                throw;
            }
        }

        public async Task ExportSessionLogs(string filePath, LogFormat format = LogFormat.Text)
        {
            try
            {
                var logs = GetSessionLogs();
                
                switch (format)
                {
                    case LogFormat.Text:
                        await ExportAsText(logs, filePath);
                        break;
                    case LogFormat.Csv:
                        await ExportAsCsv(logs, filePath);
                        break;
                    case LogFormat.Json:
                        await ExportAsJson(logs, filePath);
                        break;
                }
                
                LogInfo($"Session logs exported to: {filePath} ({format})", "Logger");
            }
            catch (Exception ex)
            {
                LogError("Failed to export session logs", "Logger", ex);
                throw;
            }
        }

        // ✅ NEW: Structured logging with context
        public IDisposable BeginScope(string scopeName, object? data = null)
        {
            return new LoggingScope(this, scopeName, SanitizeData(data));
        }

        // ✅ NEW: Batch logging for performance
        public void LogBatch(IEnumerable<(LogLevel Level, string Message, string Component, object? Data)> entries)
        {
            foreach (var (level, message, component, data) in entries)
            {
                if (level >= _config.MinimumLevel)
                {
                    Log(level, message, component, data);
                }
            }
        }

        private void Log(LogLevel level, string message, string component, object? data)
        {
            if (level < _config.MinimumLevel || _disposed)
                return;

            try
            {
                var entry = new LogEntry
                {
                    Timestamp = DateTime.Now,
                    Level = level,
                    Component = component,
                    Message = message,
                    Data = SanitizeData(data),
                    ThreadId = Thread.CurrentThread.ManagedThreadId,
                    ProcessId = Environment.ProcessId
                };

                // Add to session logs with size limit
                _sessionLogs.Enqueue(entry);
                var newCount = Interlocked.Increment(ref _sessionLogCount);
                
                // Remove old entries if over limit
                if (newCount > MAX_SESSION_LOGS)
                {
                    if (_sessionLogs.TryDequeue(out _))
                    {
                        Interlocked.Decrement(ref _sessionLogCount);
                    }
                }

                // Queue for async file writing
                var logLine = FormatLogEntry(entry);
                _pendingLogEntries.Enqueue(logLine);
                
                // Notify listeners
                LogEntryAdded?.Invoke(entry);
                
                // Immediate flush for critical/error messages
                if (level >= LogLevel.Error)
                {
                    _ = Task.Run(() => FlushPendingLogs());
                }
            }
            catch (Exception ex)
            {
                // Fallback logging to prevent recursive errors
                try
                {
                    Console.WriteLine($"[LOGGER ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} - {ex.Message}");
                }
                catch
                {
                    // Ultimate fallback - ignore
                }
            }
        }

        // ✅ Enhanced data sanitization to remove sensitive information
        private object? SanitizeData(object? data)
        {
            if (data == null)
                return null;

            try
            {
                // Convert to JSON and back to sanitize
                var json = JsonSerializer.Serialize(data);
                var dict = JsonSerializer.Deserialize<Dictionary<string, object>>(json);
                
                if (dict != null)
                {
                    return SanitizeDictionary(dict);
                }
                
                return data;
            }
            catch
            {
                // If serialization fails, return string representation
                return data.ToString();
            }
        }

        private Dictionary<string, object> SanitizeDictionary(Dictionary<string, object> dict)
        {
            var sanitized = new Dictionary<string, object>();
            
            foreach (var kvp in dict)
            {
                if (SensitiveKeys.Contains(kvp.Key))
                {
                    sanitized[kvp.Key] = "[REDACTED]";
                }
                else if (kvp.Value is string strValue && IsLikelySensitive(strValue))
                {
                    sanitized[kvp.Key] = "[REDACTED]";
                }
                else if (kvp.Value is JsonElement jsonElement && jsonElement.ValueKind == JsonValueKind.Object)
                {
                    var nestedDict = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonElement.GetRawText());
                    if (nestedDict != null)
                    {
                        sanitized[kvp.Key] = SanitizeDictionary(nestedDict);
                    }
                    else
                    {
                        sanitized[kvp.Key] = kvp.Value;
                    }
                }
                else
                {
                    sanitized[kvp.Key] = kvp.Value;
                }
            }
            
            return sanitized;
        }

        private static bool IsLikelySensitive(string value)
        {
            // Check for patterns that might indicate sensitive data
            if (value.Length > 20 && value.Length < 200)
            {
                // Might be a token or key if it's mostly alphanumeric
                var alphanumericCount = value.Count(char.IsLetterOrDigit);
                if (alphanumericCount > value.Length * 0.8)
                {
                    return true;
                }
            }
            
            return false;
        }

        private string FormatLogEntry(LogEntry entry)
        {
            var sb = new StringBuilder();
            sb.Append($"[{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff}]");
            sb.Append($" [{entry.Level}]");
            sb.Append($" [{entry.Component}]");
            sb.Append($" [T:{entry.ThreadId}]");
            sb.Append($" {entry.Message}");
            
            if (entry.Data != null)
            {
                try
                {
                    var dataJson = JsonSerializer.Serialize(entry.Data, new JsonSerializerOptions { WriteIndented = false });
                    sb.Append($" | Data: {dataJson}");
                }
                catch
                {
                    sb.Append($" | Data: {entry.Data}");
                }
            }
            
            return sb.ToString();
        }

        private async void FlushPendingLogs(object? state = null)
        {
            if (_disposed || _pendingLogEntries.IsEmpty)
                return;

            try
            {
                await _fileSemaphore.WaitAsync(1000);
                try
                {
                    var entriesToWrite = new List<string>();
                    while (_pendingLogEntries.TryDequeue(out var entry) && entriesToWrite.Count < 100)
                    {
                        entriesToWrite.Add(entry);
                    }

                    if (entriesToWrite.Count > 0)
                    {
                        await File.AppendAllLinesAsync(_sessionLogPath, entriesToWrite);
                    }
                }
                finally
                {
                    _fileSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                // Log to console as fallback
                Console.WriteLine($"[LOGGER ERROR] Failed to flush logs: {ex.Message}");
            }
        }

        private async void PerformLogRotation(object? state)
        {
            try
            {
                var currentSize = GetLogFileSize();
                if (currentSize > MAX_LOG_FILE_SIZE)
                {
                    await _fileSemaphore.WaitAsync();
                    try
                    {
                        // Flush pending logs first
                        FlushPendingLogs();
                        
                        // Create new log file
                        var timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
                        var rotatedPath = Path.Combine(_logDirectory, $"session_{timestamp}_rotated.log");
                        
                        if (File.Exists(_sessionLogPath))
                        {
                            File.Move(_sessionLogPath, rotatedPath);
                        }
                        
                        LogInfo("Log file rotated due to size limit", "Logger", new { 
                            OldSize = currentSize,
                            RotatedTo = rotatedPath 
                        });
                    }
                    finally
                    {
                        _fileSemaphore.Release();
                    }
                }
                
                // Clean old logs
                CleanOldLogs();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[LOGGER ERROR] Log rotation failed: {ex.Message}");
            }
        }

        private long GetLogFileSize()
        {
            try
            {
                return File.Exists(_sessionLogPath) ? new FileInfo(_sessionLogPath).Length : 0;
            }
            catch
            {
                return 0;
            }
        }

        private async Task ExportAsText(List<LogEntry> logs, string filePath)
        {
            var lines = logs.Select(FormatLogEntry);
            await File.WriteAllLinesAsync(filePath, lines);
        }

        private async Task ExportAsCsv(List<LogEntry> logs, string filePath)
        {
            var lines = new List<string>
            {
                "Timestamp,Level,Component,ThreadId,ProcessId,Message,Data"
            };
            
            foreach (var log in logs)
            {
                var dataStr = log.Data != null ? JsonSerializer.Serialize(log.Data).Replace("\"", "\"\"") : "";
                lines.Add($"\"{log.Timestamp:yyyy-MM-dd HH:mm:ss.fff}\",\"{log.Level}\",\"{log.Component}\",\"{log.ThreadId}\",\"{log.ProcessId}\",\"{log.Message.Replace("\"", "\"\"")}\",\"{dataStr}\"");
            }
            
            await File.WriteAllLinesAsync(filePath, lines);
        }

        private async Task ExportAsJson(List<LogEntry> logs, string filePath)
        {
            var json = JsonSerializer.Serialize(logs, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(filePath, json);
        }

        private void CleanOldLogs()
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-_config.RetentionDays);
                var logFiles = Directory.GetFiles(_logDirectory, "session_*.log");
                
                foreach (var logFile in logFiles)
                {
                    if (File.GetCreationTime(logFile) < cutoffDate)
                    {
                        File.Delete(logFile);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[LOGGER ERROR] Failed to clean old logs: {ex.Message}");
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _disposed = true;
                
                // Flush any remaining logs
                FlushPendingLogs();
                
                // Dispose timers
                _logRotationTimer?.Dispose();
                _logFlushTimer?.Dispose();
                
                // Dispose semaphore
                _fileSemaphore?.Dispose();
                
                LogInfo("Logger disposed", "System");
            }
        }

        ~EnhancedLogger()
        {
            Dispose(false);
        }
    }

    // ✅ Enhanced logging models
    public class LogEntry
    {
        public DateTime Timestamp { get; set; }
        public LogLevel Level { get; set; }
        public string Component { get; set; } = "";
        public string Message { get; set; } = "";
        public object? Data { get; set; }
        public int ThreadId { get; set; }
        public int ProcessId { get; set; }
    }

    public class PerformanceMetric
    {
        public string OperationName { get; set; } = "";
        public TimeSpan Duration { get; set; }
        public DateTime Timestamp { get; set; }
        public object? AdditionalData { get; set; }
    }

    public class DiagnosticReport
    {
        public DateTime GeneratedAt { get; set; }
        public DateTime SessionStartTime { get; set; }
        public int TotalLogEntries { get; set; }
        public int ErrorCount { get; set; }
        public int WarningCount { get; set; }
        public int CriticalCount { get; set; }
        public List<PerformanceMetric> PerformanceMetrics { get; set; } = new();
        public List<LogEntry> RecentErrors { get; set; } = new();
        public List<LogEntry> RecentWarnings { get; set; } = new();
        public Dictionary<string, int> ComponentActivity { get; set; } = new();
        public Dictionary<string, double> AverageOperationTimes { get; set; } = new();
        public long LogFileSize { get; set; }
        public List<string> ActiveOperations { get; set; } = new();
    }

    public class LoggerConfiguration
    {
        public LogLevel MinimumLevel { get; set; } = LogLevel.Info;
        public int RetentionDays { get; set; } = 30;
        public bool EnableStructuredLogging { get; set; } = true;
        public bool EnablePerformanceTracking { get; set; } = true;
        public int MaxLogFileSize { get; set; } = 50 * 1024 * 1024; // 50MB
    }

    public class LoggingScope : IDisposable
    {
        private readonly EnhancedLogger _logger;
        private readonly string _scopeName;
        private readonly DateTime _startTime;

        public LoggingScope(EnhancedLogger logger, string scopeName, object? data)
        {
            _logger = logger;
            _scopeName = scopeName;
            _startTime = DateTime.Now;
            
            _logger.LogDebug($"Entering scope: {scopeName}", "Scope", data);
        }

        public void Dispose()
        {
            var duration = DateTime.Now - _startTime;
            _logger.LogDebug($"Exiting scope: {_scopeName} (Duration: {duration.TotalMilliseconds:F1}ms)", "Scope");
        }
    }

    // Custom JSON converter for TimeSpan
    public class TimeSpanJsonConverter : System.Text.Json.Serialization.JsonConverter<TimeSpan>
    {
        public override TimeSpan Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            return TimeSpan.Parse(reader.GetString()!);
        }

        public override void Write(Utf8JsonWriter writer, TimeSpan value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString());
        }
    }

    public enum LogLevel
    {
        Debug = 0,
        Info = 1,
        Warning = 2,
        Error = 3,
        Critical = 4,
        Operation = 5
    }

    public enum LogFormat
    {
        Text,
        Csv,
        Json
    }
}