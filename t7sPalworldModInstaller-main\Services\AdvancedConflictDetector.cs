using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    public class AdvancedConflictDetector
    {
        private readonly string _palRoot;
        private readonly UE4SSDetector _detector;
        private readonly EnhancedLogger _logger;

        public AdvancedConflictDetector(string palRoot, UE4SSDetector detector, EnhancedLogger logger)
        {
            _palRoot = palRoot ?? throw new ArgumentNullException(nameof(palRoot));
            _detector = detector ?? throw new ArgumentNullException(nameof(detector));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ConflictDetectionResult> DetectConflictsAsync(AdvancedModStructure modStructure, CancellationToken cancellationToken = default)
        {
            return await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();

                var result = new ConflictDetectionResult
                {
                    ModName = modStructure.Name,
                    AnalysisDate = DateTime.Now
                };

                // Detect file conflicts
                DetectFileConflicts(modStructure, result);

                // Detect dependency conflicts
                DetectDependencyConflicts(modStructure, result);

                // Detect framework conflicts
                DetectFrameworkConflicts(modStructure, result);

                // Detect PAK file conflicts
                DetectPakFileConflicts(modStructure, result);

                // Detect UE4SS mod conflicts
                DetectUE4SSModConflicts(modStructure, result);

                // Detect PalSchema conflicts
                DetectPalSchemaConflicts(modStructure, result);

                // Analyze conflict severity
                AnalyzeConflictSeverity(result);

                _logger.LogInfo($"Conflict detection completed for {modStructure.Name}: " +
                              $"{result.Conflicts.Count} conflicts found, " +
                              $"Severity: {result.OverallSeverity}", "ConflictDetection");

                return result;
            }, cancellationToken);
        }

        private void DetectFileConflicts(AdvancedModStructure modStructure, ConflictDetectionResult result)
        {
            // Check for conflicts with existing files
            foreach (var file in modStructure.Files)
            {
                var targetPath = Path.Combine(_palRoot, file);
                if (File.Exists(targetPath))
                {
                    var conflict = new AdvancedModConflict
                    {
                        Type = InstallationConflictType.FileOverwrite,
                        Description = $"File will overwrite existing: {file}",
                        AffectedFile = file,
                        Severity = DetermineFileSeverity(file),
                        ResolutionOptions = new List<string> { "Backup and overwrite", "Skip file", "Rename existing" }
                    };
                    result.Conflicts.Add(conflict);
                }
            }

            // Check for critical system file conflicts
            var criticalFiles = new[] { "dwmapi.dll", "UE4SS.dll", "Pal-Windows.pak" };
            foreach (var criticalFile in criticalFiles)
            {
                if (modStructure.Files.Any(f => Path.GetFileName(f).Equals(criticalFile, StringComparison.OrdinalIgnoreCase)))
                {
                    var conflict = new AdvancedModConflict
                    {
                        Type = InstallationConflictType.SystemFileConflict,
                        Description = $"Mod attempts to modify critical system file: {criticalFile}",
                        AffectedFile = criticalFile,
                        Severity = InstallationConflictSeverity.Critical,
                        ResolutionOptions = new List<string> { "Block installation", "Create backup", "Expert mode only" }
                    };
                    result.Conflicts.Add(conflict);
                }
            }
        }

        private void DetectDependencyConflicts(AdvancedModStructure modStructure, ConflictDetectionResult result)
        {
            var currentUE4SS = _detector.DetectUE4SS();
            var currentPalSchema = _detector.DetectPalSchema();

            // Check UE4SS dependencies
            if (modStructure.CompatibilityInfo.RequiresUE4SS && currentUE4SS.Status == UE4SSInstallStatus.NotInstalled)
            {
                var conflict = new AdvancedModConflict
                {
                    Type = InstallationConflictType.MissingDependency,
                    Description = "Mod requires UE4SS but it is not installed",
                    Severity = InstallationConflictSeverity.High,
                    ResolutionOptions = new List<string> { "Install UE4SS first", "Skip mod installation" }
                };
                result.Conflicts.Add(conflict);
            }

            // Check PalSchema dependencies
            if (modStructure.CompatibilityInfo.RequiresPalSchema && !currentPalSchema.IsInstalled)
            {
                var conflict = new AdvancedModConflict
                {
                    Type = InstallationConflictType.MissingDependency,
                    Description = "Mod requires PalSchema but it is not installed",
                    Severity = InstallationConflictSeverity.High,
                    ResolutionOptions = new List<string> { "Install PalSchema first", "Skip mod installation" }
                };
                result.Conflicts.Add(conflict);
            }

            // Check for missing mod dependencies
            foreach (var dependency in modStructure.Dependencies)
            {
                if (!IsModInstalled(dependency))
                {
                    var conflict = new AdvancedModConflict
                    {
                        Type = InstallationConflictType.MissingDependency,
                        Description = $"Required dependency not found: {dependency}",
                        Severity = InstallationConflictSeverity.Medium,
                        ResolutionOptions = new List<string> { "Install dependency", "Continue without dependency", "Skip mod" }
                    };
                    result.Conflicts.Add(conflict);
                }
            }
        }

        private void DetectFrameworkConflicts(AdvancedModStructure modStructure, ConflictDetectionResult result)
        {
            // Check for conflicting modding frameworks
            var installedFrameworks = GetInstalledFrameworks();
            var incompatibleFrameworks = new Dictionary<string, string[]>
            {
                ["BepInEx"] = new[] { "MelonLoader" },
                ["MelonLoader"] = new[] { "BepInEx" }
            };

            foreach (var framework in modStructure.DetectedFrameworks)
            {
                if (incompatibleFrameworks.TryGetValue(framework, out var incompatible))
                {
                    foreach (var incompatibleFramework in incompatible)
                    {
                        if (installedFrameworks.Contains(incompatibleFramework))
                        {
                            var conflict = new AdvancedModConflict
                            {
                                Type = InstallationConflictType.FrameworkConflict,
                                Description = $"Mod uses {framework} which conflicts with installed {incompatibleFramework}",
                                Severity = InstallationConflictSeverity.High,
                                ResolutionOptions = new List<string> { "Remove conflicting framework", "Skip mod installation" }
                            };
                            result.Conflicts.Add(conflict);
                        }
                    }
                }
            }
        }

        private void DetectPakFileConflicts(AdvancedModStructure modStructure, ConflictDetectionResult result)
        {
            var contentPaksPath = Path.Combine(_palRoot, "Pal", "Content", "Paks");
            if (!Directory.Exists(contentPaksPath)) return;

            var existingPaks = Directory.GetFiles(contentPaksPath, "*.pak").Select(Path.GetFileName).ToHashSet(StringComparer.OrdinalIgnoreCase);

            foreach (var pakFile in modStructure.PakFiles)
            {
                var pakName = Path.GetFileName(pakFile.Path);
                if (existingPaks.Contains(pakName))
                {
                    var conflict = new AdvancedModConflict
                    {
                        Type = InstallationConflictType.PakFileConflict,
                        Description = $"PAK file conflicts with existing: {pakName}",
                        AffectedFile = pakName,
                        Severity = pakName.Equals("Pal-Windows.pak", StringComparison.OrdinalIgnoreCase) ?
                                  InstallationConflictSeverity.Critical : InstallationConflictSeverity.Medium,
                        ResolutionOptions = new List<string> { "Backup and replace", "Rename new PAK", "Skip PAK file" }
                    };
                    result.Conflicts.Add(conflict);
                }
            }
        }

        private void DetectUE4SSModConflicts(AdvancedModStructure modStructure, ConflictDetectionResult result)
        {
            var currentUE4SS = _detector.DetectUE4SS();
            if (currentUE4SS.Status == UE4SSInstallStatus.NotInstalled) return;

            foreach (var ue4ssMod in modStructure.UE4SSMods)
            {
                if (currentUE4SS.UserMods.Contains(ue4ssMod.Name, StringComparer.OrdinalIgnoreCase))
                {
                    var conflict = new AdvancedModConflict
                    {
                        Type = InstallationConflictType.ModNameConflict,
                        Description = $"UE4SS mod name conflicts with existing: {ue4ssMod.Name}",
                        AffectedFile = ue4ssMod.Name,
                        Severity = InstallationConflictSeverity.Medium,
                        ResolutionOptions = new List<string> { "Replace existing mod", "Rename new mod", "Skip mod" }
                    };
                    result.Conflicts.Add(conflict);
                }
            }
        }

        private void DetectPalSchemaConflicts(AdvancedModStructure modStructure, ConflictDetectionResult result)
        {
            var currentPalSchema = _detector.DetectPalSchema();
            if (!currentPalSchema.IsInstalled) return;

            foreach (var palSchemaMod in modStructure.PalSchemaMods)
            {
                var existingMod = currentPalSchema.Mods.FirstOrDefault(m => 
                    m.Name.Equals(palSchemaMod.Name, StringComparison.OrdinalIgnoreCase));

                if (existingMod != null)
                {
                    var conflict = new AdvancedModConflict
                    {
                        Type = InstallationConflictType.ModNameConflict,
                        Description = $"PalSchema mod conflicts with existing: {palSchemaMod.Name}",
                        AffectedFile = palSchemaMod.Name,
                        Severity = InstallationConflictSeverity.Medium,
                        ResolutionOptions = new List<string> { "Replace existing mod", "Merge configurations", "Skip mod" }
                    };
                    result.Conflicts.Add(conflict);
                }
            }
        }

        private InstallationConflictSeverity DetermineFileSeverity(string filePath)
        {
            var fileName = Path.GetFileName(filePath).ToLowerInvariant();
            var extension = Path.GetExtension(filePath).ToLowerInvariant();

            // Critical system files
            if (fileName == "dwmapi.dll" || fileName == "ue4ss.dll" || fileName == "pal-windows.pak")
                return InstallationConflictSeverity.Critical;

            // Important configuration files
            if (fileName.Contains("settings") || fileName.Contains("config") || extension == ".ini")
                return InstallationConflictSeverity.High;

            // Mod files
            if (extension == ".pak" || extension == ".lua")
                return InstallationConflictSeverity.Medium;

            // Other files
            return InstallationConflictSeverity.Low;
        }

        private void AnalyzeConflictSeverity(ConflictDetectionResult result)
        {
            if (result.Conflicts.Any(c => c.Severity == InstallationConflictSeverity.Critical))
                result.OverallSeverity = InstallationConflictSeverity.Critical;
            else if (result.Conflicts.Any(c => c.Severity == InstallationConflictSeverity.High))
                result.OverallSeverity = InstallationConflictSeverity.High;
            else if (result.Conflicts.Any(c => c.Severity == InstallationConflictSeverity.Medium))
                result.OverallSeverity = InstallationConflictSeverity.Medium;
            else if (result.Conflicts.Any())
                result.OverallSeverity = InstallationConflictSeverity.Low;
            else
                result.OverallSeverity = InstallationConflictSeverity.None;

            result.IsInstallationSafe = result.OverallSeverity <= InstallationConflictSeverity.Medium;
            result.RequiresUserIntervention = result.OverallSeverity >= InstallationConflictSeverity.Medium;
        }

        private bool IsModInstalled(string modName)
        {
            // Check UE4SS mods
            var ue4ssStatus = _detector.DetectUE4SS();
            if (ue4ssStatus.UserMods.Contains(modName, StringComparer.OrdinalIgnoreCase))
                return true;

            // Check PalSchema mods
            var palSchemaStatus = _detector.DetectPalSchema();
            if (palSchemaStatus.Mods.Any(m => m.Name.Equals(modName, StringComparison.OrdinalIgnoreCase)))
                return true;

            return false;
        }

        private List<string> GetInstalledFrameworks()
        {
            var frameworks = new List<string>();
            
            // Check for UE4SS
            var ue4ssStatus = _detector.DetectUE4SS();
            if (ue4ssStatus.Status != UE4SSInstallStatus.NotInstalled)
                frameworks.Add("UE4SS");

            // Check for PalSchema
            var palSchemaStatus = _detector.DetectPalSchema();
            if (palSchemaStatus.IsInstalled)
                frameworks.Add("PalSchema");

            // Check for other frameworks (BepInEx, MelonLoader, etc.)
            var bepInExPath = Path.Combine(_palRoot, "BepInEx");
            if (Directory.Exists(bepInExPath))
                frameworks.Add("BepInEx");

            var melonLoaderPath = Path.Combine(_palRoot, "MelonLoader");
            if (Directory.Exists(melonLoaderPath))
                frameworks.Add("MelonLoader");

            return frameworks;
        }
    }
}
