using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using NUnit.Framework;
using FluentAssertions;
using ModInstallerApp.Services;
using ModInstallerApp.Models;

namespace ModInstallerApp.Tests.Services
{
    /// <summary>
    /// Comprehensive unit tests for AppDataManager service
    /// </summary>
    [TestFixture]
    [Category(TestCategories.Unit)]
    public class AppDataManagerTests : TestBase
    {
        private AppDataManager? _appDataManager;
        private string _testAppDataPath = string.Empty;

        [SetUp]
        public override async Task SetUp()
        {
            await base.SetUp();

            _testAppDataPath = Path.Combine(TestDataDirectory, "test-appdata");
            Directory.CreateDirectory(_testAppDataPath);
            _appDataManager = AppDataManager.Instance;
        }

        [TearDown]
        public override async Task TearDown()
        {
            // Don't dispose singleton AppDataManager here as it affects other tests
            await base.TearDown();
        }

        [Test]
        public void Settings_InitialCall_ReturnsDefaultSettings()
        {
            // Act
            var settings = _appDataManager!.Settings;

            // Assert
            settings.Should().NotBeNull();
            settings.RecentInstallations.Should().BeEmpty();
        }

        [Test]
        public void SaveSettings_PersistsSettings()
        {
            // Arrange
            _appDataManager!.Settings.LastPalworldPath = "C:\\Test\\Palworld1";

            // Act
            _appDataManager.SaveSettings();

            // Assert
            var retrievedPath = _appDataManager.Settings.LastPalworldPath;
            retrievedPath.Should().Be("C:\\Test\\Palworld1");
        }

        [Test]
        public void AddRecentInstallation_WithNewPath_AddsToRecentList()
        {
            // Arrange
            var newPath = Path.Combine(TestDataDirectory, "NewPalworld", "Palworld.exe");
            Directory.CreateDirectory(Path.GetDirectoryName(newPath)!);
            File.WriteAllText(newPath, "test executable");

            // Act
            _appDataManager!.AddRecentInstallation(newPath);

            // Assert
            var settings = _appDataManager.Settings;
            settings.RecentInstallations.Should().Contain(i => i.Path == newPath);
        }

        [Test]
        public void AddRecentInstallation_WithExistingPath_MovesToTop()
        {
            // Arrange
            var existingPath = Path.Combine(TestDataDirectory, "ExistingPalworld", "Palworld.exe");
            var newPath = Path.Combine(TestDataDirectory, "NewPalworld2", "Palworld.exe");

            Directory.CreateDirectory(Path.GetDirectoryName(existingPath)!);
            Directory.CreateDirectory(Path.GetDirectoryName(newPath)!);
            File.WriteAllText(existingPath, "test executable");
            File.WriteAllText(newPath, "test executable");

            _appDataManager!.AddRecentInstallation(existingPath);
            _appDataManager.AddRecentInstallation(newPath);

            // Act
            _appDataManager.AddRecentInstallation(existingPath);

            // Assert
            var settings = _appDataManager.Settings;
            settings.RecentInstallations.First().Path.Should().Be(existingPath);
            settings.RecentInstallations.Should().HaveCount(2);
        }

        [Test]
        public void AddRecentInstallation_ExceedsMaxCount_RemovesOldest()
        {
            // Arrange
            const int maxRecentInstallations = 10; // Assuming this is the limit

            // Add more than the maximum
            for (int i = 0; i < maxRecentInstallations + 2; i++)
            {
                var testPath = Path.Combine(TestDataDirectory, $"Palworld{i}", "Palworld.exe");
                Directory.CreateDirectory(Path.GetDirectoryName(testPath)!);
                File.WriteAllText(testPath, "test executable");
                _appDataManager!.AddRecentInstallation(testPath);
            }

            // Assert
            var settings = _appDataManager!.Settings;
            settings.RecentInstallations.Should().HaveCount(maxRecentInstallations);
            var firstPath = Path.Combine(TestDataDirectory, "Palworld0", "Palworld.exe");
            var secondPath = Path.Combine(TestDataDirectory, "Palworld1", "Palworld.exe");
            settings.RecentInstallations.Should().NotContain(i => i.Path == firstPath);
            settings.RecentInstallations.Should().NotContain(i => i.Path == secondPath);
        }

        [Test]
        public void AutoDetectPalworldInstallations_FindsValidInstallations()
        {
            // Arrange
            var testInstallPath = Path.Combine(TestDataDirectory, "TestPalworld");
            var binariesPath = Path.Combine(testInstallPath, "Pal", "Binaries", "Win64");
            Directory.CreateDirectory(binariesPath);
            File.WriteAllText(Path.Combine(binariesPath, "Palworld-Win64-Shipping.exe"), "test");

            // Act
            var detectedInstallations = _appDataManager!.AutoDetectPalworldInstallations();

            // Assert
            // Note: This test may not find the test installation since AutoDetect typically
            // searches standard Steam/Epic locations. The test verifies the method doesn't crash.
            detectedInstallations.Should().NotBeNull();
        }

        [Test]
        public void RemoveRecentInstallation_WithExistingPath_RemovesFromList()
        {
            // Arrange
            var pathToRemove = Path.Combine(TestDataDirectory, "ToRemove", "Palworld.exe");
            var pathToKeep = Path.Combine(TestDataDirectory, "ToKeep", "Palworld.exe");

            Directory.CreateDirectory(Path.GetDirectoryName(pathToRemove)!);
            Directory.CreateDirectory(Path.GetDirectoryName(pathToKeep)!);
            File.WriteAllText(pathToRemove, "test executable");
            File.WriteAllText(pathToKeep, "test executable");

            _appDataManager!.AddRecentInstallation(pathToRemove);
            _appDataManager.AddRecentInstallation(pathToKeep);

            // Act
            _appDataManager.Settings.RecentInstallations.RemoveAll(i => i.Path == pathToRemove);

            // Assert
            var settings = _appDataManager.Settings;
            settings.RecentInstallations.Should().NotContain(i => i.Path == pathToRemove);
            settings.RecentInstallations.Should().Contain(i => i.Path == pathToKeep);
        }

        [Test]
        public void SaveSettings_CallsSuccessfully()
        {
            // Act & Assert - SaveSettings() no longer takes parameters
            _appDataManager!.SaveSettings();
            // Test passes if no exception is thrown
        }

        [Test]
        public void AddRecentInstallation_WithNullPath_DoesNotThrow()
        {
            // Act & Assert - Method silently returns for invalid paths
            _appDataManager!.AddRecentInstallation(null!);
            // Test passes if no exception is thrown
        }

        [Test]
        public void AddRecentInstallation_WithEmptyPath_DoesNotThrow()
        {
            // Act & Assert - Method silently returns for invalid paths
            _appDataManager!.AddRecentInstallation("");
            _appDataManager!.AddRecentInstallation("   ");
            // Test passes if no exception is thrown
        }

        [Test]
        public void AppDataManager_IsSingleton()
        {
            // Act
            var instance1 = AppDataManager.Instance;
            var instance2 = AppDataManager.Instance;

            // Assert
            instance1.Should().BeSameAs(instance2);
        }

        [Test]
        public void SettingsPersistence_AcrossInstances_MaintainsData()
        {
            // Arrange
            var persistentPath = Path.Combine(TestDataDirectory, "Persistent", "Palworld.exe");
            Directory.CreateDirectory(Path.GetDirectoryName(persistentPath)!);
            File.WriteAllText(persistentPath, "test executable");

            _appDataManager!.AddRecentInstallation(persistentPath);
            _appDataManager.Settings.LastPalworldPath = persistentPath;

            // Act
            _appDataManager.SaveSettings();

            // Assert
            var retrievedSettings = _appDataManager.Settings;
            retrievedSettings.RecentInstallations.Should().Contain(i => i.Path == persistentPath);
            retrievedSettings.LastPalworldPath.Should().Be(persistentPath);
        }

        [Test]
        public void Dispose_DisposesResourcesProperly()
        {
            // Arrange
            var disposePath = Path.Combine(TestDataDirectory, "Dispose", "Palworld.exe");
            Directory.CreateDirectory(Path.GetDirectoryName(disposePath)!);
            File.WriteAllText(disposePath, "test executable");

            _appDataManager!.AddRecentInstallation(disposePath);

            // Act
            _appDataManager.Dispose();

            // Assert
            // Test passes if no exception is thrown during disposal
            // Note: Singleton pattern means we can't test ObjectDisposedException easily
        }
    }
}
