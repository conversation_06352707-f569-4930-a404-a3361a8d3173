using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using NUnit.Framework;
using FluentAssertions;
using Moq;
using ModInstallerApp.Services;
using ModInstallerApp.Models;
using ModInstallerApp.Tests.Mocks;

namespace ModInstallerApp.Tests.Services
{
    /// <summary>
    /// Comprehensive unit tests for ModManagerService
    /// </summary>
    [TestFixture]
    [Category(TestCategories.Unit)]
    public class ModManagerServiceTests : TestBase
    {
        private ModManagerService? _modManager;
        private Mock<UE4SSDetector>? _mockDetector;
        private Mock<EnhancedInstallationEngine>? _mockInstallationEngine;

        [SetUp]
        public override async Task SetUp()
        {
            await base.SetUp();
            
            _mockDetector = MockServices.CreateMockUE4SSDetector();
            _mockInstallationEngine = MockServices.CreateMockInstallationEngine();
            
            _modManager = new ModManagerService(
                TestPalworldRoot,
                _mockInstallationEngine.Object,
                TestLogger!);
        }

        [TearDown]
        public override async Task TearDown()
        {
            _modManager?.Dispose();
            await base.TearDown();
        }

        [Test]
        public async Task LoadModsAsync_WithExistingInstallations_LoadsMods()
        {
            // Arrange
            var testInstallations = new List<InstallationRecord>
            {
                MockServices.TestData.CreateTestInstallation("TestMod1"),
                MockServices.TestData.CreateTestInstallation("TestMod2")
            };
            
            _mockInstallationEngine!.Setup(x => x.GetAllInstallationsAsync())
                .ReturnsAsync(testInstallations);

            // Act
            await _modManager!.LoadModsAsync();

            // Assert
            _modManager.Mods.Should().HaveCount(2);
            _modManager.Mods.Should().Contain(m => m.Name == "TestMod1");
            _modManager.Mods.Should().Contain(m => m.Name == "TestMod2");
        }

        [Test]
        public async Task SetModEnabledAsync_WithValidModId_EnablesMod()
        {
            // Arrange
            await SetupTestModsAsync();
            var modId = _modManager!.Mods.First().Id;

            // Act
            var result = await _modManager.SetModEnabledAsync(modId, true);

            // Assert
            result.Should().BeTrue();
            var mod = _modManager.Mods.First(m => m.Id == modId);
            mod.IsEnabled.Should().BeTrue();
        }

        [Test]
        public async Task SetModEnabledAsync_WithValidModId_DisablesMod()
        {
            // Arrange
            await SetupTestModsAsync();
            var modId = _modManager!.Mods.First().Id;
            await _modManager.SetModEnabledAsync(modId, true);

            // Act
            var result = await _modManager.SetModEnabledAsync(modId, false);

            // Assert
            result.Should().BeTrue();
            var mod = _modManager.Mods.First(m => m.Id == modId);
            mod.IsEnabled.Should().BeFalse();
        }

        [Test]
        public async Task SetModEnabledAsync_WithInvalidModId_ReturnsFalse()
        {
            // Arrange
            await SetupTestModsAsync();

            // Act
            var result = await _modManager!.SetModEnabledAsync("invalid-mod-id", true);

            // Assert
            result.Should().BeFalse();
        }

        [Test]
        public async Task FilterMods_WithCategoryFilter_ReturnsFilteredMods()
        {
            // Arrange
            await SetupTestModsAsync();
            var filter = new ModFilter
            {
                Categories = new List<ModCategory> { ModCategory.Gameplay }
            };

            // Act
            var gameplayMods = _modManager!.FilterMods(filter);

            // Assert
            gameplayMods.Should().NotBeEmpty();
            gameplayMods.Should().OnlyContain(m => m.Category == ModCategory.Gameplay);
        }

        [Test]
        public async Task FilterMods_WithSearchText_ReturnsMatchingMods()
        {
            // Arrange
            await SetupTestModsAsync();
            var filter = new ModFilter
            {
                SearchText = "Test"
            };

            // Act
            var results = _modManager!.FilterMods(filter);

            // Assert
            results.Should().NotBeEmpty();
            results.Should().OnlyContain(m => m.Name.Contains("Test") || m.Description.Contains("Test"));
        }

        [Test]
        public void Constructor_WithNullInstallationEngine_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                new ModManagerService(TestPalworldRoot, null!, TestLogger!));
        }

        [Test]
        public void Constructor_WithInvalidPalworldRoot_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => 
                new ModManagerService("", _mockInstallationEngine!.Object, TestLogger!));
        }

        private async Task SetupTestModsAsync()
        {
            var testInstallations = new List<InstallationRecord>
            {
                MockServices.TestData.CreateTestInstallation("TestGameplayMod"),
                MockServices.TestData.CreateTestInstallation("TestUtilityMod")
            };
            
            _mockInstallationEngine!.Setup(x => x.GetAllInstallationsAsync())
                .ReturnsAsync(testInstallations);

            await _modManager!.LoadModsAsync();
        }
    }
}
