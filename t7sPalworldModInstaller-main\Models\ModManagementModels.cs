using System;
using System.Collections.Generic;
using System.Drawing;
using System.ComponentModel;
using System.Linq;

namespace ModInstallerApp.Models
{
    // ── MOD MANAGEMENT MODELS ──

    public class ModItem : INotifyPropertyChanged
    {
        private bool _isEnabled = true;
        private ModState _state = ModState.Installed;
        private int _loadOrder = 0;

        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = "";
        public string DisplayName { get; set; } = "";
        public string Description { get; set; } = "";
        public string Version { get; set; } = "1.0.0";
        public string Author { get; set; } = "Unknown";
        public DateTime InstallDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;
        public long Size { get; set; } = 0;
        public ModType ModType { get; set; } = ModType.Unknown;
        public ModCategory Category { get; set; } = ModCategory.Gameplay;
        public List<string> Tags { get; set; } = new();
        public string ThumbnailPath { get; set; } = "";
        public Image? Thumbnail { get; set; }
        public List<ModDependency> Dependencies { get; set; } = new();
        public List<ModConflict> Conflicts { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
        public string InstallationPath { get; set; } = "";
        public string BackupPath { get; set; } = "";
        public bool CanUninstall { get; set; } = true;
        public bool CanRollback { get; set; } = false;

        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                if (_isEnabled != value)
                {
                    _isEnabled = value;
                    OnPropertyChanged(nameof(IsEnabled));
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        public ModState State
        {
            get => _state;
            set
            {
                if (_state != value)
                {
                    _state = value;
                    OnPropertyChanged(nameof(State));
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        public int LoadOrder
        {
            get => _loadOrder;
            set
            {
                if (_loadOrder != value)
                {
                    _loadOrder = value;
                    OnPropertyChanged(nameof(LoadOrder));
                }
            }
        }

        public string StatusText => IsEnabled ? State.ToString() : "Disabled";
        public string FormattedSize => FormatBytes(Size);
        public bool HasConflicts => Conflicts.Count > 0;
        public bool HasDependencies => Dependencies.Count > 0;

        private static string FormatBytes(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024.0 * 1024):F1} MB";
            return $"{bytes / (1024.0 * 1024 * 1024):F1} GB";
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class ModProfile
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime Created { get; set; } = DateTime.Now;
        public DateTime CreatedDate { get; set; } = DateTime.Now; // Alias for compatibility
        public DateTime LastModified { get; set; } = DateTime.Now;
        public List<ModProfileEntry> Mods { get; set; } = new();
        public Dictionary<string, object> Settings { get; set; } = new();
        public bool IsActive { get; set; } = false;

        // Computed property for enabled mods count
        public List<ModProfileEntry> EnabledMods => Mods.Where(m => m.IsEnabled).ToList();
    }

    public class ModProfileEntry
    {
        public string ModId { get; set; } = "";
        public bool IsEnabled { get; set; } = true;
        public int LoadOrder { get; set; } = 0;
        public Dictionary<string, object> ModSettings { get; set; } = new();
    }

    public class ModCollection
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Author { get; set; } = "";
        public DateTime Created { get; set; } = DateTime.Now;
        public List<string> ModIds { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public string Version { get; set; } = "1.0.0";
        public bool IsPublic { get; set; } = false;
    }

    public class ModFilter
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = "";
        public string SearchText { get; set; } = "";
        public string? NameContains { get; set; }
        public string? Author { get; set; }
        public string? ModType { get; set; }
        public string? Status { get; set; }
        public bool? HasConflicts { get; set; }
        public bool? HasUpdates { get; set; }
        public List<ModCategory> Categories { get; set; } = new();
        public List<ModType> ModTypes { get; set; } = new();
        public List<ModState> States { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public DateTime? InstalledAfter { get; set; }
        public DateTime? InstalledBefore { get; set; }
        public long? MinSize { get; set; }
        public long? MaxSize { get; set; }
        public ModSortCriteria SortBy { get; set; } = ModSortCriteria.Name;
        public bool SortDescending { get; set; } = false;
        public bool ShowEnabledOnly { get; set; } = false;
        public bool ShowConflictsOnly { get; set; } = false;
    }

    public class ModDependency
    {
        public string ModId { get; set; } = "";
        public string ModName { get; set; } = "";
        public string RequiredVersion { get; set; } = "";
        public bool IsOptional { get; set; } = false;
        public bool IsSatisfied { get; set; } = false;
        public DependencyType Type { get; set; } = DependencyType.Hard;
    }

    public class ModConflict
    {
        public string ConflictingModId { get; set; } = "";
        public string ConflictingModName { get; set; } = "";
        public ConflictType Type { get; set; } = ConflictType.FileOverwrite;
        public string Description { get; set; } = "";
        public List<string> ConflictingFiles { get; set; } = new();
        public ConflictSeverity Severity { get; set; } = ConflictSeverity.Medium;
        public bool CanAutoResolve { get; set; } = false;
    }

    public class LoadOrderValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public List<ModDependency> UnresolvedDependencies { get; set; } = new();
        public List<ModConflict> Conflicts { get; set; } = new();
    }

    // ── ENUMERATIONS ──

    public enum DependencyType
    {
        Hard,      // Must be present and enabled
        Soft,      // Should be present but not required
        Optional,  // Nice to have
        LoadOrder  // Must be loaded before/after
    }

    public enum ConflictType
    {
        FileOverwrite,    // Same file modified by multiple mods
        LoadOrder,        // Incompatible load order requirements
        Incompatible,     // Mods that cannot work together
        Performance,      // Mods that may cause performance issues together
        Functionality     // Mods that duplicate or interfere with functionality
    }

    public enum ConflictSeverity
    {
        Low,      // Minor issues, mostly cosmetic
        Medium,   // May cause problems but not game-breaking
        High,     // Likely to cause crashes or major issues
        Critical  // Will definitely cause problems
    }

    public enum ModState
    {
        Installed,
        Disabled,
        Conflicted,
        MissingDependencies,
        UpdateAvailable,
        Corrupted,
        Loading,
        Error
    }

    public enum ModCategory
    {
        Gameplay,
        Graphics,
        Audio,
        UI,
        Performance,
        Utility,
        Content,
        Overhaul,
        Cosmetic,
        Experimental,
        Other
    }

    public enum ModSortCriteria
    {
        Name,
        InstallDate,
        LastModified,
        Size,
        Author,
        Category,
        LoadOrder,
        State
    }

    // ── GRID DISPLAY MODELS ──

    public class ModGridSettings
    {
        public int TileSize { get; set; } = 200;
        public int TileSpacing { get; set; } = 10;
        public bool ShowThumbnails { get; set; } = true;
        public bool ShowMetadata { get; set; } = true;
        public bool ShowStatus { get; set; } = true;
        public bool ShowLoadOrder { get; set; } = false;
        public ModGridViewMode ViewMode { get; set; } = ModGridViewMode.Grid;
        public int ColumnsPerRow { get; set; } = 0; // 0 = auto-calculate
    }

    public enum ModGridViewMode
    {
        Grid,
        List,
        Compact
    }

    public class ModGridItem
    {
        public ModItem Mod { get; set; } = new();
        public Rectangle Bounds { get; set; }
        public bool IsSelected { get; set; }
        public bool IsHovered { get; set; }
        public bool IsDragging { get; set; }
        public Point DragOffset { get; set; }
    }
}
