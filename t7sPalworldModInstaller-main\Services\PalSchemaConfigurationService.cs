using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    /// <summary>
    /// Service for managing PalSchema configurations, profiles, and validation
    /// </summary>
    public class PalSchemaConfigurationService : IDisposable
    {
        private readonly string _palRoot;
        private readonly UE4SSDetector _detector;
        private readonly EnhancedLogger _logger;
        private readonly CacheManager _cache;
        private readonly string _configurationPath;
        private readonly string _profilesPath;
        private readonly string _templatesPath;
        private readonly string _backupsPath;
        private bool _disposed = false;

        private readonly List<PalSchemaConfig> _configurations = new();
        private readonly List<PalSchemaProfile> _profiles = new();
        private readonly List<PalSchemaTemplate> _templates = new();

        public event EventHandler<PalSchemaConfig>? ConfigurationChanged;
        public event EventHandler<PalSchemaProfile>? ProfileChanged;
        public event EventHandler<PalSchemaValidationResult>? ValidationCompleted;

        public PalSchemaConfigurationService(string palRoot, UE4SSDetector detector, EnhancedLogger logger, CacheManager cache)
        {
            _palRoot = palRoot ?? throw new ArgumentNullException(nameof(palRoot));
            _detector = detector ?? throw new ArgumentNullException(nameof(detector));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));

            // Set up configuration paths
            var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "PalworldModInstaller");
            _configurationPath = Path.Combine(appDataPath, "PalSchemaConfigurations");
            _profilesPath = Path.Combine(appDataPath, "PalSchemaProfiles");
            _templatesPath = Path.Combine(appDataPath, "PalSchemaTemplates");
            _backupsPath = Path.Combine(appDataPath, "PalSchemaBackups");

            // Ensure directories exist
            Directory.CreateDirectory(_configurationPath);
            Directory.CreateDirectory(_profilesPath);
            Directory.CreateDirectory(_templatesPath);
            Directory.CreateDirectory(_backupsPath);

            // Load built-in templates
            InitializeBuiltInTemplates();
        }

        /// <summary>
        /// Loads all configurations from disk
        /// </summary>
        public async Task LoadConfigurationsAsync()
        {
            try
            {
                _configurations.Clear();
                var configFiles = Directory.GetFiles(_configurationPath, "*.json");

                foreach (var file in configFiles)
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(file);
                        var config = JsonSerializer.Deserialize<PalSchemaConfig>(json);
                        if (config != null)
                        {
                            _configurations.Add(config);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Failed to load configuration from {file}", "PalSchemaConfig", ex);
                    }
                }

                _logger.LogInfo($"Loaded {_configurations.Count} PalSchema configurations", "PalSchemaConfig");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to load PalSchema configurations", "PalSchemaConfig", ex);
                throw;
            }
        }

        /// <summary>
        /// Loads all profiles from disk
        /// </summary>
        public async Task LoadProfilesAsync()
        {
            try
            {
                _profiles.Clear();
                var profileFiles = Directory.GetFiles(_profilesPath, "*.json");

                foreach (var file in profileFiles)
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(file);
                        var profile = JsonSerializer.Deserialize<PalSchemaProfile>(json);
                        if (profile != null)
                        {
                            _profiles.Add(profile);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Failed to load profile from {file}", "PalSchemaConfig", ex);
                    }
                }

                _logger.LogInfo($"Loaded {_profiles.Count} PalSchema profiles", "PalSchemaConfig");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to load PalSchema profiles", "PalSchemaConfig", ex);
                throw;
            }
        }

        /// <summary>
        /// Saves a configuration to disk
        /// </summary>
        public async Task SaveConfigurationAsync(PalSchemaConfig configuration)
        {
            ThrowIfDisposed();
            
            try
            {
                configuration.ModifiedDate = DateTime.Now;
                
                var fileName = $"{SanitizeFileName(configuration.Name)}_{configuration.Id}.json";
                var filePath = Path.Combine(_configurationPath, fileName);
                
                var options = new JsonSerializerOptions { WriteIndented = true };
                var json = JsonSerializer.Serialize(configuration, options);
                
                await File.WriteAllTextAsync(filePath, json);
                
                // Update in-memory collection
                var existing = _configurations.FirstOrDefault(c => c.Id == configuration.Id);
                if (existing != null)
                {
                    var index = _configurations.IndexOf(existing);
                    _configurations[index] = configuration;
                }
                else
                {
                    _configurations.Add(configuration);
                }

                ConfigurationChanged?.Invoke(this, configuration);
                _logger.LogInfo($"Saved PalSchema configuration: {configuration.Name}", "PalSchemaConfig");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to save configuration: {configuration.Name}", "PalSchemaConfig", ex);
                throw;
            }
        }

        /// <summary>
        /// Saves a profile to disk
        /// </summary>
        public async Task SaveProfileAsync(PalSchemaProfile profile)
        {
            ThrowIfDisposed();
            
            try
            {
                profile.ModifiedDate = DateTime.Now;
                
                var fileName = $"{SanitizeFileName(profile.Name)}_{profile.Id}.json";
                var filePath = Path.Combine(_profilesPath, fileName);
                
                var options = new JsonSerializerOptions { WriteIndented = true };
                var json = JsonSerializer.Serialize(profile, options);
                
                await File.WriteAllTextAsync(filePath, json);
                
                // Update in-memory collection
                var existing = _profiles.FirstOrDefault(p => p.Id == profile.Id);
                if (existing != null)
                {
                    var index = _profiles.IndexOf(existing);
                    _profiles[index] = profile;
                }
                else
                {
                    _profiles.Add(profile);
                }

                ProfileChanged?.Invoke(this, profile);
                _logger.LogInfo($"Saved PalSchema profile: {profile.Name}", "PalSchemaConfig");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to save profile: {profile.Name}", "PalSchemaConfig", ex);
                throw;
            }
        }

        /// <summary>
        /// Validates a PalSchema configuration
        /// </summary>
        public async Task<List<PalSchemaValidationResult>> ValidateConfigurationAsync(PalSchemaConfig configuration)
        {
            ThrowIfDisposed();
            
            var results = new List<PalSchemaValidationResult>();
            
            try
            {
                // Basic validation
                if (string.IsNullOrWhiteSpace(configuration.Name))
                {
                    results.Add(new PalSchemaValidationResult
                    {
                        PropertyPath = "Name",
                        Severity = PalSchemaValidationSeverity.Error,
                        Message = "Configuration name is required",
                        SuggestedFix = "Provide a descriptive name for the configuration"
                    });
                }

                // Validate configuration structure
                await ValidateConfigurationStructure(configuration, results);
                
                // Validate against PalSchema requirements
                await ValidatePalSchemaCompatibility(configuration, results);
                
                // Performance analysis
                await AnalyzePerformanceImpact(configuration, results);

                configuration.ValidationResults = results;
                ValidationCompleted?.Invoke(this, results.FirstOrDefault() ?? new PalSchemaValidationResult());
                
                _logger.LogInfo($"Validated configuration: {configuration.Name} ({results.Count} issues found)", "PalSchemaConfig");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to validate configuration: {configuration.Name}", "PalSchemaConfig", ex);
                results.Add(new PalSchemaValidationResult
                {
                    PropertyPath = "General",
                    Severity = PalSchemaValidationSeverity.Critical,
                    Message = $"Validation failed: {ex.Message}"
                });
            }
            
            return results;
        }

        /// <summary>
        /// Gets all available configurations
        /// </summary>
        public IReadOnlyList<PalSchemaConfig> GetConfigurations()
        {
            ThrowIfDisposed();
            return _configurations.AsReadOnly();
        }

        /// <summary>
        /// Gets all available profiles
        /// </summary>
        public IReadOnlyList<PalSchemaProfile> GetProfiles()
        {
            ThrowIfDisposed();
            return _profiles.AsReadOnly();
        }

        /// <summary>
        /// Gets all available templates
        /// </summary>
        public IReadOnlyList<PalSchemaTemplate> GetTemplates()
        {
            ThrowIfDisposed();
            return _templates.AsReadOnly();
        }

        /// <summary>
        /// Creates a configuration from a template
        /// </summary>
        public PalSchemaConfig CreateFromTemplate(PalSchemaTemplate template, Dictionary<string, object>? parameterValues = null)
        {
            ThrowIfDisposed();
            
            var config = new PalSchemaConfig
            {
                Name = $"{template.Name} Configuration",
                Description = $"Configuration based on {template.Name} template",
                ConfigType = PalSchemaConfigType.Template,
                Configuration = new Dictionary<string, object>(template.DefaultConfiguration)
            };

            // Apply parameter values if provided
            if (parameterValues != null)
            {
                foreach (var parameter in template.Parameters)
                {
                    if (parameterValues.TryGetValue(parameter.Name, out var value))
                    {
                        // Apply the parameter value to the configuration
                        ApplyParameterValue(config.Configuration, parameter.Name, value);
                    }
                }
            }

            return config;
        }

        private void InitializeBuiltInTemplates()
        {
            // Add some built-in templates
            _templates.Add(new PalSchemaTemplate
            {
                Name = "Basic Server Configuration",
                Description = "Basic PalSchema configuration for server environments",
                Category = "Server",
                DifficultyLevel = PalSchemaDifficultyLevel.Beginner,
                DefaultConfiguration = new Dictionary<string, object>
                {
                    ["ServerSettings"] = new Dictionary<string, object>
                    {
                        ["MaxPlayers"] = 32,
                        ["ServerName"] = "My Palworld Server",
                        ["PublicServer"] = false
                    }
                },
                Parameters = new List<PalSchemaTemplateParameter>
                {
                    new PalSchemaTemplateParameter
                    {
                        Name = "MaxPlayers",
                        DisplayName = "Maximum Players",
                        Description = "Maximum number of players allowed on the server",
                        ParameterType = PalSchemaParameterType.Integer,
                        DefaultValue = 32,
                        MinValue = 1,
                        MaxValue = 100,
                        IsRequired = true
                    }
                }
            });
        }

        private async Task ValidateConfigurationStructure(PalSchemaConfig configuration, List<PalSchemaValidationResult> results)
        {
            // Validate JSON structure
            try
            {
                var json = JsonSerializer.Serialize(configuration.Configuration);
                JsonDocument.Parse(json); // This will throw if invalid
            }
            catch (JsonException ex)
            {
                results.Add(new PalSchemaValidationResult
                {
                    PropertyPath = "Configuration",
                    Severity = PalSchemaValidationSeverity.Error,
                    Message = $"Invalid JSON structure: {ex.Message}",
                    SuggestedFix = "Fix JSON syntax errors"
                });
            }

            await Task.CompletedTask;
        }

        private async Task ValidatePalSchemaCompatibility(PalSchemaConfig configuration, List<PalSchemaValidationResult> results)
        {
            // Check if PalSchema is installed
            var palSchemaStatus = await _detector.DetectPalSchemaAsync();
            if (!palSchemaStatus.IsInstalled)
            {
                results.Add(new PalSchemaValidationResult
                {
                    PropertyPath = "General",
                    Severity = PalSchemaValidationSeverity.Warning,
                    Message = "PalSchema is not installed",
                    SuggestedFix = "Install PalSchema before applying this configuration"
                });
            }
        }

        private async Task AnalyzePerformanceImpact(PalSchemaConfig configuration, List<PalSchemaValidationResult> results)
        {
            // Simple performance analysis based on configuration complexity
            var complexity = CalculateConfigurationComplexity(configuration);
            
            if (complexity > 7)
            {
                results.Add(new PalSchemaValidationResult
                {
                    PropertyPath = "Performance",
                    Severity = PalSchemaValidationSeverity.Warning,
                    Message = "High complexity configuration may impact performance",
                    SuggestedFix = "Consider simplifying the configuration or testing performance impact"
                });
            }

            await Task.CompletedTask;
        }

        private int CalculateConfigurationComplexity(PalSchemaConfig configuration)
        {
            // Simple complexity calculation based on configuration size and nesting
            var json = JsonSerializer.Serialize(configuration.Configuration);
            var complexity = Math.Min(10, json.Length / 1000 + CountNestedObjects(configuration.Configuration));
            return complexity;
        }

        private int CountNestedObjects(Dictionary<string, object> config, int depth = 0)
        {
            if (depth > 5) return 0; // Prevent infinite recursion
            
            int count = 0;
            foreach (var value in config.Values)
            {
                if (value is Dictionary<string, object> nested)
                {
                    count += 1 + CountNestedObjects(nested, depth + 1);
                }
            }
            return count;
        }

        private void ApplyParameterValue(Dictionary<string, object> configuration, string parameterName, object value)
        {
            // Simple parameter application - in a real implementation, this would be more sophisticated
            configuration[parameterName] = value;
        }

        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(PalSchemaConfigurationService));
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
            }
        }
    }
}
