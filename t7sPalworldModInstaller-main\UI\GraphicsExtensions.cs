using System;
using System.Drawing;
using System.Drawing.Drawing2D;

namespace ModInstallerApp.UI
{
    public static class GraphicsExtensions
    {
        /// <summary>
        /// Fills a rounded rectangle with the specified brush
        /// </summary>
        public static void FillRoundedRectangle(this Graphics graphics, Brush brush, Rectangle rect, int cornerRadius)
        {
            if (graphics == null) throw new ArgumentNullException(nameof(graphics));
            if (brush == null) throw new ArgumentNullException(nameof(brush));

            using (var path = CreateRoundedRectanglePath(rect, cornerRadius))
            {
                graphics.FillPath(brush, path);
            }
        }

        /// <summary>
        /// Fills a rounded rectangle with the specified brush
        /// </summary>
        public static void FillRoundedRectangle(this Graphics graphics, Brush brush, RectangleF rect, float cornerRadius)
        {
            if (graphics == null) throw new ArgumentNullException(nameof(graphics));
            if (brush == null) throw new ArgumentNullException(nameof(brush));

            using (var path = CreateRoundedRectanglePath(rect, cornerRadius))
            {
                graphics.FillPath(brush, path);
            }
        }

        /// <summary>
        /// Draws the outline of a rounded rectangle with the specified pen
        /// </summary>
        public static void DrawRoundedRectangle(this Graphics graphics, Pen pen, Rectangle rect, int cornerRadius)
        {
            if (graphics == null) throw new ArgumentNullException(nameof(graphics));
            if (pen == null) throw new ArgumentNullException(nameof(pen));

            using (var path = CreateRoundedRectanglePath(rect, cornerRadius))
            {
                graphics.DrawPath(pen, path);
            }
        }

        /// <summary>
        /// Draws the outline of a rounded rectangle with the specified pen
        /// </summary>
        public static void DrawRoundedRectangle(this Graphics graphics, Pen pen, RectangleF rect, float cornerRadius)
        {
            if (graphics == null) throw new ArgumentNullException(nameof(graphics));
            if (pen == null) throw new ArgumentNullException(nameof(pen));

            using (var path = CreateRoundedRectanglePath(rect, cornerRadius))
            {
                graphics.DrawPath(pen, path);
            }
        }

        /// <summary>
        /// Creates a GraphicsPath for a rounded rectangle
        /// </summary>
        private static GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int cornerRadius)
        {
            return CreateRoundedRectanglePath(new RectangleF(rect.X, rect.Y, rect.Width, rect.Height), cornerRadius);
        }

        /// <summary>
        /// Creates a GraphicsPath for a rounded rectangle
        /// </summary>
        private static GraphicsPath CreateRoundedRectanglePath(RectangleF rect, float cornerRadius)
        {
            var path = new GraphicsPath();
            
            if (cornerRadius <= 0)
            {
                path.AddRectangle(rect);
                return path;
            }

            // Ensure corner radius doesn't exceed half the width or height
            var actualRadius = Math.Min(cornerRadius, Math.Min(rect.Width / 2, rect.Height / 2));
            var diameter = actualRadius * 2;

            // Top-left corner
            path.AddArc(rect.X, rect.Y, diameter, diameter, 180, 90);
            
            // Top edge
            path.AddLine(rect.X + actualRadius, rect.Y, rect.Right - actualRadius, rect.Y);
            
            // Top-right corner
            path.AddArc(rect.Right - diameter, rect.Y, diameter, diameter, 270, 90);
            
            // Right edge
            path.AddLine(rect.Right, rect.Y + actualRadius, rect.Right, rect.Bottom - actualRadius);
            
            // Bottom-right corner
            path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90);
            
            // Bottom edge
            path.AddLine(rect.Right - actualRadius, rect.Bottom, rect.X + actualRadius, rect.Bottom);
            
            // Bottom-left corner
            path.AddArc(rect.X, rect.Bottom - diameter, diameter, diameter, 90, 90);
            
            // Left edge
            path.AddLine(rect.X, rect.Bottom - actualRadius, rect.X, rect.Y + actualRadius);
            
            path.CloseFigure();
            return path;
        }
    }

    public static class ColorExtensions
    {
        /// <summary>
        /// Changes the alpha (transparency) of a color
        /// </summary>
        /// <param name="color">The original color</param>
        /// <param name="alpha">The new alpha value (0-255)</param>
        /// <returns>A new color with the specified alpha value</returns>
        public static Color ChangeAlpha(this Color color, int alpha)
        {
            alpha = Math.Max(0, Math.Min(255, alpha));
            return Color.FromArgb(alpha, color.R, color.G, color.B);
        }

        /// <summary>
        /// Changes the alpha (transparency) of a color using a percentage
        /// </summary>
        /// <param name="color">The original color</param>
        /// <param name="alphaPercent">The alpha percentage (0.0 to 1.0)</param>
        /// <returns>A new color with the specified alpha value</returns>
        public static Color ChangeAlpha(this Color color, float alphaPercent)
        {
            alphaPercent = Math.Max(0f, Math.Min(1f, alphaPercent));
            int alpha = (int)(alphaPercent * 255);
            return Color.FromArgb(alpha, color.R, color.G, color.B);
        }

        /// <summary>
        /// Lightens a color by the specified amount
        /// </summary>
        /// <param name="color">The original color</param>
        /// <param name="amount">The amount to lighten (0.0 to 1.0)</param>
        /// <returns>A lightened version of the color</returns>
        public static Color Lighten(this Color color, float amount)
        {
            amount = Math.Max(0f, Math.Min(1f, amount));
            
            int r = color.R + (int)((255 - color.R) * amount);
            int g = color.G + (int)((255 - color.G) * amount);
            int b = color.B + (int)((255 - color.B) * amount);
            
            r = Math.Max(0, Math.Min(255, r));
            g = Math.Max(0, Math.Min(255, g));
            b = Math.Max(0, Math.Min(255, b));
            
            return Color.FromArgb(color.A, r, g, b);
        }

        /// <summary>
        /// Darkens a color by the specified amount
        /// </summary>
        /// <param name="color">The original color</param>
        /// <param name="amount">The amount to darken (0.0 to 1.0)</param>
        /// <returns>A darkened version of the color</returns>
        public static Color Darken(this Color color, float amount)
        {
            amount = Math.Max(0f, Math.Min(1f, amount));
            
            int r = color.R - (int)(color.R * amount);
            int g = color.G - (int)(color.G * amount);
            int b = color.B - (int)(color.B * amount);
            
            r = Math.Max(0, Math.Min(255, r));
            g = Math.Max(0, Math.Min(255, g));
            b = Math.Max(0, Math.Min(255, b));
            
            return Color.FromArgb(color.A, r, g, b);
        }

        /// <summary>
        /// Blends two colors together
        /// </summary>
        /// <param name="color1">The first color</param>
        /// <param name="color2">The second color</param>
        /// <param name="amount">The blend amount (0.0 = all color1, 1.0 = all color2)</param>
        /// <returns>A blended color</returns>
        public static Color Blend(this Color color1, Color color2, float amount)
        {
            amount = Math.Max(0f, Math.Min(1f, amount));
            
            int a = (int)(color1.A + (color2.A - color1.A) * amount);
            int r = (int)(color1.R + (color2.R - color1.R) * amount);
            int g = (int)(color1.G + (color2.G - color1.G) * amount);
            int b = (int)(color1.B + (color2.B - color1.B) * amount);
            
            return Color.FromArgb(a, r, g, b);
        }

        /// <summary>
        /// Gets the luminance of a color (perceived brightness)
        /// </summary>
        /// <param name="color">The color</param>
        /// <returns>The luminance value (0.0 to 1.0)</returns>
        public static float GetLuminance(this Color color)
        {
            // Using the standard luminance formula
            return (0.299f * color.R + 0.587f * color.G + 0.114f * color.B) / 255f;
        }

        /// <summary>
        /// Determines if a color is considered "dark" (luminance < 0.5)
        /// </summary>
        /// <param name="color">The color</param>
        /// <returns>True if the color is dark, false otherwise</returns>
        public static bool IsDark(this Color color)
        {
            return color.GetLuminance() < 0.5f;
        }

        /// <summary>
        /// Gets a contrasting color (black or white) for text on the given background color
        /// </summary>
        /// <param name="backgroundColor">The background color</param>
        /// <returns>Black or white, whichever provides better contrast</returns>
        public static Color GetContrastingTextColor(this Color backgroundColor)
        {
            return backgroundColor.IsDark() ? Color.White : Color.Black;
        }
    }
}
