using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Interfaces
{
    /// <summary>
    /// Interface for UE4SS detection operations
    /// </summary>
    public interface IUE4SSDetector
    {
        /// <summary>
        /// Detects the current UE4SS installation status
        /// </summary>
        /// <returns>UE4SS detection result</returns>
        Task<UE4SSStatus> DetectUE4SSAsync();

        /// <summary>
        /// Invalidates the UE4SS detection cache
        /// </summary>
        void InvalidateCache();

        /// <summary>
        /// Gets the Palworld root directory being monitored
        /// </summary>
        string PalworldRoot { get; }
    }
}
