using System;
using System.IO;
using System.Threading.Tasks;
using NUnit.Framework;
using ModInstallerApp.Services;
using ModInstallerApp.Models;
using ModInstallerApp.Interfaces;

namespace ModInstallerApp.Tests
{
    /// <summary>
    /// Base class for all unit tests providing common setup, teardown, and utilities
    /// </summary>
    [TestFixture]
    public abstract class TestBase
    {
        protected string TestDataDirectory { get; private set; } = string.Empty;
        protected string TestPalworldRoot { get; private set; } = string.Empty;
        protected string TestModsDirectory { get; private set; } = string.Empty;
        protected string TestBackupDirectory { get; private set; } = string.Empty;
        protected CacheManager? TestCacheManager { get; private set; }
        protected EnhancedLogger? TestLogger { get; private set; }

        [OneTimeSetUp]
        public virtual async Task OneTimeSetUp()
        {
            // Create unique test directory for this test class
            var testId = Guid.NewGuid().ToString("N")[..8];
            var testClassName = GetType().Name;
            TestDataDirectory = Path.Combine(Path.GetTempPath(), "PalworldModInstallerTests", $"{testClassName}_{testId}");
            
            // Create test directory structure
            Directory.CreateDirectory(TestDataDirectory);
            TestPalworldRoot = Path.Combine(TestDataDirectory, "Palworld");
            TestModsDirectory = Path.Combine(TestDataDirectory, "TestMods");
            TestBackupDirectory = Path.Combine(TestDataDirectory, "Backups");
            
            Directory.CreateDirectory(TestPalworldRoot);
            Directory.CreateDirectory(TestModsDirectory);
            Directory.CreateDirectory(TestBackupDirectory);
            
            // Create basic Palworld directory structure
            await CreateBasicPalworldStructureAsync();
            
            // Initialize test services
            TestCacheManager = new CacheManager(Path.Combine(TestDataDirectory, "cache"));
            TestLogger = new EnhancedLogger(TestDataDirectory);
            
            TestContext.WriteLine($"Test environment initialized: {TestDataDirectory}");
        }

        [OneTimeTearDown]
        public virtual async Task OneTimeTearDown()
        {
            try
            {
                // Dispose services
                TestCacheManager?.Dispose();
                TestLogger?.Dispose();
                
                // Clean up test directory
                if (Directory.Exists(TestDataDirectory))
                {
                    await Task.Run(() =>
                    {
                        try
                        {
                            Directory.Delete(TestDataDirectory, true);
                        }
                        catch (Exception ex)
                        {
                            TestContext.WriteLine($"Warning: Could not delete test directory: {ex.Message}");
                        }
                    });
                }
                
                TestContext.WriteLine("Test environment cleaned up");
            }
            catch (Exception ex)
            {
                TestContext.WriteLine($"Error during test cleanup: {ex.Message}");
            }
        }

        [SetUp]
        public virtual async Task SetUp()
        {
            // Reset cache before each test
            TestCacheManager?.ClearAll();
            
            // Log test start
            TestContext.WriteLine($"Starting test: {TestContext.CurrentContext.Test.Name}");
            
            await Task.CompletedTask;
        }

        [TearDown]
        public virtual async Task TearDown()
        {
            // Log test completion
            var result = TestContext.CurrentContext.Result.Outcome.Status;
            TestContext.WriteLine($"Test completed: {TestContext.CurrentContext.Test.Name} - {result}");
            
            await Task.CompletedTask;
        }

        /// <summary>
        /// Creates a basic Palworld directory structure for testing
        /// </summary>
        protected virtual async Task CreateBasicPalworldStructureAsync()
        {
            // Create Palworld executable structure
            var binariesDir = Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64");
            Directory.CreateDirectory(binariesDir);
            
            // Create fake Palworld executable (both versions for compatibility)
            var palExePath = Path.Combine(TestPalworldRoot, "Palworld.exe");
            await File.WriteAllTextAsync(palExePath, "Fake Palworld executable for testing");

            var palShippingExePath = Path.Combine(binariesDir, "Palworld-Win64-Shipping.exe");
            await File.WriteAllTextAsync(palShippingExePath, "Fake Palworld shipping executable for testing");
            
            // Create UE4SS directory structure
            var ue4ssDir = Path.Combine(binariesDir, "ue4ss");
            var ue4ssModsDir = Path.Combine(ue4ssDir, "Mods");
            Directory.CreateDirectory(ue4ssModsDir);
            
            // Create UE4SS files
            await File.WriteAllTextAsync(Path.Combine(binariesDir, "dwmapi.dll"), "UE4SS proxy DLL");
            await File.WriteAllTextAsync(Path.Combine(ue4ssDir, "UE4SS.dll"), "UE4SS core DLL");
            await File.WriteAllTextAsync(Path.Combine(ue4ssDir, "UE4SS-settings.ini"), "[Settings]\nEnabled=true");
            await File.WriteAllTextAsync(Path.Combine(ue4ssDir, "LICENSE"), "UE4SS License");
            
            // Create core mods structure
            foreach (var coreMod in UE4SSDetector.RequiredCoreMods)
            {
                var coreModDir = Path.Combine(ue4ssModsDir, coreMod);
                Directory.CreateDirectory(coreModDir);
                await File.WriteAllTextAsync(Path.Combine(coreModDir, "enabled.txt"), "");
            }
            
            // Create mods.txt and mods.json
            await File.WriteAllTextAsync(Path.Combine(ue4ssModsDir, "mods.txt"), string.Join("\n", UE4SSDetector.RequiredCoreMods));
            await File.WriteAllTextAsync(Path.Combine(ue4ssModsDir, "mods.json"), "{}");
            
            // Create PalSchema structure
            var palSchemaDir = Path.Combine(ue4ssModsDir, "palschema");
            var palSchemaModsDir = Path.Combine(palSchemaDir, "mods");
            Directory.CreateDirectory(palSchemaModsDir);
            
            TestContext.WriteLine("Basic Palworld structure created");
        }

        /// <summary>
        /// Creates a test mod archive with specified structure
        /// </summary>
        protected async Task<string> CreateTestModArchiveAsync(string modName, ModStructureType structureType = ModStructureType.UE4SS)
        {
            var archivePath = Path.Combine(TestModsDirectory, $"{modName}.zip");
            var tempModDir = Path.Combine(TestModsDirectory, $"temp_{modName}");
            
            try
            {
                Directory.CreateDirectory(tempModDir);
                
                switch (structureType)
                {
                    case ModStructureType.UE4SS:
                        await CreateUE4SSModStructureAsync(tempModDir, modName);
                        break;
                    case ModStructureType.PalSchema:
                        await CreatePalSchemaModStructureAsync(tempModDir, modName);
                        break;
                    case ModStructureType.PAK:
                        await CreatePAKModStructureAsync(tempModDir, modName);
                        break;
                }
                
                // Create ZIP archive (simplified - in real implementation would use proper ZIP library)
                await File.WriteAllTextAsync(archivePath, $"Mock ZIP archive for {modName}");
                
                return archivePath;
            }
            finally
            {
                if (Directory.Exists(tempModDir))
                {
                    Directory.Delete(tempModDir, true);
                }
            }
        }

        private async Task CreateUE4SSModStructureAsync(string modDir, string modName)
        {
            var ue4ssModDir = Path.Combine(modDir, "Pal", "Binaries", "Win64", "ue4ss", "Mods", modName);
            Directory.CreateDirectory(ue4ssModDir);
            
            await File.WriteAllTextAsync(Path.Combine(ue4ssModDir, "enabled.txt"), "");
            await File.WriteAllTextAsync(Path.Combine(ue4ssModDir, "main.lua"), $"-- {modName} main script");
        }

        private async Task CreatePalSchemaModStructureAsync(string modDir, string modName)
        {
            var palSchemaModDir = Path.Combine(modDir, "Pal", "Binaries", "Win64", "ue4ss", "Mods", "palschema", "mods", modName);
            Directory.CreateDirectory(palSchemaModDir);
            
            var blueprintsDir = Path.Combine(palSchemaModDir, "blueprints");
            var itemsDir = Path.Combine(palSchemaModDir, "items");
            var rawDir = Path.Combine(palSchemaModDir, "raw");
            
            Directory.CreateDirectory(blueprintsDir);
            Directory.CreateDirectory(itemsDir);
            Directory.CreateDirectory(rawDir);
            
            await File.WriteAllTextAsync(Path.Combine(palSchemaModDir, "mod.json"), $"{{\"name\":\"{modName}\",\"version\":\"1.0.0\"}}");
        }

        private async Task CreatePAKModStructureAsync(string modDir, string modName)
        {
            var pakDir = Path.Combine(modDir, "Pal", "Content", "Paks", "~mods");
            Directory.CreateDirectory(pakDir);
            
            await File.WriteAllTextAsync(Path.Combine(pakDir, $"{modName}.pak"), $"Mock PAK file for {modName}");
        }

        /// <summary>
        /// Asserts that a directory exists and optionally contains expected files
        /// </summary>
        protected void AssertDirectoryExists(string path, params string[] expectedFiles)
        {
            Assert.That(Directory.Exists(path), $"Directory should exist: {path}");
            
            foreach (var expectedFile in expectedFiles)
            {
                var filePath = Path.Combine(path, expectedFile);
                Assert.That(File.Exists(filePath), $"File should exist: {filePath}");
            }
        }

        /// <summary>
        /// Asserts that a file exists and optionally has expected content
        /// </summary>
        protected async Task AssertFileExistsAsync(string path, string? expectedContent = null)
        {
            Assert.That(File.Exists(path), $"File should exist: {path}");
            
            if (expectedContent != null)
            {
                var actualContent = await File.ReadAllTextAsync(path);
                Assert.That(actualContent, Is.EqualTo(expectedContent), $"File content mismatch: {path}");
            }
        }
    }

    public enum ModStructureType
    {
        UE4SS,
        PalSchema,
        PAK
    }

    /// <summary>
    /// Test environment container
    /// </summary>
    public class TestEnvironment : IDisposable
    {
        public string TestDataDirectory { get; set; } = string.Empty;
        public string PalworldRoot { get; set; } = string.Empty;
        public ICacheManager? CacheManager { get; set; }
        public IEnhancedLogger? Logger { get; set; }
        public IUE4SSDetector? UE4SSDetector { get; set; }

        public void Dispose()
        {
            CacheManager?.Dispose();
            Logger?.Dispose();

            // Clean up test directory
            if (Directory.Exists(TestDataDirectory))
            {
                try
                {
                    Directory.Delete(TestDataDirectory, true);
                }
                catch
                {
                    // Ignore cleanup errors
                }
            }
        }
    }

    /// <summary>
    /// Base class for isolated unit tests using mock dependencies
    /// </summary>
    [TestFixture]
    public abstract class IsolatedTestBase : IDisposable
    {
        protected TestEnvironment TestEnvironment { get; private set; } = null!;
        protected string TestDataDirectory => TestEnvironment.TestDataDirectory;
        protected string TestPalworldRoot => TestEnvironment.PalworldRoot;
        protected ICacheManager CacheManager => TestEnvironment.CacheManager!;
        protected IEnhancedLogger Logger => TestEnvironment.Logger!;
        protected IUE4SSDetector UE4SSDetector => TestEnvironment.UE4SSDetector!;

        [OneTimeSetUp]
        public virtual void OneTimeSetUp()
        {
            TestEnvironment = CreateIsolatedEnvironment();
            OnOneTimeSetUp();
        }

        [OneTimeTearDown]
        public virtual void OneTimeTearDown()
        {
            OnOneTimeTearDown();
            TestEnvironment?.Dispose();
        }

        [SetUp]
        public virtual void SetUp()
        {
            // Reset mock state before each test
            if (CacheManager is Tests.Mocks.MockCacheManager mockCache)
            {
                mockCache.ClearAll();
                mockCache.ResetStatistics();
            }

            if (Logger is Tests.Mocks.MockEnhancedLogger mockLogger)
            {
                mockLogger.ClearLogs();
            }

            OnSetUp();
        }

        [TearDown]
        public virtual void TearDown()
        {
            OnTearDown();
        }

        /// <summary>
        /// Override this method in derived classes for additional one-time setup
        /// </summary>
        protected virtual void OnOneTimeSetUp() { }

        /// <summary>
        /// Override this method in derived classes for additional one-time cleanup
        /// </summary>
        protected virtual void OnOneTimeTearDown() { }

        /// <summary>
        /// Override this method in derived classes for additional setup
        /// </summary>
        protected virtual void OnSetUp() { }

        /// <summary>
        /// Override this method in derived classes for additional cleanup
        /// </summary>
        protected virtual void OnTearDown() { }

        public void Dispose()
        {
            TestEnvironment?.Dispose();
        }
    }

    /// <summary>
    /// Base class for integration tests using real services
    /// </summary>
    [TestFixture]
    [Category("Integration")]
    public abstract class IntegrationTestBase : IDisposable
    {
        protected TestEnvironment TestEnvironment { get; private set; } = null!;
        protected string TestDataDirectory => TestEnvironment.TestDataDirectory;
        protected string TestPalworldRoot => TestEnvironment.PalworldRoot;
        protected ICacheManager CacheManager => TestEnvironment.CacheManager!;
        protected IEnhancedLogger Logger => TestEnvironment.Logger!;
        protected IUE4SSDetector UE4SSDetector => TestEnvironment.UE4SSDetector!;

        [OneTimeSetUp]
        public virtual void OneTimeSetUp()
        {
            TestEnvironment = CreateIntegrationEnvironment();
            OnOneTimeSetUp();
        }

        [OneTimeTearDown]
        public virtual void OneTimeTearDown()
        {
            OnOneTimeTearDown();
            TestEnvironment?.Dispose();
        }

        [SetUp]
        public virtual void SetUp()
        {
            // Clear cache before each test for isolation
            CacheManager.ClearAll();
            OnSetUp();
        }

        [TearDown]
        public virtual void TearDown()
        {
            OnTearDown();
        }

        /// <summary>
        /// Override this method in derived classes for additional one-time setup
        /// </summary>
        protected virtual void OnOneTimeSetUp() { }

        /// <summary>
        /// Override this method in derived classes for additional one-time cleanup
        /// </summary>
        protected virtual void OnOneTimeTearDown() { }

        /// <summary>
        /// Override this method in derived classes for additional setup
        /// </summary>
        protected virtual void OnSetUp() { }

        /// <summary>
        /// Override this method in derived classes for additional cleanup
        /// </summary>
        protected virtual void OnTearDown() { }

        public void Dispose()
        {
            TestEnvironment?.Dispose();
        }

        // Helper methods for creating test environments
        private static TestEnvironment CreateIsolatedEnvironment()
        {
            var testDataDir = TestUtilities.CreateTempDirectory("isolated_test");
            var palworldRoot = Path.Combine(testDataDir, "Palworld");
            Directory.CreateDirectory(palworldRoot);

            // Create basic Palworld structure
            Directory.CreateDirectory(Path.Combine(palworldRoot, "Pal", "Binaries", "Win64"));
            File.WriteAllText(Path.Combine(palworldRoot, "Pal", "Binaries", "Win64", "Palworld-Win64-Shipping.exe"), "fake exe");

            return new TestEnvironment
            {
                TestDataDirectory = testDataDir,
                PalworldRoot = palworldRoot,
                CacheManager = new Tests.Mocks.MockCacheManager(),
                Logger = new Tests.Mocks.MockEnhancedLogger(),
                UE4SSDetector = new Tests.Mocks.MockUE4SSDetector(palworldRoot)
            };
        }

        private static TestEnvironment CreateIntegrationEnvironment()
        {
            var testDataDir = TestUtilities.CreateTempDirectory("integration_test");
            var palworldRoot = Path.Combine(testDataDir, "Palworld");
            Directory.CreateDirectory(palworldRoot);

            // Create basic Palworld structure
            Directory.CreateDirectory(Path.Combine(palworldRoot, "Pal", "Binaries", "Win64"));
            File.WriteAllText(Path.Combine(palworldRoot, "Pal", "Binaries", "Win64", "Palworld-Win64-Shipping.exe"), "fake exe");

            var cacheManager = new CacheManager(Path.Combine(testDataDir, "cache"));

            return new TestEnvironment
            {
                TestDataDirectory = testDataDir,
                PalworldRoot = palworldRoot,
                CacheManager = cacheManager,
                Logger = new EnhancedLogger(testDataDir),
                UE4SSDetector = new UE4SSDetector(palworldRoot, cacheManager)
            };
        }
    }
}
