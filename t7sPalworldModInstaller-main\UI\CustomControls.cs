using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using ModInstallerApp.Extensions;

namespace ModInstallerApp.UI
{
    public class ThemedButton : Button
    {
        bool _hover, _pressed;
        public ThemedButton()
        {
            FlatStyle = FlatStyle.Flat; FlatAppearance.BorderSize = 0;
            BackColor = Theme.Accent; ForeColor = Theme.TextDark;
            Font = Theme.BodyFont; Padding = new Padding(12, 6, 12, 6);
            AutoSize = true; AutoSizeMode = AutoSizeMode.GrowAndShrink;
            Cursor = Cursors.Hand; DoubleBuffered = true;
        }
        protected override void OnMouseEnter(EventArgs e) { _hover = true; Invalidate(); base.OnMouseEnter(e); }
        protected override void OnMouseLeave(EventArgs e) { _hover = _pressed = false; Invalidate(); base.OnMouseLeave(e); }
        protected override void OnMouseDown(MouseEventArgs e) { _pressed = true; Invalidate(); base.OnMouseDown(e); }
        protected override void OnMouseUp(MouseEventArgs e) { _pressed = false; Invalidate(); base.OnMouseUp(e); }
        protected override void OnPaint(PaintEventArgs e)
        {
            e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
            var r = ClientRectangle;
            using var gp = new GraphicsPath();
            int rad = 8;
            gp.AddArc(r.X, r.Y, rad, rad, 180, 90); gp.AddArc(r.Right - rad, r.Y, rad, rad, 270, 90);
            gp.AddArc(r.Right - rad, r.Bottom - rad, rad, rad, 0, 90); gp.AddArc(r.X, r.Bottom - rad, rad, rad, 90, 90);
            gp.CloseAllFigures();
            Color bg = _pressed ? Theme.Accent2 : _hover ? BackColor.Lighten(25) : BackColor;
            using var br = new SolidBrush(bg); e.Graphics.FillPath(br, gp);
            using var pen = new Pen(Color.FromArgb(90, Color.Black), 1); e.Graphics.DrawPath(pen, gp);
            TextRenderer.DrawText(e.Graphics, Text, Font, r, ForeColor,
                TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
        }
    }

    public class ThemedProgressBar : ProgressBar
    {
        public ThemedProgressBar() { SetStyle(ControlStyles.UserPaint, true); BackColor = Theme.Track; ForeColor = Theme.Accent; }
        protected override void OnPaint(PaintEventArgs e)
        {
            e.Graphics.Clear(Theme.Track);
            if (Maximum == 0 || Value == 0) return;
            float pct = (float)Value / Maximum;
            using var br = new SolidBrush(Theme.Accent);
            e.Graphics.FillRectangle(br, 0, 0, pct * Width, Height);
        }
    }

    public class StatusIndicator : Panel
    {
        private string _text = "";
        private Color _statusColor = Theme.TextLight;

        public string StatusText
        {
            get => _text;
            set { _text = value; Invalidate(); }
        }

        public Color StatusColor
        {
            get => _statusColor;
            set { _statusColor = value; Invalidate(); }
        }

        public StatusIndicator()
        {
            Size = new Size(200, 24);
            DoubleBuffered = true;
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            e.Graphics.Clear(Parent?.BackColor ?? Theme.Surface);
            e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

            // Draw status dot
            using var brush = new SolidBrush(_statusColor);
            e.Graphics.FillEllipse(brush, 4, 6, 12, 12);

            // Draw text
            TextRenderer.DrawText(e.Graphics, _text, Theme.SmallFont,
                new Rectangle(20, 0, Width - 20, Height), Theme.TextLight,
                TextFormatFlags.VerticalCenter | TextFormatFlags.Left);
        }
    }
}