using System;
using System.Collections.Generic;

namespace ModInstallerApp.Models
{
    public enum UE4SSInstallStatus
    {
        NotInstalled,
        PartiallyInstalled,
        FullyInstalled,
        Unknown
    }

    public class UE4SSStatus
    {
        public UE4SSInstallStatus Status { get; set; } = UE4SSInstallStatus.NotInstalled;
        public bool HasProxyDll { get; set; }
        public bool HasUE4SSFolder { get; set; }
        public bool HasUE4SSDll { get; set; }
        public bool HasSettingsIni { get; set; }
        public bool HasLicense { get; set; }
        public bool HasModsJson { get; set; }
        public bool HasModsTxt { get; set; }
        public List<string> CoreMods { get; set; } = new();
        public List<string> UserMods { get; set; } = new();
        public int CoreModsPresent { get; set; }
        public int CoreModsExpected { get; set; } = 9;
        public DateTime LastChecked { get; set; } = DateTime.Now;
    }

    public class PalSchemaStatus
    {
        public bool IsInstalled { get; set; }
        public string FolderPath { get; set; } = "";
        public List<PalSchemaMod> Mods { get; set; } = new();
        public DateTime LastChecked { get; set; } = DateTime.Now;
    }

    public class PalSchemaMod
    {
        public string Name { get; set; } = "";
        public string Path { get; set; } = "";
        public bool HasBlueprints { get; set; }
        public bool HasItems { get; set; }
        public bool HasRaw { get; set; }
        public bool IsValid { get; set; }
    }
}