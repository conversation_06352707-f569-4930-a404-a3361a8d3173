using System;

namespace ModInstallerApp.Interfaces
{
    /// <summary>
    /// Interface for cache management operations
    /// </summary>
    public interface ICacheManager : IDisposable
    {
        /// <summary>
        /// Gets a cached value by key
        /// </summary>
        /// <typeparam name="T">Type of the cached value</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached value or null if not found</returns>
        T? Get<T>(string key);

        /// <summary>
        /// Sets a value in the cache with the specified expiration
        /// </summary>
        /// <typeparam name="T">Type of the value to cache</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Value to cache</param>
        /// <param name="expiration">Cache expiration time</param>
        void Set<T>(string key, T value, TimeSpan expiration);

        /// <summary>
        /// Removes a specific key from the cache
        /// </summary>
        /// <param name="key">Cache key to remove</param>
        void Remove(string key);

        /// <summary>
        /// Invalidates a specific cache entry
        /// </summary>
        /// <param name="key">Cache key to invalidate</param>
        void InvalidateCache(string key);

        /// <summary>
        /// Clears all cached items
        /// </summary>
        void ClearAll();

        /// <summary>
        /// Clears expired cache entries
        /// </summary>
        void ClearExpired();

        /// <summary>
        /// Gets the total number of items in the cache
        /// </summary>
        int Count { get; }

        /// <summary>
        /// Gets the cache hit ratio (for performance monitoring)
        /// </summary>
        double HitRatio { get; }
    }
}
