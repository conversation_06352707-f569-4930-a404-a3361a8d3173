using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows.Forms;
using ModInstallerApp.Models;
using ModInstallerApp.Services;

namespace ModInstallerApp.UI
{
    /// <summary>
    /// Dialog for managing PalSchema configurations with visual JSON editor and real-time preview
    /// </summary>
    public partial class PalSchemaConfigurationDialog : Form
    {
        private readonly PalSchemaConfigurationService _configService;
        private readonly EnhancedLogger _logger;
        private PalSchemaConfig? _currentConfig;
        private bool _isModified = false;

        // UI Controls
        private TabControl _mainTabControl = null!;
        private TextBox _nameTextBox = null!;
        private TextBox _descriptionTextBox = null!;
        private ComboBox _configTypeComboBox = null!;
        private RichTextBox _jsonEditor = null!;
        private TreeView _propertyTreeView = null!;
        private ListView _validationListView = null!;
        private Panel _previewPanel = null!;
        private Label _previewLabel = null!;
        private Button _saveButton = null!;
        private Button _cancelButton = null!;
        private Button _validateButton = null!;
        private Button _loadTemplateButton = null!;
        private Button _exportButton = null!;
        private Button _importButton = null!;
        private ProgressBar _validationProgressBar = null!;

        public PalSchemaConfig? Result { get; private set; }

        public PalSchemaConfigurationDialog(PalSchemaConfigurationService configService, EnhancedLogger logger, PalSchemaConfig? existingConfig = null)
        {
            _configService = configService ?? throw new ArgumentNullException(nameof(configService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _currentConfig = existingConfig ?? new PalSchemaConfig();

            InitializeComponent();
            LoadConfiguration();
        }

        private void InitializeComponent()
        {
            Text = "PalSchema Configuration Editor";
            Size = new Size(1000, 700);
            StartPosition = FormStartPosition.CenterParent;
            MinimumSize = new Size(800, 600);

            // Main tab control
            _mainTabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Margin = new Padding(10)
            };

            // Configuration tab
            var configTab = new TabPage("Configuration");
            CreateConfigurationTab(configTab);
            _mainTabControl.TabPages.Add(configTab);

            // JSON Editor tab
            var jsonTab = new TabPage("JSON Editor");
            CreateJsonEditorTab(jsonTab);
            _mainTabControl.TabPages.Add(jsonTab);

            // Property Editor tab
            var propertyTab = new TabPage("Property Editor");
            CreatePropertyEditorTab(propertyTab);
            _mainTabControl.TabPages.Add(propertyTab);

            // Validation tab
            var validationTab = new TabPage("Validation");
            CreateValidationTab(validationTab);
            _mainTabControl.TabPages.Add(validationTab);

            // Preview tab
            var previewTab = new TabPage("Preview");
            CreatePreviewTab(previewTab);
            _mainTabControl.TabPages.Add(previewTab);

            // Button panel
            var buttonPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom,
                Padding = new Padding(10)
            };

            _saveButton = new Button
            {
                Text = "Save",
                Size = new Size(80, 30),
                Location = new Point(10, 10),
                DialogResult = DialogResult.OK
            };
            _saveButton.Click += SaveButton_Click;

            _cancelButton = new Button
            {
                Text = "Cancel",
                Size = new Size(80, 30),
                Location = new Point(100, 10),
                DialogResult = DialogResult.Cancel
            };

            _validateButton = new Button
            {
                Text = "Validate",
                Size = new Size(80, 30),
                Location = new Point(190, 10)
            };
            _validateButton.Click += ValidateButton_Click;

            _loadTemplateButton = new Button
            {
                Text = "Load Template",
                Size = new Size(100, 30),
                Location = new Point(280, 10)
            };
            _loadTemplateButton.Click += LoadTemplateButton_Click;

            _exportButton = new Button
            {
                Text = "Export",
                Size = new Size(80, 30),
                Location = new Point(390, 10)
            };
            _exportButton.Click += ExportButton_Click;

            _importButton = new Button
            {
                Text = "Import",
                Size = new Size(80, 30),
                Location = new Point(480, 10)
            };
            _importButton.Click += ImportButton_Click;

            buttonPanel.Controls.AddRange(new Control[] 
            { 
                _saveButton, _cancelButton, _validateButton, 
                _loadTemplateButton, _exportButton, _importButton 
            });

            // Main layout
            var mainPanel = new Panel { Dock = DockStyle.Fill };
            mainPanel.Controls.Add(_mainTabControl);
            mainPanel.Controls.Add(buttonPanel);

            Controls.Add(mainPanel);
        }

        private void CreateConfigurationTab(TabPage tab)
        {
            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                Padding = new Padding(10)
            };

            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // Name
            layout.Controls.Add(new Label { Text = "Name:", Anchor = AnchorStyles.Left }, 0, 0);
            _nameTextBox = new TextBox { Dock = DockStyle.Fill };
            _nameTextBox.TextChanged += (s, e) => MarkAsModified();
            layout.Controls.Add(_nameTextBox, 1, 0);

            // Description
            layout.Controls.Add(new Label { Text = "Description:", Anchor = AnchorStyles.Left }, 0, 1);
            _descriptionTextBox = new TextBox { Dock = DockStyle.Fill, Multiline = true, Height = 60 };
            _descriptionTextBox.TextChanged += (s, e) => MarkAsModified();
            layout.Controls.Add(_descriptionTextBox, 1, 1);

            // Config Type
            layout.Controls.Add(new Label { Text = "Type:", Anchor = AnchorStyles.Left }, 0, 2);
            _configTypeComboBox = new ComboBox 
            { 
                Dock = DockStyle.Fill, 
                DropDownStyle = ComboBoxStyle.DropDownList 
            };
            _configTypeComboBox.Items.AddRange(Enum.GetNames(typeof(PalSchemaConfigType)));
            _configTypeComboBox.SelectedIndexChanged += (s, e) => MarkAsModified();
            layout.Controls.Add(_configTypeComboBox, 1, 2);

            tab.Controls.Add(layout);
        }

        private void CreateJsonEditorTab(TabPage tab)
        {
            _jsonEditor = new RichTextBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Consolas", 10),
                WordWrap = false,
                AcceptsTab = true
            };
            _jsonEditor.TextChanged += JsonEditor_TextChanged;

            tab.Controls.Add(_jsonEditor);
        }

        private void CreatePropertyEditorTab(TabPage tab)
        {
            _propertyTreeView = new TreeView
            {
                Dock = DockStyle.Fill,
                CheckBoxes = false,
                FullRowSelect = true,
                ShowLines = true,
                ShowPlusMinus = true
            };
            _propertyTreeView.AfterSelect += PropertyTreeView_AfterSelect;

            tab.Controls.Add(_propertyTreeView);
        }

        private void CreateValidationTab(TabPage tab)
        {
            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 3,
                ColumnCount = 1,
                Padding = new Padding(10)
            };

            layout.RowStyles.Add(new RowStyle(SizeType.Absolute, 30));
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 100));
            layout.RowStyles.Add(new RowStyle(SizeType.Absolute, 25));

            // Validation header
            var headerLabel = new Label 
            { 
                Text = "Configuration Validation Results", 
                Font = new Font(Font, FontStyle.Bold),
                Dock = DockStyle.Fill
            };
            layout.Controls.Add(headerLabel, 0, 0);

            // Validation list
            _validationListView = new ListView
            {
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true
            };
            _validationListView.Columns.Add("Severity", 80);
            _validationListView.Columns.Add("Property", 150);
            _validationListView.Columns.Add("Message", 400);
            _validationListView.Columns.Add("Suggested Fix", 300);

            layout.Controls.Add(_validationListView, 0, 1);

            // Progress bar
            _validationProgressBar = new ProgressBar
            {
                Dock = DockStyle.Fill,
                Visible = false
            };
            layout.Controls.Add(_validationProgressBar, 0, 2);

            tab.Controls.Add(layout);
        }

        private void CreatePreviewTab(TabPage tab)
        {
            _previewPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(10)
            };

            _previewLabel = new Label
            {
                Text = "Configuration preview will appear here after validation",
                Dock = DockStyle.Fill,
                Font = new Font("Consolas", 9),
                ForeColor = Color.Gray
            };

            _previewPanel.Controls.Add(_previewLabel);
            tab.Controls.Add(_previewPanel);
        }

        private void LoadConfiguration()
        {
            if (_currentConfig == null) return;

            _nameTextBox.Text = _currentConfig.Name;
            _descriptionTextBox.Text = _currentConfig.Description;
            _configTypeComboBox.SelectedItem = _currentConfig.ConfigType.ToString();

            UpdateJsonEditor();
            UpdatePropertyTree();
            _isModified = false;
        }

        private void UpdateJsonEditor()
        {
            if (_currentConfig?.Configuration != null)
            {
                try
                {
                    var options = new JsonSerializerOptions { WriteIndented = true };
                    var json = JsonSerializer.Serialize(_currentConfig.Configuration, options);
                    _jsonEditor.Text = json;
                }
                catch (Exception ex)
                {
                    _jsonEditor.Text = $"Error serializing configuration: {ex.Message}";
                }
            }
        }

        private void UpdatePropertyTree()
        {
            _propertyTreeView.Nodes.Clear();
            
            if (_currentConfig?.Configuration != null)
            {
                var rootNode = new TreeNode("Configuration");
                PopulateTreeNode(rootNode, _currentConfig.Configuration);
                _propertyTreeView.Nodes.Add(rootNode);
                rootNode.Expand();
            }
        }

        private void PopulateTreeNode(TreeNode parentNode, object value)
        {
            if (value is Dictionary<string, object> dict)
            {
                foreach (var kvp in dict)
                {
                    var childNode = new TreeNode($"{kvp.Key}: {GetValuePreview(kvp.Value)}");
                    childNode.Tag = kvp;
                    parentNode.Nodes.Add(childNode);
                    
                    if (kvp.Value is Dictionary<string, object>)
                    {
                        PopulateTreeNode(childNode, kvp.Value);
                    }
                }
            }
        }

        private string GetValuePreview(object value)
        {
            if (value == null) return "null";
            if (value is string s) return $"\"{s}\"";
            if (value is Dictionary<string, object> dict) return $"{{ {dict.Count} properties }}";
            return value.ToString() ?? "";
        }

        private void MarkAsModified()
        {
            _isModified = true;
            if (!Text.EndsWith(" *"))
            {
                Text += " *";
            }
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_currentConfig == null) return;

                // Update configuration from UI
                _currentConfig.Name = _nameTextBox.Text;
                _currentConfig.Description = _descriptionTextBox.Text;
                if (Enum.TryParse<PalSchemaConfigType>(_configTypeComboBox.SelectedItem?.ToString(), out var configType))
                {
                    _currentConfig.ConfigType = configType;
                }

                // Parse JSON from editor
                try
                {
                    var configDict = JsonSerializer.Deserialize<Dictionary<string, object>>(_jsonEditor.Text);
                    if (configDict != null)
                    {
                        _currentConfig.Configuration = configDict;
                    }
                }
                catch (JsonException ex)
                {
                    MessageBox.Show($"Invalid JSON in editor: {ex.Message}", "JSON Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                await _configService.SaveConfigurationAsync(_currentConfig);
                Result = _currentConfig;
                _isModified = false;
                
                MessageBox.Show("Configuration saved successfully!", "Success", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to save PalSchema configuration", "UI", ex);
                MessageBox.Show($"Failed to save configuration: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void ValidateButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_currentConfig == null) return;

                _validationProgressBar.Visible = true;
                _validationProgressBar.Style = ProgressBarStyle.Marquee;

                var results = await _configService.ValidateConfigurationAsync(_currentConfig);
                
                _validationListView.Items.Clear();
                foreach (var result in results)
                {
                    var item = new ListViewItem(result.Severity.ToString());
                    item.SubItems.Add(result.PropertyPath);
                    item.SubItems.Add(result.Message);
                    item.SubItems.Add(result.SuggestedFix ?? "");
                    
                    // Color code by severity
                    switch (result.Severity)
                    {
                        case PalSchemaValidationSeverity.Error:
                        case PalSchemaValidationSeverity.Critical:
                            item.ForeColor = Color.Red;
                            break;
                        case PalSchemaValidationSeverity.Warning:
                            item.ForeColor = Color.Orange;
                            break;
                        default:
                            item.ForeColor = Color.Blue;
                            break;
                    }
                    
                    _validationListView.Items.Add(item);
                }

                _validationProgressBar.Visible = false;
                _mainTabControl.SelectedTab = _mainTabControl.TabPages["Validation"];
                
                var errorCount = results.Count(r => r.Severity >= PalSchemaValidationSeverity.Error);
                var warningCount = results.Count(r => r.Severity == PalSchemaValidationSeverity.Warning);
                
                MessageBox.Show($"Validation completed.\nErrors: {errorCount}\nWarnings: {warningCount}", 
                    "Validation Results", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _validationProgressBar.Visible = false;
                _logger.LogError("Failed to validate PalSchema configuration", "UI", ex);
                MessageBox.Show($"Validation failed: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadTemplateButton_Click(object? sender, EventArgs e)
        {
            try
            {
                var templates = _configService.GetTemplates();
                if (!templates.Any())
                {
                    MessageBox.Show("No templates available.", "No Templates", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Simple template selection dialog
                var templateNames = templates.Select(t => t.Name).ToArray();
                var selectedTemplate = ShowTemplateSelectionDialog(templateNames);
                
                if (selectedTemplate != null)
                {
                    var template = templates.First(t => t.Name == selectedTemplate);
                    _currentConfig = _configService.CreateFromTemplate(template);
                    LoadConfiguration();
                    MarkAsModified();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to load template", "UI", ex);
                MessageBox.Show($"Failed to load template: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string? ShowTemplateSelectionDialog(string[] templateNames)
        {
            // Simple input dialog for template selection
            var dialog = new Form
            {
                Text = "Select Template",
                Size = new Size(300, 150),
                StartPosition = FormStartPosition.CenterParent
            };

            var comboBox = new ComboBox
            {
                Items = { templateNames },
                DropDownStyle = ComboBoxStyle.DropDownList,
                Location = new Point(10, 10),
                Size = new Size(260, 25)
            };

            var okButton = new Button
            {
                Text = "OK",
                DialogResult = DialogResult.OK,
                Location = new Point(110, 50),
                Size = new Size(75, 25)
            };

            var cancelButton = new Button
            {
                Text = "Cancel",
                DialogResult = DialogResult.Cancel,
                Location = new Point(195, 50),
                Size = new Size(75, 25)
            };

            dialog.Controls.AddRange(new Control[] { comboBox, okButton, cancelButton });
            
            return dialog.ShowDialog() == DialogResult.OK ? comboBox.SelectedItem?.ToString() : null;
        }

        private void ExportButton_Click(object? sender, EventArgs e)
        {
            // TODO: Implement export functionality
            MessageBox.Show("Export functionality will be implemented in a future update.", "Not Implemented", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ImportButton_Click(object? sender, EventArgs e)
        {
            // TODO: Implement import functionality
            MessageBox.Show("Import functionality will be implemented in a future update.", "Not Implemented", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void JsonEditor_TextChanged(object? sender, EventArgs e)
        {
            MarkAsModified();
        }

        private void PropertyTreeView_AfterSelect(object? sender, TreeViewEventArgs e)
        {
            // TODO: Implement property editing in tree view
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (_isModified)
            {
                var result = MessageBox.Show("You have unsaved changes. Do you want to save before closing?", 
                    "Unsaved Changes", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    SaveButton_Click(null, EventArgs.Empty);
                }
                else if (result == DialogResult.Cancel)
                {
                    e.Cancel = true;
                }
            }
            
            base.OnFormClosing(e);
        }
    }
}
