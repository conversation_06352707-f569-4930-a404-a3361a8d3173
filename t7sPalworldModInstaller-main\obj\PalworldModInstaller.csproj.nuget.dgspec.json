{"format": 1, "restore": {"C:\\Users\\<USER>\\Downloads\\t7sPalworldModInstaller\\t7sPalworldModInstaller-main\\PalworldModInstaller.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Downloads\\t7sPalworldModInstaller\\t7sPalworldModInstaller-main\\PalworldModInstaller.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\t7sPalworldModInstaller\\t7sPalworldModInstaller-main\\PalworldModInstaller.csproj", "projectName": "PalworldModInstaller", "projectPath": "C:\\Users\\<USER>\\Downloads\\t7sPalworldModInstaller\\t7sPalworldModInstaller-main\\PalworldModInstaller.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\t7sPalworldModInstaller\\t7sPalworldModInstaller-main\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"FluentAssertions": {"target": "Package", "version": "[6.12.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "Moq": {"target": "Package", "version": "[4.20.69, )"}, "NUnit": {"target": "Package", "version": "[4.0.1, )"}, "NUnit.Analyzers": {"target": "Package", "version": "[3.9.0, )"}, "NUnit3TestAdapter": {"target": "Package", "version": "[4.5.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}