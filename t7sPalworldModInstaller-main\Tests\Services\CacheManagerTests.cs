using System;
using System.IO;
using System.Threading.Tasks;
using NUnit.Framework;
using FluentAssertions;
using ModInstallerApp.Services;

namespace ModInstallerApp.Tests.Services
{
    /// <summary>
    /// Comprehensive unit tests for CacheManager service
    /// </summary>
    [TestFixture]
    [Category(TestCategories.Unit)]
    public class CacheManagerTests : TestBase
    {
        private CacheManager? _cacheManager;
        private string _testCachePath = string.Empty;

        [SetUp]
        public override async Task SetUp()
        {
            await base.SetUp();
            
            _testCachePath = Path.Combine(TestDataDirectory, "test-cache");
            _cacheManager = new CacheManager(_testCachePath);
        }

        [TearDown]
        public override async Task TearDown()
        {
            _cacheManager?.Dispose();
            await base.TearDown();
        }

        [Test]
        public void Set_WithValidKeyAndValue_StoresValue()
        {
            // Arrange
            const string key = "test-key";
            const string value = "test-value";

            // Act
            _cacheManager!.Set(key, value);

            // Assert
            var retrievedValue = _cacheManager.Get<string>(key);
            retrievedValue.Should().Be(value);
        }

        [Test]
        public void Get_WithNonExistentKey_ReturnsDefault()
        {
            // Arrange
            const string key = "non-existent-key";

            // Act
            var result = _cacheManager!.Get<string>(key);

            // Assert
            result.Should().BeNull();
        }

        [Test]
        public void Set_WithExpiry_ExpiresAfterTimeout()
        {
            // Arrange
            const string key = "expiring-key";
            const string value = "expiring-value";
            var expiry = TimeSpan.FromMilliseconds(100);

            // Act
            _cacheManager!.Set(key, value, expiry);
            
            // Wait for expiry
            Task.Delay(150).Wait();

            // Assert
            var result = _cacheManager.Get<string>(key);
            result.Should().BeNull();
        }

        [Test]
        public void Remove_WithExistingKey_RemovesValue()
        {
            // Arrange
            const string key = "removable-key";
            const string value = "removable-value";
            _cacheManager!.Set(key, value);

            // Act
            _cacheManager.InvalidateCache(key);

            // Assert
            var result = _cacheManager.Get<string>(key);
            result.Should().BeNull();
        }

        [Test]
        public void ClearAll_RemovesAllCachedItems()
        {
            // Arrange
            _cacheManager!.Set("key1", "value1");
            _cacheManager.Set("key2", "value2");
            _cacheManager.Set("key3", "value3");

            // Act
            _cacheManager.ClearAll();

            // Assert
            _cacheManager.Get<string>("key1").Should().BeNull();
            _cacheManager.Get<string>("key2").Should().BeNull();
            _cacheManager.Get<string>("key3").Should().BeNull();
        }

        [Test]
        public void Set_WithComplexObject_StoresAndRetrievesCorrectly()
        {
            // Arrange
            const string key = "complex-object";
            var complexObject = new TestComplexObject
            {
                Id = 123,
                Name = "Test Object",
                CreatedDate = DateTime.UtcNow,
                Properties = new[] { "prop1", "prop2", "prop3" }
            };

            // Act
            _cacheManager!.Set(key, complexObject);

            // Assert
            var retrieved = _cacheManager.Get<TestComplexObject>(key);
            retrieved.Should().NotBeNull();
            retrieved!.Id.Should().Be(complexObject.Id);
            retrieved.Name.Should().Be(complexObject.Name);
            retrieved.Properties.Should().BeEquivalentTo(complexObject.Properties);
        }

        [Test]
        public void Constructor_WithInvalidPath_DoesNotThrow()
        {
            // Act & Assert - Constructor doesn't validate empty paths, just creates directories
            var cache1 = new CacheManager("");
            var cache2 = new CacheManager("   ");

            cache1.Dispose();
            cache2.Dispose();
            // Test passes if no exception is thrown
        }

        [Test]
        public void Constructor_WithNullPath_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new CacheManager(null!));
        }

        [Test]
        public void Set_WithNullKey_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => _cacheManager!.Set(null!, "value"));
        }

        [Test]
        public void Get_WithNullKey_ReturnsDefault()
        {
            // Act
            var result = _cacheManager!.Get<string>(null!);

            // Assert
            result.Should().BeNull();
        }

        [Test]
        public void InvalidateCache_WithNullKey_DoesNotThrow()
        {
            // Act & Assert - Method silently returns for null/empty keys
            _cacheManager!.InvalidateCache(null!);
            // Test passes if no exception is thrown
        }

        [Test]
        public void ThreadSafety_ConcurrentOperations_HandledCorrectly()
        {
            // Arrange
            const int operationCount = 100;
            var tasks = new Task[operationCount];

            // Act
            for (int i = 0; i < operationCount; i++)
            {
                var index = i;
                tasks[i] = Task.Run(() =>
                {
                    var key = $"concurrent-key-{index}";
                    var value = $"concurrent-value-{index}";
                    
                    _cacheManager!.Set(key, value);
                    var retrieved = _cacheManager.Get<string>(key);
                    
                    retrieved.Should().Be(value);
                });
            }

            // Assert
            Task.WaitAll(tasks);
            // If we reach here without exceptions, thread safety is working
        }

        [Test]
        public void Dispose_DisposesResourcesProperly()
        {
            // Arrange
            var cacheManager = new CacheManager(_testCachePath);
            cacheManager.Set("test", "value");

            // Act
            cacheManager.Dispose();

            // Assert
            // Verify that subsequent operations throw ObjectDisposedException
            Assert.Throws<ObjectDisposedException>(() => cacheManager.Get<string>("test"));
            Assert.Throws<ObjectDisposedException>(() => cacheManager.Set("test", "value"));
        }

        private class TestComplexObject
        {
            public int Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public DateTime CreatedDate { get; set; }
            public string[] Properties { get; set; } = Array.Empty<string>();
        }
    }
}
