using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ModInstallerApp.Models;

namespace ModInstallerApp.UI
{
    public partial class ProfileManagerDialog : Form
    {
        public List<ModProfile> Profiles { get; private set; }
        public ModProfile? SelectedProfile { get; private set; }

        private ListBox _profileListBox = null!;
        private TextBox _profileNameTextBox = null!;
        private TextBox _profileDescriptionTextBox = null!;
        private Button _newButton = null!;
        private Button _saveButton = null!;
        private Button _deleteButton = null!;
        private Button _duplicateButton = null!;
        private Button _exportButton = null!;
        private Button _importButton = null!;
        private Button _selectButton = null!;
        private Button _cancelButton = null!;
        private Label _modCountLabel = null!;

        public ProfileManagerDialog(List<ModProfile> profiles)
        {
            Profiles = profiles ?? new List<ModProfile>();
            InitializeComponent();
            LoadProfiles();
        }

        private void InitializeComponent()
        {
            this.Text = "Profile Manager";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(500, 400);

            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                Padding = new Padding(10)
            };

            // Profile list
            var profilePanel = new GroupBox
            {
                Text = "Profiles",
                Dock = DockStyle.Fill
            };

            _profileListBox = new ListBox
            {
                Dock = DockStyle.Fill,
                DisplayMember = "Name"
            };
            _profileListBox.SelectedIndexChanged += ProfileListBox_SelectedIndexChanged;
            profilePanel.Controls.Add(_profileListBox);

            mainPanel.Controls.Add(profilePanel, 0, 0);
            mainPanel.SetRowSpan(profilePanel, 2);

            // Profile details
            var detailsPanel = new GroupBox
            {
                Text = "Profile Details",
                Dock = DockStyle.Fill
            };

            var detailsLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                Padding = new Padding(5)
            };

            detailsLayout.Controls.Add(new Label { Text = "Name:", Anchor = AnchorStyles.Left }, 0, 0);
            _profileNameTextBox = new TextBox { Dock = DockStyle.Fill };
            _profileNameTextBox.TextChanged += ProfileNameTextBox_TextChanged;
            detailsLayout.Controls.Add(_profileNameTextBox, 1, 0);

            detailsLayout.Controls.Add(new Label { Text = "Description:", Anchor = AnchorStyles.Left }, 0, 1);
            _profileDescriptionTextBox = new TextBox 
            { 
                Dock = DockStyle.Fill,
                Multiline = true,
                Height = 60
            };
            _profileDescriptionTextBox.TextChanged += ProfileDescriptionTextBox_TextChanged;
            detailsLayout.Controls.Add(_profileDescriptionTextBox, 1, 1);

            _modCountLabel = new Label 
            { 
                Text = "Mods: 0",
                Dock = DockStyle.Fill,
                ForeColor = Color.Gray
            };
            detailsLayout.Controls.Add(_modCountLabel, 1, 2);

            // Set column styles
            detailsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));
            detailsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // Set row styles
            detailsLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 30));
            detailsLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 80));
            detailsLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 25));
            detailsLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100));

            detailsPanel.Controls.Add(detailsLayout);
            mainPanel.Controls.Add(detailsPanel, 1, 0);

            // Action buttons
            var actionPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                Padding = new Padding(5)
            };

            _newButton = new Button
            {
                Text = "New",
                Size = new Size(75, 25)
            };
            _newButton.Click += NewButton_Click;

            _saveButton = new Button
            {
                Text = "Save",
                Size = new Size(75, 25),
                Enabled = false
            };
            _saveButton.Click += SaveButton_Click;

            _deleteButton = new Button
            {
                Text = "Delete",
                Size = new Size(75, 25),
                Enabled = false
            };
            _deleteButton.Click += DeleteButton_Click;

            _duplicateButton = new Button
            {
                Text = "Duplicate",
                Size = new Size(75, 25),
                Enabled = false
            };
            _duplicateButton.Click += DuplicateButton_Click;

            _exportButton = new Button
            {
                Text = "Export",
                Size = new Size(75, 25),
                Enabled = false
            };
            _exportButton.Click += ExportButton_Click;

            _importButton = new Button
            {
                Text = "Import",
                Size = new Size(75, 25)
            };
            _importButton.Click += ImportButton_Click;

            actionPanel.Controls.Add(_newButton);
            actionPanel.Controls.Add(_saveButton);
            actionPanel.Controls.Add(_deleteButton);
            actionPanel.Controls.Add(_duplicateButton);
            actionPanel.Controls.Add(_exportButton);
            actionPanel.Controls.Add(_importButton);

            mainPanel.Controls.Add(actionPanel, 1, 1);

            // Dialog buttons
            var dialogButtonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft,
                Padding = new Padding(5)
            };

            _cancelButton = new Button
            {
                Text = "Cancel",
                Size = new Size(75, 25),
                DialogResult = DialogResult.Cancel
            };

            _selectButton = new Button
            {
                Text = "Select",
                Size = new Size(75, 25),
                DialogResult = DialogResult.OK,
                Enabled = false
            };
            _selectButton.Click += SelectButton_Click;

            dialogButtonPanel.Controls.Add(_cancelButton);
            dialogButtonPanel.Controls.Add(_selectButton);

            mainPanel.Controls.Add(dialogButtonPanel, 1, 2);

            // Set column styles
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 60));

            // Set row styles
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 70));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));

            this.Controls.Add(mainPanel);
            this.AcceptButton = _selectButton;
            this.CancelButton = _cancelButton;
        }

        private void LoadProfiles()
        {
            _profileListBox.Items.Clear();
            foreach (var profile in Profiles)
            {
                _profileListBox.Items.Add(profile);
            }
        }

        private void ProfileListBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            var selectedProfile = _profileListBox.SelectedItem as ModProfile;
            if (selectedProfile != null)
            {
                _profileNameTextBox.Text = selectedProfile.Name;
                _profileDescriptionTextBox.Text = selectedProfile.Description ?? "";
                _modCountLabel.Text = $"Mods: {selectedProfile.Mods.Count}";

                _saveButton.Enabled = true;
                _deleteButton.Enabled = true;
                _duplicateButton.Enabled = true;
                _exportButton.Enabled = true;
                _selectButton.Enabled = true;
            }
            else
            {
                _profileNameTextBox.Clear();
                _profileDescriptionTextBox.Clear();
                _modCountLabel.Text = "Mods: 0";

                _saveButton.Enabled = false;
                _deleteButton.Enabled = false;
                _duplicateButton.Enabled = false;
                _exportButton.Enabled = false;
                _selectButton.Enabled = false;
            }
        }

        private void ProfileNameTextBox_TextChanged(object? sender, EventArgs e)
        {
            _saveButton.Enabled = !string.IsNullOrWhiteSpace(_profileNameTextBox.Text);
        }

        private void ProfileDescriptionTextBox_TextChanged(object? sender, EventArgs e)
        {
            // Enable save button when description changes
            _saveButton.Enabled = !string.IsNullOrWhiteSpace(_profileNameTextBox.Text);
        }

        private void NewButton_Click(object? sender, EventArgs e)
        {
            var newProfile = new ModProfile
            {
                Id = Guid.NewGuid().ToString(),
                Name = "New Profile",
                Description = "",
                CreatedDate = DateTime.Now,
                LastModified = DateTime.Now,
                Mods = new List<ModProfileEntry>()
            };

            Profiles.Add(newProfile);
            LoadProfiles();
            _profileListBox.SelectedItem = newProfile;
        }

        private void SaveButton_Click(object? sender, EventArgs e)
        {
            var selectedProfile = _profileListBox.SelectedItem as ModProfile;
            if (selectedProfile != null && !string.IsNullOrWhiteSpace(_profileNameTextBox.Text))
            {
                selectedProfile.Name = _profileNameTextBox.Text.Trim();
                selectedProfile.Description = _profileDescriptionTextBox.Text.Trim();
                selectedProfile.LastModified = DateTime.Now;
                
                LoadProfiles();
                _profileListBox.SelectedItem = selectedProfile;
                
                MessageBox.Show("Profile saved successfully!", "Profile Manager", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DeleteButton_Click(object? sender, EventArgs e)
        {
            var selectedProfile = _profileListBox.SelectedItem as ModProfile;
            if (selectedProfile != null)
            {
                var result = MessageBox.Show($"Are you sure you want to delete the profile '{selectedProfile.Name}'?", 
                    "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    Profiles.Remove(selectedProfile);
                    LoadProfiles();
                }
            }
        }

        private void DuplicateButton_Click(object? sender, EventArgs e)
        {
            var selectedProfile = _profileListBox.SelectedItem as ModProfile;
            if (selectedProfile != null)
            {
                var duplicateProfile = new ModProfile
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = selectedProfile.Name + " (Copy)",
                    Description = selectedProfile.Description,
                    CreatedDate = DateTime.Now,
                    LastModified = DateTime.Now,
                    Mods = new List<ModProfileEntry>(selectedProfile.Mods)
                };

                Profiles.Add(duplicateProfile);
                LoadProfiles();
                _profileListBox.SelectedItem = duplicateProfile;
            }
        }

        private void ExportButton_Click(object? sender, EventArgs e)
        {
            // TODO: Implement profile export functionality
            MessageBox.Show("Export functionality not yet implemented.", "Profile Manager", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ImportButton_Click(object? sender, EventArgs e)
        {
            // TODO: Implement profile import functionality
            MessageBox.Show("Import functionality not yet implemented.", "Profile Manager", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void SelectButton_Click(object? sender, EventArgs e)
        {
            SelectedProfile = _profileListBox.SelectedItem as ModProfile;
            this.DialogResult = DialogResult.OK;
            this.Close();
        }
    }
}
