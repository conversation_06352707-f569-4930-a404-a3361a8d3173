using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Security;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    // ── ZIP EXTRACTOR ──
    
    public class ZipExtractor : IArchiveExtractor
    {
        public bool CanExtract(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension == ".zip";
        }

        public System.Threading.Tasks.Task ExtractAsync(string archivePath, string extractPath)
        {
            try
            {
                // Secure path validation
                string canonicalExtractPath = Path.GetFullPath(extractPath);

                using var archive = ZipFile.OpenRead(archivePath);
                foreach (var entry in archive.Entries)
                {
                    // Prevent path traversal attacks
                    string destinationPath = Path.GetFullPath(Path.Combine(canonicalExtractPath, entry.FullName));
                    if (!destinationPath.StartsWith(canonicalExtractPath, StringComparison.OrdinalIgnoreCase))
                    {
                        throw new SecurityException($"Path traversal detected: {entry.FullName}");
                    }

                    // Skip directory entries
                    if (string.IsNullOrEmpty(entry.Name))
                    {
                        Directory.CreateDirectory(destinationPath);
                        continue;
                    }

                    // Ensure parent directory exists
                    Directory.CreateDirectory(Path.GetDirectoryName(destinationPath)!);

                    // Extract file
                    entry.ExtractToFile(destinationPath, true);
                }
                
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                return Task.FromException(new InvalidOperationException($"Failed to extract ZIP archive: {ex.Message}", ex));
            }
        }

        public Task<List<string>> ListContentsAsync(string archivePath)
        {
            try
            {
                var contents = new List<string>();

                using var archive = ZipFile.OpenRead(archivePath);
                contents.AddRange(archive.Entries.Select(entry => entry.FullName));
                
                return Task.FromResult(contents);
            }
            catch (Exception ex)
            {
                return Task.FromException<List<string>>(
                    new InvalidOperationException($"Failed to read ZIP archive contents: {ex.Message}", ex));
            }
        }

        public bool ValidateArchive(string archivePath)
        {
            try
            {
                using var archive = ZipFile.OpenRead(archivePath);
                // Try to read the first entry to validate the archive
                archive.Entries.FirstOrDefault();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }

    // ── 7-ZIP EXTRACTOR ──
    
    public class SevenZipExtractor : IArchiveExtractor
    {
        public bool CanExtract(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension == ".7z" || extension == ".tar" || extension == ".gz" || extension == ".xz";
        }

        public Task ExtractAsync(string archivePath, string extractPath)
        {
            return Task.Run(() =>
            {
                try
                {
                    // Secure path validation
                    string canonicalExtractPath = Path.GetFullPath(extractPath);
                    string sevenZipPath = Find7ZipExecutable();
                    
                    if (string.IsNullOrEmpty(sevenZipPath))
                    {
                        throw new FileNotFoundException("7-Zip executable not found. Please install 7-Zip.");
                    }

                    // Secure process execution
                    var startInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = sevenZipPath,
                        Arguments = $"x \"{archivePath}\" -o\"{canonicalExtractPath}\" -y",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    };

                    using var process = System.Diagnostics.Process.Start(startInfo);
                    if (process == null)
                        throw new InvalidOperationException("Failed to start 7-Zip process");

                    process.WaitForExit();

                    if (process.ExitCode != 0)
                    {
                        var error = process.StandardError.ReadToEnd();
                        throw new InvalidOperationException($"7-Zip extraction failed: {error}");
                    }

                    // Validate extracted files don't escape destination
                    ValidateExtractedPaths(canonicalExtractPath);
                }
                catch (Exception ex) when (!(ex is FileNotFoundException))
                {
                    throw new InvalidOperationException($"Failed to extract 7z archive: {ex.Message}", ex);
                }
            });
        }

        public Task<List<string>> ListContentsAsync(string archivePath)
        {
            return Task.Run(() =>
            {
                var contents = new List<string>();
                
                try
                {
                    string sevenZipPath = Find7ZipExecutable();
                    
                    if (string.IsNullOrEmpty(sevenZipPath))
                    {
                        throw new FileNotFoundException("7-Zip executable not found.");
                    }

                    var startInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = sevenZipPath,
                        Arguments = $"l \"{archivePath}\"",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true
                    };

                    using var process = System.Diagnostics.Process.Start(startInfo);
                    if (process == null)
                        throw new InvalidOperationException("Failed to start 7-Zip process");

                    var output = process.StandardOutput.ReadToEnd();
                    process.WaitForExit();

                    if (process.ExitCode == 0)
                    {
                        // Parse 7z list output to extract file names
                        var lines = output.Split('\n');
                        bool inFilesList = false;
                        
                        foreach (var line in lines)
                        {
                            if (line.Contains("Name"))
                            {
                                inFilesList = true;
                                continue;
                            }
                            
                            if (inFilesList && !string.IsNullOrWhiteSpace(line) && !line.Contains("--"))
                            {
                                var parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                                if (parts.Length > 0)
                                {
                                    contents.Add(parts[parts.Length - 1]);
                                }
                            }
                        }
                    }
                    else
                    {
                        var error = process.StandardError.ReadToEnd();
                        throw new InvalidOperationException($"7-Zip listing failed: {error}");
                    }
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Failed to list 7z archive contents: {ex.Message}", ex);
                }

                return contents;
            });
        }

        public bool ValidateArchive(string archivePath)
        {
            try
            {
                string sevenZipPath = Find7ZipExecutable();
                if (string.IsNullOrEmpty(sevenZipPath))
                    return false;

                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = sevenZipPath,
                    Arguments = $"t \"{archivePath}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                if (process == null)
                    return false;

                process.WaitForExit();
                return process.ExitCode == 0;
            }
            catch
            {
                return false;
            }
        }

        private void ValidateExtractedPaths(string basePath)
        {
            var extractedFiles = Directory.GetFiles(basePath, "*", SearchOption.AllDirectories);
            foreach (var file in extractedFiles)
            {
                string canonicalPath = Path.GetFullPath(file);
                if (!canonicalPath.StartsWith(basePath, StringComparison.OrdinalIgnoreCase))
                {
                    throw new SecurityException($"Path traversal detected in extracted file: {file}");
                }
            }
        }

        private string Find7ZipExecutable()
        {
            var possiblePaths = new[]
            {
                @"C:\Program Files\7-Zip\7z.exe",
                @"C:\Program Files (x86)\7-Zip\7z.exe"
            };

            foreach (var path in possiblePaths)
            {
                if (File.Exists(path))
                    return path;
            }

            // Try to find in PATH
            try
            {
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "where",
                    Arguments = "7z.exe",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                if (process != null)
                {
                    var output = process.StandardOutput.ReadToEnd().Trim();
                    process.WaitForExit();
                    
                    if (process.ExitCode == 0 && File.Exists(output))
                        return output;
                }
            }
            catch
            {
                // Ignore errors when trying to find in PATH
            }

            return string.Empty;
        }
    }

    // ── RAR EXTRACTOR ──
    
    public class RarExtractor : IArchiveExtractor
    {
        public bool CanExtract(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension == ".rar";
        }

        public Task ExtractAsync(string archivePath, string extractPath)
        {
            return Task.Run(() =>
            {
                try
                {
                    // Secure path validation
                    string canonicalExtractPath = Path.GetFullPath(extractPath);
                    string winRarPath = FindWinRarExecutable();
                    
                    if (string.IsNullOrEmpty(winRarPath))
                    {
                        throw new FileNotFoundException("WinRAR executable not found. Please install WinRAR or use a different archive format.");
                    }

                    // Secure process execution
                    var startInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = winRarPath,
                        Arguments = $"x -y \"{archivePath}\" \"{canonicalExtractPath}\\\"",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    };

                    using var process = System.Diagnostics.Process.Start(startInfo);
                    if (process == null)
                        throw new InvalidOperationException("Failed to start WinRAR process");

                    process.WaitForExit();

                    if (process.ExitCode != 0)
                    {
                        var error = process.StandardError.ReadToEnd();
                        throw new InvalidOperationException($"WinRAR extraction failed: {error}");
                    }

                    // Validate extracted files don't escape destination
                    ValidateExtractedPaths(canonicalExtractPath);
                }
                catch (Exception ex) when (!(ex is FileNotFoundException))
                {
                    throw new InvalidOperationException($"Failed to extract RAR archive: {ex.Message}", ex);
                }
            });
        }

        public Task<List<string>> ListContentsAsync(string archivePath)
        {
            return Task.Run(() =>
            {
                var contents = new List<string>();
                
                try
                {
                    string winRarPath = FindWinRarExecutable();
                    
                    if (string.IsNullOrEmpty(winRarPath))
                    {
                        throw new FileNotFoundException("WinRAR executable not found.");
                    }

                    var startInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = winRarPath,
                        Arguments = $"lb \"{archivePath}\"",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true
                    };

                    using var process = System.Diagnostics.Process.Start(startInfo);
                    if (process == null)
                        throw new InvalidOperationException("Failed to start WinRAR process");

                    var output = process.StandardOutput.ReadToEnd();
                    process.WaitForExit();

                    if (process.ExitCode == 0)
                    {
                        // Parse WinRAR list output to extract file names
                        var lines = output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                        contents.AddRange(lines);
                    }
                    else
                    {
                        var error = process.StandardError.ReadToEnd();
                        throw new InvalidOperationException($"WinRAR listing failed: {error}");
                    }
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Failed to list RAR archive contents: {ex.Message}", ex);
                }

                return contents;
            });
        }

        public bool ValidateArchive(string archivePath)
        {
            try
            {
                string winRarPath = FindWinRarExecutable();
                if (string.IsNullOrEmpty(winRarPath))
                    return false;

                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = winRarPath,
                    Arguments = $"t \"{archivePath}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                if (process == null)
                    return false;

                process.WaitForExit();
                return process.ExitCode == 0;
            }
            catch
            {
                return false;
            }
        }

        private void ValidateExtractedPaths(string basePath)
        {
            var extractedFiles = Directory.GetFiles(basePath, "*", SearchOption.AllDirectories);
            foreach (var file in extractedFiles)
            {
                string canonicalPath = Path.GetFullPath(file);
                if (!canonicalPath.StartsWith(basePath, StringComparison.OrdinalIgnoreCase))
                {
                    throw new SecurityException($"Path traversal detected in extracted file: {file}");
                }
            }
        }

        private string FindWinRarExecutable()
        {
            var possiblePaths = new[]
            {
                @"C:\Program Files\WinRAR\WinRAR.exe",
                @"C:\Program Files (x86)\WinRAR\WinRAR.exe",
                @"C:\Program Files\WinRAR\Rar.exe",
                @"C:\Program Files (x86)\WinRAR\Rar.exe"
            };

            foreach (var path in possiblePaths)
            {
                if (File.Exists(path))
                    return path;
            }

            return string.Empty;
        }
    }

    // ── ARCHIVE FORMAT DETECTOR ──
    
    public static class ArchiveFormatDetector
    {
        private static readonly Dictionary<string, byte[]> FileSignatures = new()
        {
            // ZIP file signatures
            [".zip"] = new byte[] { 0x50, 0x4B, 0x03, 0x04 }, // PK..
            // RAR file signatures
            [".rar"] = new byte[] { 0x52, 0x61, 0x72, 0x21, 0x1A, 0x07, 0x00 }, // Rar!...
            // 7z file signatures
            [".7z"] = new byte[] { 0x37, 0x7A, 0xBC, 0xAF, 0x27, 0x1C }, // 7z..'.
            // TAR file (often has various signatures)
            [".tar"] = new byte[] { 0x75, 0x73, 0x74, 0x61, 0x72 }, // ustar (at offset 257)
            // GZIP
            [".gz"] = new byte[] { 0x1F, 0x8B },
        };

        public static string DetectFormat(string filePath)
        {
            if (!File.Exists(filePath))
                return string.Empty;

            try
            {
                using var fs = File.OpenRead(filePath);
                var buffer = new byte[512]; // Read enough bytes to check various signatures
                var bytesRead = fs.Read(buffer, 0, buffer.Length);

                // Check file extension first (most reliable)
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                if (FileSignatures.ContainsKey(extension))
                {
                    var signature = FileSignatures[extension];
                    if (extension == ".tar")
                    {
                        // TAR signature is at offset 257
                        if (bytesRead > 262 && CheckSignature(buffer, signature, 257))
                            return extension;
                    }
                    else if (CheckSignature(buffer, signature, 0))
                    {
                        return extension;
                    }
                }

                // If extension doesn't match, try to detect by signature
                foreach (var kvp in FileSignatures)
                {
                    var signature = kvp.Value;
                    var format = kvp.Key;
                    
                    if (format == ".tar")
                    {
                        if (bytesRead > 262 && CheckSignature(buffer, signature, 257))
                            return format;
                    }
                    else if (CheckSignature(buffer, signature, 0))
                    {
                        return format;
                    }
                }

                return string.Empty;
            }
            catch
            {
                // If we can't read the file, fall back to extension
                return Path.GetExtension(filePath).ToLowerInvariant();
            }
        }

        private static bool CheckSignature(byte[] buffer, byte[] signature, int offset = 0)
        {
            if (buffer.Length < offset + signature.Length)
                return false;

            for (int i = 0; i < signature.Length; i++)
            {
                if (buffer[offset + i] != signature[i])
                    return false;
            }

            return true;
        }

        public static bool IsArchiveFile(string filePath)
        {
            var format = DetectFormat(filePath);
            return !string.IsNullOrEmpty(format);
        }

        public static IArchiveExtractor? GetExtractor(string filePath)
        {
            var format = DetectFormat(filePath);
            
            return format switch
            {
                ".zip" => new ZipExtractor(),
                ".7z" or ".tar" or ".gz" or ".xz" => new SevenZipExtractor(),
                ".rar" => new RarExtractor(),
                _ => null
            };
        }
    }

    // ── ARCHIVE SECURITY VALIDATION ──

    public class ArchiveSecurityValidator
    {
        private const long MAX_ARCHIVE_SIZE = 2L * 1024 * 1024 * 1024; // 2GB
        private const long MAX_EXTRACTED_SIZE = 10L * 1024 * 1024 * 1024; // 10GB
        private const int MAX_ENTRIES = 100000; // Maximum number of files
        private const double MAX_COMPRESSION_RATIO = 1000.0; // 1000:1 ratio
        private const int MAX_NESTED_DEPTH = 10; // Maximum directory nesting
        private const long MAX_SINGLE_FILE_SIZE = 1L * 1024 * 1024 * 1024; // 1GB per file

        public static async Task<ArchiveSecurityResult> ValidateArchiveSecurityAsync(string archivePath)
        {
            var result = new ArchiveSecurityResult { IsSecure = true };

            try
            {
                // Check archive file size
                var archiveSize = new FileInfo(archivePath).Length;
                if (archiveSize > MAX_ARCHIVE_SIZE)
                {
                    result.IsSecure = false;
                    result.SecurityIssues.Add($"Archive too large: {FormatFileSize(archiveSize)} exceeds limit of {FormatFileSize(MAX_ARCHIVE_SIZE)}");
                }

                // Get extractor for detailed analysis
                var extractor = ArchiveFormatDetector.GetExtractor(archivePath);
                if (extractor == null)
                {
                    result.IsSecure = false;
                    result.SecurityIssues.Add("Unsupported archive format");
                    return result;
                }

                // Analyze archive contents
                var contents = await extractor.ListContentsAsync(archivePath);

                // Check entry count
                if (contents.Count > MAX_ENTRIES)
                {
                    result.IsSecure = false;
                    result.SecurityIssues.Add($"Too many entries: {contents.Count} exceeds limit of {MAX_ENTRIES}");
                }

                long totalUncompressedSize = 0;
                int maxDepth = 0;

                foreach (var entry in contents)
                {
                    // Check for path traversal attempts
                    if (entry.Contains("..") || Path.IsPathRooted(entry))
                    {
                        result.IsSecure = false;
                        result.SecurityIssues.Add($"Suspicious path detected: {entry}");
                    }

                    // Check directory depth
                    var depth = entry.Split(new[] { '/', '\\' }, StringSplitOptions.RemoveEmptyEntries).Length;
                    maxDepth = Math.Max(maxDepth, depth);

                    // Estimate uncompressed size (this is approximate for non-ZIP formats)
                    if (extractor is ZipExtractor)
                    {
                        using var archive = ZipFile.OpenRead(archivePath);
                        var zipEntry = archive.Entries.FirstOrDefault(e => e.FullName == entry);
                        if (zipEntry != null)
                        {
                            totalUncompressedSize += zipEntry.Length;

                            // Check individual file size
                            if (zipEntry.Length > MAX_SINGLE_FILE_SIZE)
                            {
                                result.IsSecure = false;
                                result.SecurityIssues.Add($"File too large: {entry} ({FormatFileSize(zipEntry.Length)})");
                            }

                            // Check compression ratio for zip bombs
                            if (zipEntry.CompressedLength > 0)
                            {
                                var ratio = (double)zipEntry.Length / zipEntry.CompressedLength;
                                if (ratio > MAX_COMPRESSION_RATIO)
                                {
                                    result.IsSecure = false;
                                    result.SecurityIssues.Add($"Suspicious compression ratio: {entry} ({ratio:F1}:1)");
                                }
                            }
                        }
                    }
                }

                // Check total uncompressed size
                if (totalUncompressedSize > MAX_EXTRACTED_SIZE)
                {
                    result.IsSecure = false;
                    result.SecurityIssues.Add($"Total extracted size too large: {FormatFileSize(totalUncompressedSize)} exceeds limit of {FormatFileSize(MAX_EXTRACTED_SIZE)}");
                }

                // Check directory depth
                if (maxDepth > MAX_NESTED_DEPTH)
                {
                    result.IsSecure = false;
                    result.SecurityIssues.Add($"Directory nesting too deep: {maxDepth} levels exceeds limit of {MAX_NESTED_DEPTH}");
                }

                result.ArchiveSize = archiveSize;
                result.EstimatedExtractedSize = totalUncompressedSize;
                result.EntryCount = contents.Count;
                result.MaxDirectoryDepth = maxDepth;
            }
            catch (Exception ex)
            {
                result.IsSecure = false;
                result.SecurityIssues.Add($"Security validation failed: {ex.Message}");
            }

            return result;
        }

        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len /= 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }

    public class ArchiveSecurityResult
    {
        public bool IsSecure { get; set; } = true;
        public List<string> SecurityIssues { get; set; } = new();
        public long ArchiveSize { get; set; }
        public long EstimatedExtractedSize { get; set; }
        public int EntryCount { get; set; }
        public int MaxDirectoryDepth { get; set; }
        public List<string> SuspiciousFiles { get; set; } = new();
        public List<string> ExecutableFiles { get; set; } = new();
    }

    // ── MALICIOUS FILE DETECTION ──

    public static class MaliciousFileDetector
    {
        private static readonly HashSet<string> DangerousExtensions = new(StringComparer.OrdinalIgnoreCase)
        {
            ".exe", ".bat", ".cmd", ".com", ".scr", ".pif", ".vbs", ".vbe", ".js", ".jse",
            ".wsf", ".wsh", ".msi", ".msp", ".hta", ".cpl", ".jar", ".ps1", ".psm1",
            ".reg", ".inf", ".scf", ".lnk", ".url", ".dll", ".sys", ".drv"
        };

        private static readonly HashSet<string> AllowedModExtensions = new(StringComparer.OrdinalIgnoreCase)
        {
            ".pak", ".lua", ".txt", ".json", ".ini", ".cfg", ".xml", ".yml", ".yaml",
            ".md", ".readme", ".license", ".png", ".jpg", ".jpeg", ".gif", ".bmp",
            ".wav", ".mp3", ".ogg", ".zip", ".7z", ".rar"
        };

        private static readonly string[] SuspiciousPatterns = new[]
        {
            "autorun.inf",
            "desktop.ini",
            "thumbs.db",
            "system32",
            "windows",
            "program files",
            "programdata",
            "appdata",
            "temp",
            "tmp"
        };

        public static async Task<MaliciousFileResult> ScanArchiveAsync(string archivePath)
        {
            var result = new MaliciousFileResult { IsSafe = true };

            try
            {
                var extractor = ArchiveFormatDetector.GetExtractor(archivePath);
                if (extractor == null)
                {
                    result.IsSafe = false;
                    result.Issues.Add("Cannot analyze unknown archive format");
                    return result;
                }

                var contents = await extractor.ListContentsAsync(archivePath);

                foreach (var entry in contents)
                {
                    var fileName = Path.GetFileName(entry);
                    var extension = Path.GetExtension(entry);
                    var entryLower = entry.ToLowerInvariant();

                    // Check for dangerous file extensions
                    if (DangerousExtensions.Contains(extension))
                    {
                        result.IsSafe = false;
                        result.ExecutableFiles.Add(entry);
                        result.Issues.Add($"Executable file detected: {entry}");
                    }

                    // Check for suspicious file names
                    if (SuspiciousPatterns.Any(pattern => entryLower.Contains(pattern)))
                    {
                        result.IsSafe = false;
                        result.SuspiciousFiles.Add(entry);
                        result.Issues.Add($"Suspicious file path: {entry}");
                    }

                    // Check for files outside expected mod locations
                    if (!IsValidModPath(entry))
                    {
                        result.SuspiciousFiles.Add(entry);
                        result.Issues.Add($"File in unexpected location: {entry}");
                    }

                    // Check for hidden or system files
                    if (fileName.StartsWith(".") && !IsAllowedHiddenFile(fileName))
                    {
                        result.SuspiciousFiles.Add(entry);
                        result.Issues.Add($"Hidden file detected: {entry}");
                    }

                    // Check for double extensions (e.g., file.txt.exe)
                    if (HasDoubleExtension(fileName))
                    {
                        result.IsSafe = false;
                        result.SuspiciousFiles.Add(entry);
                        result.Issues.Add($"Double extension detected: {entry}");
                    }
                }

                result.TotalFiles = contents.Count;
                result.ExecutableCount = result.ExecutableFiles.Count;
                result.SuspiciousCount = result.SuspiciousFiles.Count;
            }
            catch (Exception ex)
            {
                result.IsSafe = false;
                result.Issues.Add($"Malicious file scan failed: {ex.Message}");
            }

            return result;
        }

        private static bool IsValidModPath(string path)
        {
            var pathLower = path.ToLowerInvariant();

            // Valid mod paths
            var validPaths = new[]
            {
                "pal/",
                "mods/",
                "ue4ss/",
                "palschema/",
                "content/",
                "binaries/",
                "~mods/",
                "logicmods/"
            };

            // Check if path starts with any valid mod directory
            if (validPaths.Any(validPath => pathLower.StartsWith(validPath)))
                return true;

            // Allow files in root directory if they have valid extensions
            if (!path.Contains('/') && !path.Contains('\\'))
            {
                var extension = Path.GetExtension(path);
                return AllowedModExtensions.Contains(extension) ||
                       string.IsNullOrEmpty(extension); // Allow extensionless files in root
            }

            return false;
        }

        private static bool IsAllowedHiddenFile(string fileName)
        {
            var allowedHiddenFiles = new[] { ".gitignore", ".gitattributes", ".editorconfig" };
            return allowedHiddenFiles.Contains(fileName.ToLowerInvariant());
        }

        private static bool HasDoubleExtension(string fileName)
        {
            var parts = fileName.Split('.');
            if (parts.Length < 3) return false;

            var lastExtension = "." + parts[^1];
            var secondLastExtension = "." + parts[^2];

            return DangerousExtensions.Contains(lastExtension) &&
                   AllowedModExtensions.Contains(secondLastExtension);
        }
    }

    public class MaliciousFileResult
    {
        public bool IsSafe { get; set; } = true;
        public List<string> Issues { get; set; } = new();
        public List<string> ExecutableFiles { get; set; } = new();
        public List<string> SuspiciousFiles { get; set; } = new();
        public int TotalFiles { get; set; }
        public int ExecutableCount { get; set; }
        public int SuspiciousCount { get; set; }
    }

    // ── ARCHIVE VALIDATION UTILITIES ──

    public static class ArchiveValidationUtils
    {
        public static async Task<ArchiveValidationResult> ValidateArchiveComprehensiveAsync(string archivePath)
        {
            var result = new ArchiveValidationResult { IsValid = true };

            try
            {
                // Basic integrity check
                var integrityValid = await ValidateArchiveIntegrity(archivePath);
                if (!integrityValid)
                {
                    result.IsValid = false;
                    result.Issues.Add("Archive integrity check failed");
                    return result;
                }

                // Security validation
                var securityResult = await ArchiveSecurityValidator.ValidateArchiveSecurityAsync(archivePath);
                if (!securityResult.IsSecure)
                {
                    result.IsValid = false;
                    result.Issues.AddRange(securityResult.SecurityIssues);
                    result.SecurityResult = securityResult;
                }

                // Malicious file detection
                var malwareResult = await MaliciousFileDetector.ScanArchiveAsync(archivePath);
                if (!malwareResult.IsSafe)
                {
                    result.IsValid = false;
                    result.Issues.AddRange(malwareResult.Issues);
                    result.MalwareResult = malwareResult;
                }

                // Content validation
                var contentResult = await ValidateArchiveContent(archivePath);
                if (!contentResult.IsValid)
                {
                    result.Issues.AddRange(contentResult.Issues);
                    // Content issues are warnings, not failures
                }
                result.ContentResult = contentResult;

                result.SecurityResult = securityResult;
                result.MalwareResult = malwareResult;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Issues.Add($"Validation failed: {ex.Message}");
            }

            return result;
        }

        public static async Task<bool> ValidateArchiveIntegrity(string archivePath)
        {
            var extractor = ArchiveFormatDetector.GetExtractor(archivePath);
            if (extractor == null)
                return false;

            try
            {
                // Use Task.Run to run the validation on a background thread since
                // ValidateArchive is a synchronous method that might be CPU-bound
                return await Task.Run(() => extractor.ValidateArchive(archivePath));
            }
            catch
            {
                return false;
            }
        }

        public static async Task<long> GetArchiveSize(string archivePath)
        {
            try
            {
                return await Task.Run(() => new FileInfo(archivePath).Length);
            }
            catch
            {
                return 0;
            }
        }

        public static async Task<int> CountArchiveEntries(string archivePath)
        {
            var extractor = ArchiveFormatDetector.GetExtractor(archivePath);
            if (extractor == null)
                return 0;

            try
            {
                var contents = await extractor.ListContentsAsync(archivePath);
                return contents.Count;
            }
            catch
            {
                return 0;
            }
        }

        public static async Task<bool> ContainsPalFolder(string archivePath)
        {
            var extractor = ArchiveFormatDetector.GetExtractor(archivePath);
            if (extractor == null)
                return false;

            try
            {
                var contents = await extractor.ListContentsAsync(archivePath);
                return contents.Any(entry => entry.Contains("Pal/") || entry.Contains("Pal\\"));
            }
            catch
            {
                return false;
            }
        }

        public static async Task<bool> ContainsUE4SSMods(string archivePath)
        {
            var extractor = ArchiveFormatDetector.GetExtractor(archivePath);
            if (extractor == null)
                return false;

            try
            {
                var contents = await extractor.ListContentsAsync(archivePath);
                return contents.Any(entry => entry.EndsWith(".lua", StringComparison.OrdinalIgnoreCase) ||
                                           entry.EndsWith("enabled.txt", StringComparison.OrdinalIgnoreCase));
            }
            catch
            {
                return false;
            }
        }

        public static async Task<bool> ContainsPakFiles(string archivePath)
        {
            var extractor = ArchiveFormatDetector.GetExtractor(archivePath);
            if (extractor == null)
                return false;

            try
            {
                var contents = await extractor.ListContentsAsync(archivePath);
                return contents.Any(entry => entry.EndsWith(".pak", StringComparison.OrdinalIgnoreCase));
            }
            catch
            {
                return false;
            }
        }

        private static async Task<ContentValidationResult> ValidateArchiveContent(string archivePath)
        {
            var result = new ContentValidationResult { IsValid = true };

            try
            {
                var extractor = ArchiveFormatDetector.GetExtractor(archivePath);
                if (extractor == null)
                {
                    result.IsValid = false;
                    result.Issues.Add("Unknown archive format");
                    return result;
                }

                var contents = await extractor.ListContentsAsync(archivePath);

                // Analyze mod structure
                var hasPalFolder = contents.Any(entry => entry.Contains("Pal/") || entry.Contains("Pal\\"));
                var hasUE4SSMods = contents.Any(entry => entry.EndsWith(".lua", StringComparison.OrdinalIgnoreCase));
                var hasPakFiles = contents.Any(entry => entry.EndsWith(".pak", StringComparison.OrdinalIgnoreCase));
                var hasPalSchemaMods = contents.Any(entry => entry.Contains("PalSchema") || entry.Contains("palschema"));

                if (!hasPalFolder && !hasUE4SSMods && !hasPakFiles && !hasPalSchemaMods)
                {
                    result.Issues.Add("Archive does not appear to contain valid Palworld mod content");
                }

                // Check for common mod file types
                var validModFiles = contents.Count(entry =>
                {
                    var ext = Path.GetExtension(entry).ToLowerInvariant();
                    return ext == ".pak" || ext == ".lua" || ext == ".json" || ext == ".ini" || ext == ".txt";
                });

                if (validModFiles == 0)
                {
                    result.Issues.Add("No recognized mod files found");
                }

                result.HasPalFolder = hasPalFolder;
                result.HasUE4SSMods = hasUE4SSMods;
                result.HasPakFiles = hasPakFiles;
                result.HasPalSchemaMods = hasPalSchemaMods;
                result.ValidModFileCount = validModFiles;
                result.TotalFileCount = contents.Count;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Issues.Add($"Content validation failed: {ex.Message}");
            }

            return result;
        }
    }

    // ── VALIDATION RESULT CLASSES ──

    public class ArchiveValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<string> Issues { get; set; } = new();
        public ArchiveSecurityResult? SecurityResult { get; set; }
        public MaliciousFileResult? MalwareResult { get; set; }
        public ContentValidationResult? ContentResult { get; set; }
    }

    public class ContentValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<string> Issues { get; set; } = new();
        public bool HasPalFolder { get; set; }
        public bool HasUE4SSMods { get; set; }
        public bool HasPakFiles { get; set; }
        public bool HasPalSchemaMods { get; set; }
        public int ValidModFileCount { get; set; }
        public int TotalFileCount { get; set; }
    }
}