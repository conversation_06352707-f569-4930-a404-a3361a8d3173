using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    /// <summary>
    /// Service for managing mod dependencies and conflict detection
    /// </summary>
    public class ModDependencyService
    {
        private readonly EnhancedLogger _logger;
        private readonly Dictionary<string, List<string>> _fileOwnership = new();

        public ModDependencyService(EnhancedLogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Analyzes dependencies for a list of mods
        /// </summary>
        public Task<List<ModDependency>> AnalyzeDependenciesAsync(List<ModItem> mods)
        {
            var dependencies = new List<ModDependency>();

            foreach (var mod in mods)
            {
                var modDependencies = ExtractModDependencies(mod);
                dependencies.AddRange(modDependencies);
            }

            // Resolve dependencies
            foreach (var dependency in dependencies)
            {
                dependency.IsSatisfied = ResolveDependency(dependency, mods);
            }

            return Task.FromResult(dependencies);
        }

        /// <summary>
        /// Detects conflicts between mods
        /// </summary>
        public async Task<List<ModConflict>> DetectConflictsAsync(List<ModItem> mods)
        {
            var conflicts = new List<ModConflict>();

            // Build file ownership map
            await BuildFileOwnershipMapAsync(mods);

            // Check for file conflicts
            conflicts.AddRange(DetectFileConflicts(mods));

            // Check for load order conflicts
            conflicts.AddRange(DetectLoadOrderConflicts(mods));

            // Check for incompatibility conflicts
            conflicts.AddRange(DetectIncompatibilityConflicts(mods));

            return conflicts;
        }

        /// <summary>
        /// Validates the load order of mods
        /// </summary>
        public LoadOrderValidationResult ValidateLoadOrder(List<ModItem> mods)
        {
            var result = new LoadOrderValidationResult();

            try
            {
                // Sort mods by load order
                var sortedMods = mods.Where(m => m.IsEnabled).OrderBy(m => m.LoadOrder).ToList();

                // Check for duplicate load orders
                var loadOrderGroups = sortedMods.GroupBy(m => m.LoadOrder).Where(g => g.Count() > 1);
                foreach (var group in loadOrderGroups)
                {
                    result.Warnings.Add($"Multiple mods have load order {group.Key}: {string.Join(", ", group.Select(m => m.Name))}");
                }

                // Check dependencies are loaded in correct order
                foreach (var mod in sortedMods)
                {
                    foreach (var dependency in mod.Dependencies.Where(d => d.Type == DependencyType.LoadOrder))
                    {
                        var dependencyMod = sortedMods.FirstOrDefault(m => m.Id == dependency.ModId);
                        if (dependencyMod != null && dependencyMod.LoadOrder >= mod.LoadOrder)
                        {
                            result.Errors.Add($"Mod '{mod.Name}' must be loaded after '{dependencyMod.Name}'");
                            result.IsValid = false;
                        }
                    }
                }

                // Check for unresolved dependencies
                foreach (var mod in sortedMods)
                {
                    foreach (var dependency in mod.Dependencies.Where(d => !d.IsSatisfied && !d.IsOptional))
                    {
                        result.UnresolvedDependencies.Add(dependency);
                        result.Errors.Add($"Mod '{mod.Name}' requires '{dependency.ModName}' but it is not available or enabled");
                        result.IsValid = false;
                    }
                }

                _logger.LogInfo($"Load order validation completed: {(result.IsValid ? "Valid" : "Invalid")} with {result.Errors.Count} errors and {result.Warnings.Count} warnings", "DependencyService");
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add($"Validation failed: {ex.Message}");
                _logger.LogError("Load order validation failed", "DependencyService", ex);
            }

            return result;
        }

        /// <summary>
        /// Suggests an optimal load order for the given mods
        /// </summary>
        public List<ModItem> SuggestLoadOrder(List<ModItem> mods)
        {
            var enabledMods = mods.Where(m => m.IsEnabled).ToList();
            var sortedMods = new List<ModItem>();
            var processed = new HashSet<string>();

            // Topological sort based on dependencies
            foreach (var mod in enabledMods)
            {
                AddModToSortedList(mod, enabledMods, sortedMods, processed);
            }

            // Assign load orders
            for (int i = 0; i < sortedMods.Count; i++)
            {
                sortedMods[i].LoadOrder = i + 1;
            }

            return sortedMods;
        }

        private List<ModDependency> ExtractModDependencies(ModItem mod)
        {
            var dependencies = new List<ModDependency>();

            try
            {
                // Check metadata for dependencies
                if (mod.Metadata.TryGetValue("dependencies", out var depsObj) && depsObj is JsonElement depsElement)
                {
                    if (depsElement.ValueKind == JsonValueKind.Array)
                    {
                        foreach (var depElement in depsElement.EnumerateArray())
                        {
                            var dependency = ParseDependencyFromJson(depElement);
                            if (dependency != null)
                            {
                                dependencies.Add(dependency);
                            }
                        }
                    }
                }

                // Check for implicit dependencies based on mod type
                dependencies.AddRange(GetImplicitDependencies(mod));
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Failed to extract dependencies for mod {mod.Name}: {ex.Message}", "DependencyService");
            }

            return dependencies;
        }

        private ModDependency? ParseDependencyFromJson(JsonElement element)
        {
            try
            {
                if (element.TryGetProperty("id", out var idElement) && element.TryGetProperty("name", out var nameElement))
                {
                    var dependency = new ModDependency
                    {
                        ModId = idElement.GetString() ?? "",
                        ModName = nameElement.GetString() ?? ""
                    };

                    if (element.TryGetProperty("version", out var versionElement))
                    {
                        dependency.RequiredVersion = versionElement.GetString() ?? "";
                    }

                    if (element.TryGetProperty("optional", out var optionalElement))
                    {
                        dependency.IsOptional = optionalElement.GetBoolean();
                    }

                    if (element.TryGetProperty("type", out var typeElement))
                    {
                        var typeString = typeElement.GetString();
                        if (Enum.TryParse<DependencyType>(typeString, true, out var depType))
                        {
                            dependency.Type = depType;
                        }
                    }

                    return dependency;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Failed to parse dependency from JSON: {ex.Message}", "DependencyService");
            }

            return null;
        }

        private List<ModDependency> GetImplicitDependencies(ModItem mod)
        {
            var dependencies = new List<ModDependency>();

            // UE4SS mods depend on UE4SS being installed
            if (mod.ModType == ModType.UE4SSMod)
            {
                dependencies.Add(new ModDependency
                {
                    ModId = "ue4ss",
                    ModName = "UE4SS",
                    RequiredVersion = "3.0.0",
                    Type = DependencyType.Hard,
                    IsOptional = false
                });
            }

            return dependencies;
        }

        private bool ResolveDependency(ModDependency dependency, List<ModItem> availableMods)
        {
            var dependencyMod = availableMods.FirstOrDefault(m => 
                m.Id == dependency.ModId || 
                m.Name.Equals(dependency.ModName, StringComparison.OrdinalIgnoreCase));

            if (dependencyMod == null)
                return false;

            if (!dependencyMod.IsEnabled && !dependency.IsOptional)
                return false;

            // Check version if specified
            if (!string.IsNullOrEmpty(dependency.RequiredVersion))
            {
                // Simple version comparison - in a real implementation, use proper version parsing
                if (!string.IsNullOrEmpty(dependencyMod.Version) && 
                    string.Compare(dependencyMod.Version, dependency.RequiredVersion, StringComparison.OrdinalIgnoreCase) < 0)
                {
                    return false;
                }
            }

            return true;
        }

        private async Task BuildFileOwnershipMapAsync(List<ModItem> mods)
        {
            _fileOwnership.Clear();

            await Task.Run(() =>
            {
                foreach (var mod in mods.Where(m => m.IsEnabled))
                {
                    // Get files from installation path
                    if (Directory.Exists(mod.InstallationPath))
                    {
                        var files = Directory.GetFiles(mod.InstallationPath, "*", SearchOption.AllDirectories);
                        foreach (var file in files)
                        {
                            var relativePath = Path.GetRelativePath(mod.InstallationPath, file);
                            
                            if (!_fileOwnership.ContainsKey(relativePath))
                            {
                                _fileOwnership[relativePath] = new List<string>();
                            }
                            
                            _fileOwnership[relativePath].Add(mod.Id);
                        }
                    }
                }
            });
        }

        private List<ModConflict> DetectFileConflicts(List<ModItem> mods)
        {
            var conflicts = new List<ModConflict>();

            foreach (var fileOwners in _fileOwnership.Where(kvp => kvp.Value.Count > 1))
            {
                var file = fileOwners.Key;
                var owners = fileOwners.Value;

                for (int i = 0; i < owners.Count - 1; i++)
                {
                    for (int j = i + 1; j < owners.Count; j++)
                    {
                        var mod1 = mods.FirstOrDefault(m => m.Id == owners[i]);
                        var mod2 = mods.FirstOrDefault(m => m.Id == owners[j]);

                        if (mod1 != null && mod2 != null)
                        {
                            conflicts.Add(new ModConflict
                            {
                                ConflictingModId = mod2.Id,
                                ConflictingModName = mod2.Name,
                                Type = ConflictType.FileOverwrite,
                                Description = $"Both mods modify the file: {file}",
                                ConflictingFiles = new List<string> { file },
                                Severity = DetermineConflictSeverity(file),
                                CanAutoResolve = false
                            });
                        }
                    }
                }
            }

            return conflicts;
        }

        private List<ModConflict> DetectLoadOrderConflicts(List<ModItem> mods)
        {
            var conflicts = new List<ModConflict>();

            // Check for circular dependencies
            foreach (var mod in mods.Where(m => m.IsEnabled))
            {
                if (HasCircularDependency(mod, mods, new HashSet<string>()))
                {
                    conflicts.Add(new ModConflict
                    {
                        ConflictingModId = mod.Id,
                        ConflictingModName = mod.Name,
                        Type = ConflictType.LoadOrder,
                        Description = "Circular dependency detected",
                        Severity = ConflictSeverity.High,
                        CanAutoResolve = false
                    });
                }
            }

            return conflicts;
        }

        private List<ModConflict> DetectIncompatibilityConflicts(List<ModItem> mods)
        {
            var conflicts = new List<ModConflict>();

            // Check metadata for known incompatibilities
            foreach (var mod in mods.Where(m => m.IsEnabled))
            {
                if (mod.Metadata.TryGetValue("incompatible", out var incompatibleObj) && incompatibleObj is JsonElement incompatibleElement)
                {
                    if (incompatibleElement.ValueKind == JsonValueKind.Array)
                    {
                        foreach (var incompatibleModElement in incompatibleElement.EnumerateArray())
                        {
                            var incompatibleModId = incompatibleModElement.GetString();
                            var conflictingMod = mods.FirstOrDefault(m => m.Id == incompatibleModId && m.IsEnabled);
                            
                            if (conflictingMod != null)
                            {
                                conflicts.Add(new ModConflict
                                {
                                    ConflictingModId = conflictingMod.Id,
                                    ConflictingModName = conflictingMod.Name,
                                    Type = ConflictType.Incompatible,
                                    Description = $"Mod '{mod.Name}' is incompatible with '{conflictingMod.Name}'",
                                    Severity = ConflictSeverity.High,
                                    CanAutoResolve = false
                                });
                            }
                        }
                    }
                }
            }

            return conflicts;
        }

        private ConflictSeverity DetermineConflictSeverity(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            var fileName = Path.GetFileName(filePath).ToLowerInvariant();

            // Critical files
            if (fileName.Contains("main") || fileName.Contains("core") || extension == ".exe" || extension == ".dll")
                return ConflictSeverity.Critical;

            // High severity files
            if (extension == ".pak" || fileName.Contains("config") || fileName.Contains("setting"))
                return ConflictSeverity.High;

            // Medium severity files
            if (extension == ".lua" || extension == ".json" || extension == ".xml")
                return ConflictSeverity.Medium;

            // Low severity files (textures, sounds, etc.)
            return ConflictSeverity.Low;
        }

        private bool HasCircularDependency(ModItem mod, List<ModItem> allMods, HashSet<string> visited)
        {
            if (visited.Contains(mod.Id))
                return true;

            visited.Add(mod.Id);

            foreach (var dependency in mod.Dependencies)
            {
                var dependencyMod = allMods.FirstOrDefault(m => m.Id == dependency.ModId);
                if (dependencyMod != null && HasCircularDependency(dependencyMod, allMods, visited))
                {
                    return true;
                }
            }

            visited.Remove(mod.Id);
            return false;
        }

        private void AddModToSortedList(ModItem mod, List<ModItem> allMods, List<ModItem> sortedMods, HashSet<string> processed)
        {
            if (processed.Contains(mod.Id))
                return;

            // Add dependencies first
            foreach (var dependency in mod.Dependencies.Where(d => d.Type == DependencyType.LoadOrder))
            {
                var dependencyMod = allMods.FirstOrDefault(m => m.Id == dependency.ModId);
                if (dependencyMod != null && dependencyMod.IsEnabled)
                {
                    AddModToSortedList(dependencyMod, allMods, sortedMods, processed);
                }
            }

            sortedMods.Add(mod);
            processed.Add(mod.Id);
        }
    }
}
