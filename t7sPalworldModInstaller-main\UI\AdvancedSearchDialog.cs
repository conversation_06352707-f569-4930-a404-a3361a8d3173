using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ModInstallerApp.Models;

namespace ModInstallerApp.UI
{
    public partial class AdvancedSearchDialog : Form
    {
        public SearchCriteria SearchCriteria { get; private set; }

        private TextBox _nameTextBox = null!;
        private TextBox _authorTextBox = null!;
        private ComboBox _typeComboBox = null!;
        private ComboBox _statusComboBox = null!;
        private DateTimePicker _installedAfterPicker = null!;
        private DateTimePicker _installedBeforePicker = null!;
        private CheckBox _hasConflictsCheckBox = null!;
        private CheckBox _hasUpdatesCheckBox = null!;
        private TextBox _tagsTextBox = null!;
        private Button _searchButton = null!;
        private Button _cancelButton = null!;
        private Button _clearButton = null!;

        public AdvancedSearchDialog()
        {
            InitializeComponent();
            SearchCriteria = new SearchCriteria();
        }

        private void InitializeComponent()
        {
            this.Text = "Advanced Search";
            this.Size = new Size(450, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 10,
                Padding = new Padding(10)
            };

            // Name search
            mainPanel.Controls.Add(new Label { Text = "Name contains:", Anchor = AnchorStyles.Left }, 0, 0);
            _nameTextBox = new TextBox { Dock = DockStyle.Fill };
            mainPanel.Controls.Add(_nameTextBox, 1, 0);

            // Author search
            mainPanel.Controls.Add(new Label { Text = "Author:", Anchor = AnchorStyles.Left }, 0, 1);
            _authorTextBox = new TextBox { Dock = DockStyle.Fill };
            mainPanel.Controls.Add(_authorTextBox, 1, 1);

            // Type filter
            mainPanel.Controls.Add(new Label { Text = "Type:", Anchor = AnchorStyles.Left }, 0, 2);
            _typeComboBox = new ComboBox 
            { 
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _typeComboBox.Items.AddRange(new[] { "All", "UE4SS", "PalSchema", "PAK", "Mixed" });
            _typeComboBox.SelectedIndex = 0;
            mainPanel.Controls.Add(_typeComboBox, 1, 2);

            // Status filter
            mainPanel.Controls.Add(new Label { Text = "Status:", Anchor = AnchorStyles.Left }, 0, 3);
            _statusComboBox = new ComboBox 
            { 
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _statusComboBox.Items.AddRange(new[] { "All", "Enabled", "Disabled", "Conflicted", "Outdated" });
            _statusComboBox.SelectedIndex = 0;
            mainPanel.Controls.Add(_statusComboBox, 1, 3);

            // Installed after
            mainPanel.Controls.Add(new Label { Text = "Installed after:", Anchor = AnchorStyles.Left }, 0, 4);
            _installedAfterPicker = new DateTimePicker 
            { 
                Dock = DockStyle.Fill,
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now.AddMonths(-1)
            };
            mainPanel.Controls.Add(_installedAfterPicker, 1, 4);

            // Installed before
            mainPanel.Controls.Add(new Label { Text = "Installed before:", Anchor = AnchorStyles.Left }, 0, 5);
            _installedBeforePicker = new DateTimePicker 
            { 
                Dock = DockStyle.Fill,
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };
            mainPanel.Controls.Add(_installedBeforePicker, 1, 5);

            // Has conflicts
            mainPanel.Controls.Add(new Label { Text = "Has conflicts:", Anchor = AnchorStyles.Left }, 0, 6);
            _hasConflictsCheckBox = new CheckBox { Dock = DockStyle.Fill };
            mainPanel.Controls.Add(_hasConflictsCheckBox, 1, 6);

            // Has updates
            mainPanel.Controls.Add(new Label { Text = "Has updates:", Anchor = AnchorStyles.Left }, 0, 7);
            _hasUpdatesCheckBox = new CheckBox { Dock = DockStyle.Fill };
            mainPanel.Controls.Add(_hasUpdatesCheckBox, 1, 7);

            // Tags
            mainPanel.Controls.Add(new Label { Text = "Tags (comma-separated):", Anchor = AnchorStyles.Left }, 0, 8);
            _tagsTextBox = new TextBox { Dock = DockStyle.Fill };
            mainPanel.Controls.Add(_tagsTextBox, 1, 8);

            // Buttons
            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft,
                Padding = new Padding(5)
            };

            _cancelButton = new Button
            {
                Text = "Cancel",
                Size = new Size(75, 25),
                DialogResult = DialogResult.Cancel
            };
            _cancelButton.Click += (s, e) => this.Close();

            _searchButton = new Button
            {
                Text = "Search",
                Size = new Size(75, 25),
                DialogResult = DialogResult.OK
            };
            _searchButton.Click += SearchButton_Click;

            _clearButton = new Button
            {
                Text = "Clear",
                Size = new Size(75, 25)
            };
            _clearButton.Click += ClearButton_Click;

            buttonPanel.Controls.Add(_cancelButton);
            buttonPanel.Controls.Add(_searchButton);
            buttonPanel.Controls.Add(_clearButton);

            mainPanel.Controls.Add(buttonPanel, 1, 9);

            // Set column styles
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // Set row styles
            for (int i = 0; i < 9; i++)
            {
                mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));
            }
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));

            this.Controls.Add(mainPanel);
            this.AcceptButton = _searchButton;
            this.CancelButton = _cancelButton;
        }

        private void SearchButton_Click(object? sender, EventArgs e)
        {
            SearchCriteria = new SearchCriteria
            {
                NameContains = string.IsNullOrWhiteSpace(_nameTextBox.Text) ? null : _nameTextBox.Text.Trim(),
                Author = string.IsNullOrWhiteSpace(_authorTextBox.Text) ? null : _authorTextBox.Text.Trim(),
                ModType = _typeComboBox.SelectedIndex == 0 ? null : _typeComboBox.SelectedItem?.ToString(),
                Status = _statusComboBox.SelectedIndex == 0 ? null : _statusComboBox.SelectedItem?.ToString(),
                InstalledAfter = _installedAfterPicker.Value.Date,
                InstalledBefore = _installedBeforePicker.Value.Date.AddDays(1).AddTicks(-1),
                HasConflicts = _hasConflictsCheckBox.Checked ? true : null,
                HasUpdates = _hasUpdatesCheckBox.Checked ? true : null,
                Tags = string.IsNullOrWhiteSpace(_tagsTextBox.Text) ? 
                    new List<string>() : 
                    _tagsTextBox.Text.Split(',').Select(t => t.Trim()).Where(t => !string.IsNullOrEmpty(t)).ToList()
            };

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void ClearButton_Click(object? sender, EventArgs e)
        {
            _nameTextBox.Clear();
            _authorTextBox.Clear();
            _typeComboBox.SelectedIndex = 0;
            _statusComboBox.SelectedIndex = 0;
            _installedAfterPicker.Value = DateTime.Now.AddMonths(-1);
            _installedBeforePicker.Value = DateTime.Now;
            _hasConflictsCheckBox.Checked = false;
            _hasUpdatesCheckBox.Checked = false;
            _tagsTextBox.Clear();
        }

        public void LoadFromFilter(ModFilter filter)
        {
            if (filter == null) return;

            _nameTextBox.Text = filter.NameContains ?? "";
            _authorTextBox.Text = filter.Author ?? "";

            // Set type combo box
            if (!string.IsNullOrEmpty(filter.ModType))
            {
                var index = _typeComboBox.Items.IndexOf(filter.ModType);
                _typeComboBox.SelectedIndex = index >= 0 ? index : 0;
            }
            else
            {
                _typeComboBox.SelectedIndex = 0;
            }

            // Set status combo box
            if (!string.IsNullOrEmpty(filter.Status))
            {
                var index = _statusComboBox.Items.IndexOf(filter.Status);
                _statusComboBox.SelectedIndex = index >= 0 ? index : 0;
            }
            else
            {
                _statusComboBox.SelectedIndex = 0;
            }

            if (filter.InstalledAfter.HasValue)
                _installedAfterPicker.Value = filter.InstalledAfter.Value;

            if (filter.InstalledBefore.HasValue)
                _installedBeforePicker.Value = filter.InstalledBefore.Value;

            _hasConflictsCheckBox.Checked = filter.HasConflicts ?? false;
            _hasUpdatesCheckBox.Checked = filter.HasUpdates ?? false;
            _tagsTextBox.Text = filter.Tags != null ? string.Join(", ", filter.Tags) : "";
        }

        public ModFilter Filter
        {
            get
            {
                return new ModFilter
                {
                    NameContains = string.IsNullOrWhiteSpace(_nameTextBox.Text) ? null : _nameTextBox.Text.Trim(),
                    Author = string.IsNullOrWhiteSpace(_authorTextBox.Text) ? null : _authorTextBox.Text.Trim(),
                    ModType = _typeComboBox.SelectedIndex == 0 ? null : _typeComboBox.SelectedItem?.ToString(),
                    Status = _statusComboBox.SelectedIndex == 0 ? null : _statusComboBox.SelectedItem?.ToString(),
                    InstalledAfter = _installedAfterPicker.Value.Date,
                    InstalledBefore = _installedBeforePicker.Value.Date.AddDays(1).AddTicks(-1),
                    HasConflicts = _hasConflictsCheckBox.Checked ? true : null,
                    HasUpdates = _hasUpdatesCheckBox.Checked ? true : null,
                    Tags = string.IsNullOrWhiteSpace(_tagsTextBox.Text) ?
                        new List<string>() :
                        _tagsTextBox.Text.Split(',').Select(t => t.Trim()).Where(t => !string.IsNullOrEmpty(t)).ToList()
                };
            }
        }
    }

    public class SearchCriteria
    {
        public string? NameContains { get; set; }
        public string? Author { get; set; }
        public string? ModType { get; set; }
        public string? Status { get; set; }
        public DateTime? InstalledAfter { get; set; }
        public DateTime? InstalledBefore { get; set; }
        public bool? HasConflicts { get; set; }
        public bool? HasUpdates { get; set; }
        public List<string> Tags { get; set; } = new();
    }
}
