using System;
using System.IO;
using System.Threading.Tasks;
using NUnit.Framework;
using FluentAssertions;
using ModInstallerApp.Services;
using ModInstallerApp.Models;
using ModInstallerApp.Tests.Mocks;

namespace ModInstallerApp.Tests.Services
{
    /// <summary>
    /// Comprehensive unit tests for UE4SSDetector service
    /// </summary>
    [TestFixture]
    public class UE4SSDetectorTests : TestBase
    {
        private UE4SSDetector? _detector;

        [SetUp]
        public override async Task SetUp()
        {
            await base.SetUp();
            _detector = new UE4SSDetector(TestPalworldRoot, TestCacheManager!);
        }

        [TearDown]
        public override async Task TearDown()
        {
            _detector?.Dispose();
            await base.TearDown();
        }

        [Test]
        public async Task DetectUE4SSAsync_WithFullInstallation_ReturnsFullyInstalled()
        {
            // Arrange - Basic structure already created in TestBase

            // Act
            var result = await _detector!.DetectUE4SSAsync();

            // Assert
            result.Should().NotBeNull();
            result.Status.Should().Be(UE4SSInstallStatus.FullyInstalled);
            result.CoreModsPresent.Should().Be(UE4SSDetector.RequiredCoreMods.Length);
            result.UserMods.Should().NotBeNull();
        }

        [Test]
        public async Task DetectUE4SSAsync_WithMissingProxyDLL_ReturnsPartiallyInstalled()
        {
            // Arrange
            var proxyDllPath = Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64", "dwmapi.dll");
            File.Delete(proxyDllPath);

            // Act
            var result = await _detector!.DetectUE4SSAsync();

            // Assert
            result.Status.Should().Be(UE4SSInstallStatus.PartiallyInstalled);
        }

        [Test]
        public async Task DetectUE4SSAsync_WithMissingUE4SSFolder_ReturnsNotInstalled()
        {
            // Arrange
            var ue4ssPath = Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64", "ue4ss");
            Directory.Delete(ue4ssPath, true);

            // Act
            var result = await _detector!.DetectUE4SSAsync();

            // Assert
            result.Status.Should().Be(UE4SSInstallStatus.NotInstalled);
        }

        [Test]
        public async Task DetectPalSchemaAsync_WithPalSchemaInstalled_DetectsPalSchema()
        {
            // Arrange - PalSchema structure already created in TestBase

            // Act
            var result = await _detector!.DetectPalSchemaAsync();

            // Assert
            result.IsInstalled.Should().BeTrue();
            result.Mods.Should().NotBeNull();
        }

        [Test]
        public async Task DetectPalSchemaAsync_WithMissingPalSchema_ReturnsNotInstalled()
        {
            // Arrange
            var palSchemaPath = Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods", "palschema");
            Directory.Delete(palSchemaPath, true);

            // Act
            var result = await _detector!.DetectPalSchemaAsync();

            // Assert
            result.IsInstalled.Should().BeFalse();
        }

        [Test]
        public async Task DetectUE4SSAsync_WithUserMods_DetectsUserMods()
        {
            // Arrange
            var userModPath = Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods", "TestUserMod");
            Directory.CreateDirectory(userModPath);
            await File.WriteAllTextAsync(Path.Combine(userModPath, "enabled.txt"), "");
            await File.WriteAllTextAsync(Path.Combine(userModPath, "main.lua"), "-- Test user mod");
            
            // Act
            var result = await _detector!.DetectUE4SSAsync();
            
            // Assert
            result.UserMods.Should().Contain("TestUserMod");
        }

        [Test]
        public void GetUE4SSPath_ReturnsCorrectPath()
        {
            // Act
            var path = _detector!.GetUE4SSPath();
            
            // Assert
            path.Should().Be(Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64", "ue4ss"));
        }

        [Test]
        public void GetModsPath_ReturnsCorrectPath()
        {
            // Act
            var path = _detector!.GetModsPath();
            
            // Assert
            path.Should().Be(Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods"));
        }

        [Test]
        public void GetPalSchemaPath_ReturnsCorrectPath()
        {
            // Act
            var path = _detector!.GetPalSchemaPath();
            
            // Assert
            path.Should().Be(Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods", "palschema"));
        }

        [Test]
        public async Task DetectUE4SSAsync_CachesResults()
        {
            // Arrange
            TestCacheManager!.ClearAll();
            
            // Act
            var result1 = await _detector!.DetectUE4SSAsync();
            var result2 = await _detector!.DetectUE4SSAsync();
            
            // Assert
            result1.Should().BeEquivalentTo(result2);
            // Verify cache was used (in a real implementation, we'd verify cache hit count)
        }

        [Test]
        public async Task DetectUE4SSAsync_InvalidatesCache_WhenFilesChange()
        {
            // Arrange
            var result1 = await _detector!.DetectUE4SSAsync();
            
            // Modify UE4SS structure
            var newModPath = Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods", "NewMod");
            Directory.CreateDirectory(newModPath);
            await File.WriteAllTextAsync(Path.Combine(newModPath, "enabled.txt"), "");
            
            // Act
            var result2 = await _detector!.DetectUE4SSAsync();
            
            // Assert
            result2.UserMods.Should().Contain("NewMod");
            result1.UserMods.Should().NotContain("NewMod");
        }

        [Test]
        public void Constructor_WithInvalidPath_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => new UE4SSDetector("", TestCacheManager!));
            Assert.Throws<ArgumentException>(() => new UE4SSDetector("invalid-path", TestCacheManager!));
        }

        [Test]
        public void Constructor_WithNullCache_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new UE4SSDetector(TestPalworldRoot, null!));
        }

        [Test]
        public async Task DetectUE4SSAsync_WithCorruptedModsJson_HandlesGracefully()
        {
            // Arrange
            var modsJsonPath = Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods", "mods.json");
            await File.WriteAllTextAsync(modsJsonPath, "invalid json content");
            
            // Act
            var result = await _detector!.DetectUE4SSAsync();
            
            // Assert
            result.Should().NotBeNull();
            // Should still detect other aspects correctly despite corrupted JSON
            result.Status.Should().Be(UE4SSInstallStatus.FullyInstalled);
        }

        [Test]
        public async Task DetectUE4SSAsync_WithMissingCoreMods_ReturnsPartiallyInstalled()
        {
            // Arrange
            var firstCoreModPath = Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods", UE4SSDetector.RequiredCoreMods[0]);
            Directory.Delete(firstCoreModPath, true);
            
            // Act
            var result = await _detector!.DetectUE4SSAsync();
            
            // Assert
            result.Status.Should().Be(UE4SSInstallStatus.PartiallyInstalled);
            result.CoreModsPresent.Should().Be(UE4SSDetector.RequiredCoreMods.Length - 1);
        }

        [Test]
        public async Task DetectUE4SSAsync_WithSymbolicLinks_HandlesCorrectly()
        {
            // Arrange
            var symlinkModPath = Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods", "SymlinkMod");
            
            try
            {
                // Create a symbolic link (if supported on the platform)
                Directory.CreateDirectory(symlinkModPath);
                await File.WriteAllTextAsync(Path.Combine(symlinkModPath, "enabled.txt"), "");
                
                // Act
                var result = await _detector!.DetectUE4SSAsync();
                
                // Assert
                result.UserMods.Should().Contain("SymlinkMod");
            }
            catch (UnauthorizedAccessException)
            {
                // Skip test if symbolic links are not supported
                Assert.Ignore("Symbolic links not supported in current environment");
            }
        }

        [Test]
        public void Dispose_DisposesResourcesProperly()
        {
            // Arrange
            var detector = new UE4SSDetector(TestPalworldRoot, TestCacheManager!);
            
            // Act
            detector.Dispose();
            
            // Assert
            // Verify that subsequent operations throw ObjectDisposedException
            Assert.ThrowsAsync<ObjectDisposedException>(async () => await detector.DetectUE4SSAsync());
        }
    }
}
