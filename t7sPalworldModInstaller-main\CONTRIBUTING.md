# Contributing to t7's Palworld Mod Installer

Thank you for your interest in contributing to the Palworld Mod Installer! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Reporting Issues
- Use the [GitHub Issues](https://github.com/tseven17/t7sPalworldModInstaller/issues) page
- Search existing issues before creating a new one
- Provide detailed information including:
  - Operating system and version
  - .NET version
  - Steps to reproduce
  - Expected vs actual behavior
  - Screenshots if applicable

### Suggesting Features
- Use [GitHub Discussions](https://github.com/tseven17/t7sPalworldModInstaller/discussions) for feature requests
- Explain the use case and benefits
- Consider implementation complexity
- Check the [roadmap](./roadmap.txt) for planned features

### Code Contributions

#### Prerequisites
- .NET 8.0 SDK
- Visual Studio 2022 or VS Code
- Git knowledge
- Familiarity with C# and Windows Forms

#### Development Setup
1. Fork the repository
2. Clone your fork:
   ```bash
   git clone https://github.com/yourusername/t7sPalworldModInstaller.git
   cd t7sPalworldModInstaller
   ```
3. Create a feature branch:
   ```bash
   git checkout -b feature/your-feature-name
   ```
4. Install dependencies:
   ```bash
   dotnet restore
   ```
5. Build and test:
   ```bash
   dotnet build
   dotnet test
   ```

#### Code Standards

##### Coding Style
- Follow Microsoft C# coding conventions
- Use meaningful variable and method names
- Add XML documentation for public APIs
- Keep methods focused and under 50 lines when possible
- Use async/await for I/O operations

##### Security Requirements
- Validate all user inputs
- Use parameterized queries/commands
- Implement proper error handling
- Follow principle of least privilege
- Never log sensitive information

##### Testing Requirements
- Write unit tests for new functionality
- Maintain test coverage above 80%
- Include integration tests for complex workflows
- Add security tests for security-sensitive code
- Use the existing test framework and patterns

#### Pull Request Process

1. **Before Submitting**
   - Ensure all tests pass: `dotnet test`
   - Run security analysis: `dotnet list package --vulnerable`
   - Check code formatting
   - Update documentation if needed

2. **Pull Request Guidelines**
   - Use a clear, descriptive title
   - Reference related issues: "Fixes #123"
   - Provide detailed description of changes
   - Include screenshots for UI changes
   - Keep PRs focused and reasonably sized

3. **Review Process**
   - All PRs require review from maintainers
   - Address feedback promptly
   - Keep discussions constructive
   - Be patient during the review process

## 🏗️ Architecture Guidelines

### Project Structure
```
├── Models/          # Data models and DTOs
├── Services/        # Business logic and core services
├── UI/             # User interface components
├── Tests/          # Comprehensive test suite
├── Extensions/     # Utility extensions
└── Interfaces/     # Service contracts
```

### Design Principles
- **Separation of Concerns**: Keep UI, business logic, and data access separate
- **Dependency Injection**: Use interfaces and DI for testability
- **Async/Await**: Use async patterns for I/O operations
- **Resource Management**: Implement IDisposable properly
- **Error Handling**: Use structured exception handling

### Service Layer Guidelines
- Implement interfaces for all services
- Use dependency injection
- Handle errors gracefully
- Log important operations
- Support cancellation tokens
- Provide progress reporting for long operations

### UI Guidelines
- Keep UI logic minimal
- Use async/await for long operations
- Provide user feedback (progress bars, status messages)
- Handle errors gracefully with user-friendly messages
- Follow Windows UI conventions

## 🧪 Testing Guidelines

### Test Categories
- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test component interactions
- **Security Tests**: Validate security measures
- **Performance Tests**: Benchmark critical operations
- **UI Tests**: Automated UI interaction testing

### Test Naming Convention
```csharp
[Test]
public void MethodName_Scenario_ExpectedBehavior()
{
    // Arrange
    // Act
    // Assert
}
```

### Mock Usage
- Use the existing mock services in `Tests/Mocks/`
- Create focused, minimal mocks
- Verify interactions when appropriate
- Use FluentAssertions for readable assertions

## 📝 Documentation

### Code Documentation
- Add XML documentation for public APIs
- Include usage examples for complex methods
- Document security considerations
- Explain performance implications

### User Documentation
- Update user guides for new features
- Include screenshots for UI changes
- Provide troubleshooting information
- Keep documentation current with code changes

## 🔒 Security Considerations

### Security Review Required For
- File system operations
- Process execution
- Network communications
- User input handling
- Configuration storage
- Archive extraction

### Security Best Practices
- Validate and sanitize all inputs
- Use secure file operations
- Implement proper authentication/authorization
- Follow OWASP guidelines
- Regular security dependency updates

## 📋 Release Process

### Version Numbering
- Follow Semantic Versioning (SemVer)
- Major.Minor.Patch format
- Update version in `.csproj` file

### Release Checklist
- [ ] All tests passing
- [ ] Security scan clean
- [ ] Documentation updated
- [ ] Performance benchmarks acceptable
- [ ] User acceptance testing completed

## 🎯 Areas Needing Contribution

### High Priority
- Performance optimizations
- Additional test coverage
- Documentation improvements
- Accessibility features

### Medium Priority
- UI/UX enhancements
- Additional mod format support
- Internationalization
- Plugin system development

### Low Priority
- Code refactoring
- Additional diagnostic tools
- Advanced configuration options

## 💬 Communication

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Discord**: Real-time community chat
- **Email**: <EMAIL> (security issues only)

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

## 🙏 Recognition

Contributors will be recognized in:
- README.md acknowledgments
- Release notes
- About dialog in the application

Thank you for helping make the Palworld Mod Installer better for everyone!
