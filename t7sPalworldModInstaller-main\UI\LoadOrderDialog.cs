using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ModInstallerApp.Models;

namespace ModInstallerApp.UI
{
    public partial class LoadOrderDialog : Form
    {
        public List<ModItem> ModLoadOrder { get; private set; }

        private ListBox _modListBox = null!;
        private Button _moveUpButton = null!;
        private Button _moveDownButton = null!;
        private Button _moveTopButton = null!;
        private Button _moveBottomButton = null!;
        private Button _autoSortButton = null!;
        private Button _resetButton = null!;
        private Button _saveButton = null!;
        private Button _cancelButton = null!;
        private Label _instructionsLabel = null!;
        private CheckBox _enableAutoSortCheckBox = null!;

        public LoadOrderDialog(List<ModItem> mods)
        {
            ModLoadOrder = new List<ModItem>(mods ?? new List<ModItem>());
            InitializeComponent();
            LoadMods();
        }

        private void InitializeComponent()
        {
            this.Text = "Mod Load Order";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(500, 400);

            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                Padding = new Padding(10)
            };

            // Instructions
            _instructionsLabel = new Label
            {
                Text = "Drag and drop mods to reorder them, or use the buttons. Mods at the top load first.",
                Dock = DockStyle.Fill,
                ForeColor = Color.Gray,
                AutoSize = false
            };
            mainPanel.Controls.Add(_instructionsLabel, 0, 0);
            mainPanel.SetColumnSpan(_instructionsLabel, 2);

            // Mod list
            var listPanel = new GroupBox
            {
                Text = "Load Order (Top = First to Load)",
                Dock = DockStyle.Fill
            };

            _modListBox = new ListBox
            {
                Dock = DockStyle.Fill,
                DisplayMember = "DisplayName",
                AllowDrop = true
            };
            _modListBox.SelectedIndexChanged += ModListBox_SelectedIndexChanged;
            _modListBox.DragEnter += ModListBox_DragEnter;
            _modListBox.DragDrop += ModListBox_DragDrop;
            _modListBox.MouseDown += ModListBox_MouseDown;

            listPanel.Controls.Add(_modListBox);
            mainPanel.Controls.Add(listPanel, 0, 1);
            mainPanel.SetRowSpan(listPanel, 2);

            // Control buttons
            var controlPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.TopDown,
                Padding = new Padding(5)
            };

            _moveTopButton = new Button
            {
                Text = "Move to Top",
                Size = new Size(100, 25),
                Enabled = false
            };
            _moveTopButton.Click += MoveTopButton_Click;

            _moveUpButton = new Button
            {
                Text = "Move Up",
                Size = new Size(100, 25),
                Enabled = false
            };
            _moveUpButton.Click += MoveUpButton_Click;

            _moveDownButton = new Button
            {
                Text = "Move Down",
                Size = new Size(100, 25),
                Enabled = false
            };
            _moveDownButton.Click += MoveDownButton_Click;

            _moveBottomButton = new Button
            {
                Text = "Move to Bottom",
                Size = new Size(100, 25),
                Enabled = false
            };
            _moveBottomButton.Click += MoveBottomButton_Click;

            controlPanel.Controls.Add(_moveTopButton);
            controlPanel.Controls.Add(_moveUpButton);
            controlPanel.Controls.Add(_moveDownButton);
            controlPanel.Controls.Add(_moveBottomButton);

            // Separator
            controlPanel.Controls.Add(new Label { Height = 10 });

            _autoSortButton = new Button
            {
                Text = "Auto Sort",
                Size = new Size(100, 25)
            };
            _autoSortButton.Click += AutoSortButton_Click;

            _resetButton = new Button
            {
                Text = "Reset Order",
                Size = new Size(100, 25)
            };
            _resetButton.Click += ResetButton_Click;

            controlPanel.Controls.Add(_autoSortButton);
            controlPanel.Controls.Add(_resetButton);

            // Auto-sort option
            controlPanel.Controls.Add(new Label { Height = 10 });
            _enableAutoSortCheckBox = new CheckBox
            {
                Text = "Enable auto-sort on startup",
                Size = new Size(120, 20)
            };
            controlPanel.Controls.Add(_enableAutoSortCheckBox);

            mainPanel.Controls.Add(controlPanel, 1, 1);

            // Dialog buttons
            var dialogButtonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft,
                Padding = new Padding(5)
            };

            _cancelButton = new Button
            {
                Text = "Cancel",
                Size = new Size(75, 25),
                DialogResult = DialogResult.Cancel
            };

            _saveButton = new Button
            {
                Text = "Save Order",
                Size = new Size(75, 25),
                DialogResult = DialogResult.OK
            };
            _saveButton.Click += SaveButton_Click;

            dialogButtonPanel.Controls.Add(_cancelButton);
            dialogButtonPanel.Controls.Add(_saveButton);

            mainPanel.Controls.Add(dialogButtonPanel, 1, 3);

            // Set column styles
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30));

            // Set row styles
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 70));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 30));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));

            this.Controls.Add(mainPanel);
            this.AcceptButton = _saveButton;
            this.CancelButton = _cancelButton;
        }

        private void LoadMods()
        {
            _modListBox.Items.Clear();
            foreach (var mod in ModLoadOrder)
            {
                _modListBox.Items.Add(mod);
            }
        }

        private void ModListBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            var hasSelection = _modListBox.SelectedIndex >= 0;
            var isFirst = _modListBox.SelectedIndex == 0;
            var isLast = _modListBox.SelectedIndex == _modListBox.Items.Count - 1;

            _moveTopButton.Enabled = hasSelection && !isFirst;
            _moveUpButton.Enabled = hasSelection && !isFirst;
            _moveDownButton.Enabled = hasSelection && !isLast;
            _moveBottomButton.Enabled = hasSelection && !isLast;
        }

        private void MoveTopButton_Click(object? sender, EventArgs e)
        {
            MoveSelectedMod(0);
        }

        private void MoveUpButton_Click(object? sender, EventArgs e)
        {
            var selectedIndex = _modListBox.SelectedIndex;
            if (selectedIndex > 0)
            {
                MoveSelectedMod(selectedIndex - 1);
            }
        }

        private void MoveDownButton_Click(object? sender, EventArgs e)
        {
            var selectedIndex = _modListBox.SelectedIndex;
            if (selectedIndex < _modListBox.Items.Count - 1)
            {
                MoveSelectedMod(selectedIndex + 1);
            }
        }

        private void MoveBottomButton_Click(object? sender, EventArgs e)
        {
            MoveSelectedMod(_modListBox.Items.Count - 1);
        }

        private void MoveSelectedMod(int newIndex)
        {
            var selectedIndex = _modListBox.SelectedIndex;
            if (selectedIndex >= 0 && newIndex >= 0 && newIndex < ModLoadOrder.Count)
            {
                var mod = ModLoadOrder[selectedIndex];
                ModLoadOrder.RemoveAt(selectedIndex);
                ModLoadOrder.Insert(newIndex, mod);
                
                LoadMods();
                _modListBox.SelectedIndex = newIndex;
            }
        }

        private void AutoSortButton_Click(object? sender, EventArgs e)
        {
            // Simple auto-sort by dependencies and mod type
            ModLoadOrder = ModLoadOrder
                .OrderBy(m => GetModTypePriority(m.ModType))
                .ThenBy(m => m.Name)
                .ToList();
            
            LoadMods();
            MessageBox.Show("Mods have been automatically sorted by type and name.", "Auto Sort", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private int GetModTypePriority(ModType modType)
        {
            return modType switch
            {
                ModType.UE4SSMod => 1,
                ModType.PalSchemaMod => 2,
                ModType.PakMod => 3,
                ModType.HybridMod => 4,
                _ => 5
            };
        }

        private void ResetButton_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to reset the load order to alphabetical?", 
                "Reset Load Order", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                ModLoadOrder = ModLoadOrder.OrderBy(m => m.Name).ToList();
                LoadMods();
            }
        }

        private void ModListBox_DragEnter(object? sender, DragEventArgs e)
        {
            if (e.Data?.GetDataPresent(DataFormats.Text) == true)
            {
                e.Effect = DragDropEffects.Move;
            }
        }

        private void ModListBox_DragDrop(object? sender, DragEventArgs e)
        {
            // Simple drag-drop implementation
            var point = _modListBox.PointToClient(new Point(e.X, e.Y));
            var index = _modListBox.IndexFromPoint(point);
            
            if (index >= 0 && _modListBox.SelectedIndex >= 0)
            {
                MoveSelectedMod(index);
            }
        }

        private void ModListBox_MouseDown(object? sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && _modListBox.SelectedItem != null)
            {
                _modListBox.DoDragDrop(_modListBox.SelectedItem.ToString() ?? "", DragDropEffects.Move);
            }
        }

        private void SaveButton_Click(object? sender, EventArgs e)
        {
            // Update load order indices
            for (int i = 0; i < ModLoadOrder.Count; i++)
            {
                ModLoadOrder[i].LoadOrder = i;
            }

            this.DialogResult = DialogResult.OK;
            this.Close();
        }
    }
}
