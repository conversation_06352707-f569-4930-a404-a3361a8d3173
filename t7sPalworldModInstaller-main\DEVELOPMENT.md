# Development Guide

This guide provides detailed information for developers working on the Palworld Mod Installer.

## 🏗️ Architecture Overview

### Core Components

#### Services Layer
- **UE4SSDetector**: Detects and manages UE4SS installations
- **CacheManager**: Thread-safe caching with disk persistence
- **EnhancedLogger**: Structured logging with privacy protection
- **SmartBackupEngine**: High-performance backup operations
- **ModManagerService**: Advanced mod management and conflict resolution
- **PalSchemaConfigurationService**: PalSchema configuration management

#### UI Layer
- **InstallerForm**: Main application window
- **ModManagerForm**: Advanced mod management interface
- **PalSchemaConfigurationDialog**: PalSchema configuration UI
- **DiagnosticToolsDialog**: Diagnostic and troubleshooting tools

#### Models
- **ApplicationModels**: Core application data structures
- **UE4SSModels**: UE4SS-specific models
- **ModManagementModels**: Mod management data structures
- **PalSchemaModels**: PalSchema configuration models

### Design Patterns

#### Dependency Injection
```csharp
// Service registration
services.AddSingleton<ICacheManager, CacheManager>();
services.AddScoped<IUE4SSDetector, UE4SSDetector>();

// Service consumption
public class ModManagerService
{
    private readonly IUE4SSDetector _ue4ssDetector;
    private readonly ICacheManager _cache;
    
    public ModManagerService(IUE4SSDetector ue4ssDetector, ICacheManager cache)
    {
        _ue4ssDetector = ue4ssDetector;
        _cache = cache;
    }
}
```

#### Async/Await Pattern
```csharp
public async Task<InstallationResult> InstallModAsync(
    string modPath, 
    CancellationToken cancellationToken = default,
    IProgress<InstallationProgress>? progress = null)
{
    try
    {
        progress?.Report(new InstallationProgress { Stage = "Validating", Percentage = 0 });
        
        // Async operations with proper cancellation support
        await ValidateModAsync(modPath, cancellationToken);
        
        progress?.Report(new InstallationProgress { Stage = "Installing", Percentage = 50 });
        
        var result = await PerformInstallationAsync(modPath, cancellationToken);
        
        progress?.Report(new InstallationProgress { Stage = "Complete", Percentage = 100 });
        
        return result;
    }
    catch (OperationCanceledException)
    {
        // Handle cancellation gracefully
        throw;
    }
    catch (Exception ex)
    {
        _logger.LogError($"Installation failed: {ex.Message}", "ModInstallation", ex);
        throw;
    }
}
```

#### Resource Management
```csharp
public class ResourceManagedService : IDisposable
{
    private readonly ReaderWriterLockSlim _lock = new();
    private bool _disposed = false;

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _lock?.Dispose();
            _disposed = true;
        }
    }
}
```

## 🔧 Development Environment

### Required Tools
- **Visual Studio 2022** (Community or higher) or **VS Code**
- **.NET 8.0 SDK**
- **Git** for version control
- **Windows 10/11** for testing

### Recommended Extensions (VS Code)
- C# Dev Kit
- NUnit Test Adapter
- GitLens
- SonarLint
- EditorConfig

### Project Configuration
```xml
<PropertyGroup>
  <TargetFramework>net8.0-windows</TargetFramework>
  <UseWindowsForms>true</UseWindowsForms>
  <Nullable>enable</Nullable>
  <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
</PropertyGroup>
```

## 🧪 Testing Framework

### Test Structure
```
Tests/
├── Services/           # Unit tests for services
├── Integration/        # Integration tests
├── Security/          # Security validation tests
├── ErrorRecovery/     # Error handling tests
├── Mocks/            # Mock implementations
└── TestBase.cs       # Base test class
```

### Test Base Class
```csharp
[TestFixture]
public abstract class TestBase : IDisposable
{
    protected TestEnvironment TestEnvironment { get; private set; }
    
    [SetUp]
    public virtual void Setup()
    {
        TestEnvironment = CreateTestEnvironment();
    }
    
    [TearDown]
    public virtual void TearDown()
    {
        TestEnvironment?.Dispose();
    }
}
```

### Mock Services
```csharp
public class MockCacheManager : ICacheManager
{
    private readonly ConcurrentDictionary<string, object> _cache = new();
    
    public T? Get<T>(string key) => 
        _cache.TryGetValue(key, out var value) ? (T)value : default;
    
    public void Set<T>(string key, T value, TimeSpan expiration) => 
        _cache[key] = value!;
}
```

### Test Categories
```csharp
[Test]
[Category("Unit")]
public void ServiceMethod_ValidInput_ReturnsExpectedResult() { }

[Test]
[Category("Integration")]
public void EndToEndWorkflow_CompleteScenario_Success() { }

[Test]
[Category("Security")]
public void PathValidation_MaliciousInput_ThrowsSecurityException() { }

[Test]
[Category("Performance")]
public void LargeFileOperation_Performance_WithinAcceptableLimits() { }
```

## 🔒 Security Implementation

### Input Validation
```csharp
public static string ValidatePath(string path)
{
    if (string.IsNullOrWhiteSpace(path))
        throw new ArgumentException("Path cannot be null or empty");
    
    // Resolve to canonical path
    var fullPath = Path.GetFullPath(path);
    
    // Check for traversal attempts
    if (fullPath.Contains(".."))
        throw new SecurityException("Path traversal detected");
    
    return fullPath;
}
```

### Secure Process Execution
```csharp
public async Task<ProcessResult> ExecuteSecureProcessAsync(
    string executable, 
    string[] arguments,
    CancellationToken cancellationToken = default)
{
    var startInfo = new ProcessStartInfo
    {
        FileName = executable,
        UseShellExecute = false,  // Security: No shell execution
        CreateNoWindow = true,
        RedirectStandardOutput = true,
        RedirectStandardError = true
    };
    
    // Add validated arguments
    foreach (var arg in arguments)
    {
        startInfo.ArgumentList.Add(ValidateArgument(arg));
    }
    
    using var process = new Process { StartInfo = startInfo };
    // ... rest of implementation
}
```

### Configuration Encryption
```csharp
public void SaveEncryptedSettings(ApplicationSettings settings)
{
    var json = JsonSerializer.Serialize(settings);
    var encrypted = ProtectedData.Protect(
        Encoding.UTF8.GetBytes(json),
        GetMachineEntropy(),
        DataProtectionScope.CurrentUser);
    
    File.WriteAllBytes(GetSettingsPath(), encrypted);
}
```

## 📊 Performance Guidelines

### Async Best Practices
```csharp
// Good: ConfigureAwait(false) for non-UI operations
await SomeServiceCallAsync().ConfigureAwait(false);

// Good: Parallel processing with controlled concurrency
var semaphore = new SemaphoreSlim(Environment.ProcessorCount);
var tasks = files.Select(async file =>
{
    await semaphore.WaitAsync();
    try
    {
        return await ProcessFileAsync(file);
    }
    finally
    {
        semaphore.Release();
    }
});
```

### Memory Management
```csharp
// Use large buffers for file operations
private const int BufferSize = 1024 * 1024; // 1MB

// Stream processing for large files
public async Task ProcessLargeFileAsync(string filePath)
{
    using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
    using var bufferedStream = new BufferedStream(fileStream, BufferSize);
    
    var buffer = new byte[BufferSize];
    int bytesRead;
    while ((bytesRead = await bufferedStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
    {
        await ProcessChunkAsync(buffer, bytesRead);
    }
}
```

## 🐛 Debugging Tips

### Logging Configuration
```csharp
// Enable detailed logging for development
_logger.LogDebug("Processing mod: {ModName} from {ModPath}", modName, modPath);
_logger.LogInfo("Installation completed successfully", "ModInstallation", new { 
    ModName = modName, 
    Duration = stopwatch.Elapsed 
});
```

### Common Issues

#### Thread Safety
- Always use thread-safe collections for shared data
- Use proper locking mechanisms (ReaderWriterLockSlim)
- Avoid blocking UI thread with long operations

#### Memory Leaks
- Dispose of resources properly
- Unsubscribe from events
- Use weak references for event handlers when appropriate

#### Performance Issues
- Profile with dotMemory or PerfView
- Use async/await for I/O operations
- Implement proper caching strategies

## 📦 Build and Deployment

### Build Configuration
```bash
# Debug build
dotnet build --configuration Debug

# Release build
dotnet build --configuration Release

# Publish for deployment
dotnet publish --configuration Release --output ./publish
```

### CI/CD Pipeline
The project includes GitHub Actions for:
- Automated testing
- Security scanning
- Performance benchmarking
- Release artifact generation

### Release Process
1. Update version in `.csproj`
2. Update CHANGELOG.md
3. Create release branch
4. Run full test suite
5. Create GitHub release
6. Deploy artifacts

## 🔍 Code Review Checklist

### Security
- [ ] Input validation implemented
- [ ] No hardcoded secrets
- [ ] Proper error handling
- [ ] Security tests included

### Performance
- [ ] Async/await used appropriately
- [ ] Resource disposal implemented
- [ ] Memory usage optimized
- [ ] Performance tests included

### Code Quality
- [ ] Follows coding standards
- [ ] Proper documentation
- [ ] Unit tests included
- [ ] No code duplication

### Architecture
- [ ] Separation of concerns maintained
- [ ] Dependency injection used
- [ ] Interfaces properly defined
- [ ] Error handling consistent

This development guide should help you understand the codebase structure and contribute effectively to the project.
