using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    /// <summary>
    /// Service for detecting and automatically fixing common installation and configuration issues
    /// </summary>
    public class CommonIssueDetectionService : IDisposable
    {
        private readonly string _palRoot;
        private readonly UE4SSDetector _ue4ssDetector;
        private readonly ModManagerService _modManager;
        private readonly EnhancedLogger _logger;
        private readonly List<CommonIssue> _registeredIssues = new();
        private bool _disposed = false;

        public event EventHandler<DiagnosticIssue>? IssueDetected;
        public event EventHandler<DiagnosticIssue>? IssueFixed;
        public event EventHandler<string>? AutoFixCompleted;

        public CommonIssueDetectionService(string palRoot, UE4SSDetector ue4ssDetector, 
            ModManagerService modManager, EnhancedLogger logger)
        {
            _palRoot = palRoot ?? throw new ArgumentNullException(nameof(palRoot));
            _ue4ssDetector = ue4ssDetector ?? throw new ArgumentNullException(nameof(ue4ssDetector));
            _modManager = modManager ?? throw new ArgumentNullException(nameof(modManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            RegisterCommonIssues();
        }

        /// <summary>
        /// Registers all common issues that can be detected and fixed
        /// </summary>
        private void RegisterCommonIssues()
        {
            _registeredIssues.AddRange(new[]
            {
                // UE4SS Issues
                new CommonIssue
                {
                    Id = "ue4ss_missing_proxy_dll",
                    Name = "Missing UE4SS Proxy DLL",
                    Description = "The dwmapi.dll proxy file is missing from the game's Win64 directory",
                    DetectionMethod = () => !File.Exists(Path.Combine(_palRoot, "Pal", "Binaries", "Win64", "dwmapi.dll")),
                    FixMethod = () => FixMissingProxyDll(),
                    Severity = DiagnosticSeverity.Error,
                    Categories = new List<string> { "UE4SS", "Installation" },
                    RequiresUserConfirmation = false
                },

                new CommonIssue
                {
                    Id = "ue4ss_missing_core_files",
                    Name = "Missing UE4SS Core Files",
                    Description = "Essential UE4SS files are missing from the ue4ss directory",
                    DetectionMethod = () => DetectMissingUE4SSCoreFiles(),
                    FixMethod = () => FixMissingUE4SSCoreFiles(),
                    Severity = DiagnosticSeverity.Error,
                    Categories = new List<string> { "UE4SS", "Installation" },
                    RequiresUserConfirmation = true
                },

                new CommonIssue
                {
                    Id = "ue4ss_corrupted_settings",
                    Name = "Corrupted UE4SS Settings",
                    Description = "UE4SS-settings.ini file is corrupted or has invalid configuration",
                    DetectionMethod = () => DetectCorruptedUE4SSSettings(),
                    FixMethod = () => FixCorruptedUE4SSSettings(),
                    Severity = DiagnosticSeverity.Warning,
                    Categories = new List<string> { "UE4SS", "Configuration" },
                    RequiresUserConfirmation = false
                },

                // Mod Issues
                new CommonIssue
                {
                    Id = "mods_missing_enabled_files",
                    Name = "Missing Mod Enabled Files",
                    Description = "Some UE4SS mods are missing their enabled.txt files",
                    DetectionMethod = () => DetectMissingEnabledFiles(),
                    FixMethod = () => FixMissingEnabledFiles(),
                    Severity = DiagnosticSeverity.Warning,
                    Categories = new List<string> { "Mods", "Configuration" },
                    RequiresUserConfirmation = false
                },

                new CommonIssue
                {
                    Id = "mods_incorrect_load_order",
                    Name = "Incorrect Mod Load Order",
                    Description = "Mod load order may cause conflicts or prevent proper functionality",
                    DetectionMethod = () => DetectIncorrectLoadOrder(),
                    FixMethod = () => FixIncorrectLoadOrder(),
                    Severity = DiagnosticSeverity.Warning,
                    Categories = new List<string> { "Mods", "Load Order" },
                    RequiresUserConfirmation = true
                },

                // File System Issues
                new CommonIssue
                {
                    Id = "game_files_readonly",
                    Name = "Game Files are Read-Only",
                    Description = "Some game files are marked as read-only, preventing mod installation",
                    DetectionMethod = () => DetectReadOnlyGameFiles(),
                    FixMethod = () => FixReadOnlyGameFiles(),
                    Severity = DiagnosticSeverity.Error,
                    Categories = new List<string> { "File System", "Permissions" },
                    RequiresUserConfirmation = true
                },

                new CommonIssue
                {
                    Id = "temp_files_cleanup",
                    Name = "Temporary Files Need Cleanup",
                    Description = "Old temporary files from previous installations are taking up space",
                    DetectionMethod = () => DetectTempFilesNeedCleanup(),
                    FixMethod = () => FixTempFilesCleanup(),
                    Severity = DiagnosticSeverity.Info,
                    Categories = new List<string> { "File System", "Cleanup" },
                    RequiresUserConfirmation = false
                },

                // Performance Issues
                new CommonIssue
                {
                    Id = "game_running_during_install",
                    Name = "Game Running During Installation",
                    Description = "Palworld is running, which may interfere with mod installation",
                    DetectionMethod = () => IsGameRunning(),
                    FixMethod = () => FixGameRunning(),
                    Severity = DiagnosticSeverity.Warning,
                    Categories = new List<string> { "Performance", "Process" },
                    RequiresUserConfirmation = true
                },

                // Configuration Issues
                new CommonIssue
                {
                    Id = "missing_mods_json",
                    Name = "Missing mods.json File",
                    Description = "The mods.json file is missing from the UE4SS Mods directory",
                    DetectionMethod = () => !File.Exists(Path.Combine(_ue4ssDetector.GetModsPath(), "mods.json")),
                    FixMethod = () => FixMissingModsJson(),
                    Severity = DiagnosticSeverity.Error,
                    Categories = new List<string> { "UE4SS", "Configuration" },
                    RequiresUserConfirmation = false
                },

                new CommonIssue
                {
                    Id = "missing_mods_txt",
                    Name = "Missing mods.txt File",
                    Description = "The mods.txt file is missing from the UE4SS Mods directory",
                    DetectionMethod = () => !File.Exists(Path.Combine(_ue4ssDetector.GetModsPath(), "mods.txt")),
                    FixMethod = () => FixMissingModsTxt(),
                    Severity = DiagnosticSeverity.Error,
                    Categories = new List<string> { "UE4SS", "Configuration" },
                    RequiresUserConfirmation = false
                }
            });

            _logger.LogInfo($"Registered {_registeredIssues.Count} common issues for detection", "CommonIssues");
        }

        /// <summary>
        /// Runs detection for all registered common issues
        /// </summary>
        public Task<List<DiagnosticIssue>> DetectAllIssuesAsync()
        {
            var detectedIssues = new List<DiagnosticIssue>();

            try
            {
                _logger.LogInfo("Running comprehensive issue detection", "CommonIssues");

                foreach (var commonIssue in _registeredIssues)
                {
                    try
                    {
                        if (commonIssue.DetectionMethod())
                        {
                            var issue = new DiagnosticIssue
                            {
                                Id = commonIssue.Id,
                                Title = commonIssue.Name,
                                Description = commonIssue.Description,
                                Severity = commonIssue.Severity,
                                Category = string.Join(", ", commonIssue.Categories),
                                CanAutoFix = commonIssue.FixMethod != null,
                                AutoFixDescription = commonIssue.CanAutoFix ? 
                                    $"Can automatically fix: {commonIssue.Name}" : ""
                            };

                            detectedIssues.Add(issue);
                            IssueDetected?.Invoke(this, issue);
                            
                            _logger.LogInfo($"Detected issue: {commonIssue.Name}", "CommonIssues");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Error detecting issue {commonIssue.Name}: {ex.Message}", "CommonIssues");
                    }
                }

                _logger.LogInfo($"Issue detection completed: {detectedIssues.Count} issues found", "CommonIssues");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to run issue detection", "CommonIssues", ex);
            }

            return Task.FromResult(detectedIssues);
        }

        /// <summary>
        /// Attempts to automatically fix a specific issue
        /// </summary>
        public Task<bool> AutoFixIssueAsync(string issueId)
        {
            try
            {
                var commonIssue = _registeredIssues.FirstOrDefault(i => i.Id == issueId);
                if (commonIssue?.FixMethod == null)
                {
                    _logger.LogWarning($"No auto-fix available for issue: {issueId}", "CommonIssues");
                    return Task.FromResult(false);
                }

                _logger.LogInfo($"Attempting auto-fix for issue: {commonIssue.Name}", "CommonIssues");

                var success = commonIssue.FixMethod();
                
                if (success)
                {
                    var issue = new DiagnosticIssue
                    {
                        Id = issueId,
                        Title = commonIssue.Name,
                        Description = "Issue has been automatically resolved"
                    };
                    
                    IssueFixed?.Invoke(this, issue);
                    AutoFixCompleted?.Invoke(this, issueId);
                    _logger.LogInfo($"Successfully auto-fixed issue: {commonIssue.Name}", "CommonIssues");
                }
                else
                {
                    _logger.LogWarning($"Auto-fix failed for issue: {commonIssue.Name}", "CommonIssues");
                }

                return Task.FromResult(success);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during auto-fix for issue {issueId}", "CommonIssues", ex);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// Attempts to fix all detected issues that can be auto-fixed
        /// </summary>
        public async Task<Dictionary<string, bool>> AutoFixAllIssuesAsync(bool requireConfirmation = true)
        {
            var results = new Dictionary<string, bool>();
            var detectedIssues = await DetectAllIssuesAsync();

            foreach (var issue in detectedIssues.Where(i => i.CanAutoFix))
            {
                var commonIssue = _registeredIssues.FirstOrDefault(ci => ci.Id == issue.Id);
                if (commonIssue != null)
                {
                    if (!requireConfirmation || !commonIssue.RequiresUserConfirmation)
                    {
                        var success = await AutoFixIssueAsync(issue.Id);
                        results[issue.Id] = success;
                    }
                }
            }

            _logger.LogInfo($"Auto-fix completed for {results.Count} issues", "CommonIssues");
            return results;
        }

        /// <summary>
        /// Gets all registered common issues
        /// </summary>
        public IReadOnlyList<CommonIssue> GetRegisteredIssues()
        {
            return _registeredIssues.AsReadOnly();
        }

        // Detection Methods
        private bool DetectMissingUE4SSCoreFiles()
        {
            var ue4ssPath = _ue4ssDetector.GetUE4SSPath();
            var requiredFiles = new[] { "UE4SS.dll", "UE4SS-settings.ini", "LICENSE" };
            
            return requiredFiles.Any(file => !File.Exists(Path.Combine(ue4ssPath, file)));
        }

        private bool DetectCorruptedUE4SSSettings()
        {
            var settingsPath = Path.Combine(_ue4ssDetector.GetUE4SSPath(), "UE4SS-settings.ini");
            if (!File.Exists(settingsPath))
                return false;

            try
            {
                var content = File.ReadAllText(settingsPath);
                return content.Length < 10 || !content.Contains("[");
            }
            catch
            {
                return true;
            }
        }

        private bool DetectMissingEnabledFiles()
        {
            try
            {
                var modsPath = _ue4ssDetector.GetModsPath();
                if (!Directory.Exists(modsPath))
                    return false;

                var modDirs = Directory.GetDirectories(modsPath);
                return modDirs.Any(dir => !File.Exists(Path.Combine(dir, "enabled.txt")));
            }
            catch
            {
                return false;
            }
        }

        private bool DetectIncorrectLoadOrder()
        {
            // Implementation for detecting incorrect load order
            return false; // Placeholder
        }

        private bool DetectReadOnlyGameFiles()
        {
            try
            {
                var gameFiles = new[] { "Palworld.exe", "Pal\\Binaries\\Win64\\Palworld-Win64-Shipping.exe" };
                return gameFiles.Any(file =>
                {
                    var fullPath = Path.Combine(_palRoot, file);
                    return File.Exists(fullPath) && (File.GetAttributes(fullPath) & FileAttributes.ReadOnly) != 0;
                });
            }
            catch
            {
                return false;
            }
        }

        private bool DetectTempFilesNeedCleanup()
        {
            try
            {
                var tempPath = Path.GetTempPath();
                var palTempFiles = Directory.GetFiles(tempPath, "*palworld*", SearchOption.TopDirectoryOnly);
                var modTempFiles = Directory.GetFiles(tempPath, "*mod*", SearchOption.TopDirectoryOnly);
                
                return palTempFiles.Length + modTempFiles.Length > 10;
            }
            catch
            {
                return false;
            }
        }

        private bool IsGameRunning()
        {
            return Process.GetProcessesByName("Palworld").Length > 0 ||
                   Process.GetProcessesByName("Palworld-Win64-Shipping").Length > 0;
        }

        // Fix Methods
        private bool FixMissingProxyDll()
        {
            try
            {
                var proxyPath = Path.Combine(_palRoot, "Pal", "Binaries", "Win64", "dwmapi.dll");
                // In a real implementation, this would download or copy the correct proxy DLL
                File.WriteAllText(proxyPath, "// UE4SS Proxy DLL placeholder");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to fix missing proxy DLL", "CommonIssues", ex);
                return false;
            }
        }

        private bool FixMissingUE4SSCoreFiles()
        {
            // Implementation for fixing missing UE4SS core files
            return false; // Placeholder - would require actual UE4SS files
        }

        private bool FixCorruptedUE4SSSettings()
        {
            try
            {
                var settingsPath = Path.Combine(_ue4ssDetector.GetUE4SSPath(), "UE4SS-settings.ini");
                var defaultSettings = @"[Debug]
ConsoleEnabled = true
GuiConsoleEnabled = true
GuiConsoleVisible = false

[General]
EnableHotReloadSystem = false";

                File.WriteAllText(settingsPath, defaultSettings);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to fix corrupted UE4SS settings", "CommonIssues", ex);
                return false;
            }
        }

        private bool FixMissingEnabledFiles()
        {
            try
            {
                var modsPath = _ue4ssDetector.GetModsPath();
                var modDirs = Directory.GetDirectories(modsPath);
                
                foreach (var modDir in modDirs)
                {
                    var enabledFile = Path.Combine(modDir, "enabled.txt");
                    if (!File.Exists(enabledFile))
                    {
                        File.WriteAllText(enabledFile, "");
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to fix missing enabled files", "CommonIssues", ex);
                return false;
            }
        }

        private bool FixIncorrectLoadOrder()
        {
            // Implementation for fixing incorrect load order
            return false; // Placeholder
        }

        private bool FixReadOnlyGameFiles()
        {
            try
            {
                var gameFiles = new[] { "Palworld.exe", "Pal\\Binaries\\Win64\\Palworld-Win64-Shipping.exe" };
                foreach (var file in gameFiles)
                {
                    var fullPath = Path.Combine(_palRoot, file);
                    if (File.Exists(fullPath))
                    {
                        var attributes = File.GetAttributes(fullPath);
                        if ((attributes & FileAttributes.ReadOnly) != 0)
                        {
                            File.SetAttributes(fullPath, attributes & ~FileAttributes.ReadOnly);
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to fix read-only game files", "CommonIssues", ex);
                return false;
            }
        }

        private bool FixTempFilesCleanup()
        {
            try
            {
                var tempPath = Path.GetTempPath();
                var filesToDelete = new List<string>();
                
                filesToDelete.AddRange(Directory.GetFiles(tempPath, "*palworld*", SearchOption.TopDirectoryOnly));
                filesToDelete.AddRange(Directory.GetFiles(tempPath, "*mod*", SearchOption.TopDirectoryOnly));
                
                foreach (var file in filesToDelete)
                {
                    try
                    {
                        File.Delete(file);
                    }
                    catch
                    {
                        // Ignore individual file deletion failures
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to cleanup temp files", "CommonIssues", ex);
                return false;
            }
        }

        private bool FixGameRunning()
        {
            try
            {
                var processes = Process.GetProcessesByName("Palworld")
                    .Concat(Process.GetProcessesByName("Palworld-Win64-Shipping"))
                    .ToArray();
                
                foreach (var process in processes)
                {
                    try
                    {
                        process.CloseMainWindow();
                        if (!process.WaitForExit(5000))
                        {
                            process.Kill();
                        }
                    }
                    catch
                    {
                        // Ignore individual process termination failures
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to close game processes", "CommonIssues", ex);
                return false;
            }
        }

        private bool FixMissingModsJson()
        {
            try
            {
                var modsJsonPath = Path.Combine(_ue4ssDetector.GetModsPath(), "mods.json");
                File.WriteAllText(modsJsonPath, "{}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to create mods.json", "CommonIssues", ex);
                return false;
            }
        }

        private bool FixMissingModsTxt()
        {
            try
            {
                var modsTxtPath = Path.Combine(_ue4ssDetector.GetModsPath(), "mods.txt");
                var coreModsList = string.Join("\n", UE4SSDetector.RequiredCoreMods);
                File.WriteAllText(modsTxtPath, coreModsList);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to create mods.txt", "CommonIssues", ex);
                return false;
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
                _logger.LogInfo("Common Issue Detection Service disposed", "CommonIssues");
            }
        }
    }
}
