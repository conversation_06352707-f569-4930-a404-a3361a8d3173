using System;
using System.Drawing;
using System.Windows.Forms;
using ModInstallerApp.Models;

namespace ModInstallerApp.UI
{
    public class BackupOptionsDialog : Form
    {
        private readonly UE4SSStatus _ue4ssStatus;
        private readonly PalSchemaStatus _palSchemaStatus;
        private readonly long _estimatedSize;

        public BackupSettings Settings { get; private set; }

        private readonly CheckBox chkUE4SSCore;
        private readonly CheckBox chkUE4SSCoreMods;
        private readonly CheckBox chkUserMods;
        private readonly CheckBox chkGameContentMods;
        private readonly CheckBox chkPalSchemaSystem;
        private readonly CheckBox chkPalSchemaMods;
        private readonly Label lblEstimatedSize;

        public BackupOptionsDialog(BackupSettings currentSettings, UE4SSStatus ue4ssStatus,
                                  PalSchemaStatus palSchemaStatus, long estimatedSize)
        {
            Settings = currentSettings;
            _ue4ssStatus = ue4ssStatus;
            _palSchemaStatus = palSchemaStatus;
            _estimatedSize = estimatedSize;

            Text = "Backup Options";
            Size = new Size(500, 400);
            StartPosition = FormStartPosition.CenterParent;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;

            Theme.Apply(this);

            var layout = new TableLayoutPanel { Dock = DockStyle.Fill, RowCount = 10, ColumnCount = 1 };
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // Title
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // UE4SS Core
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // UE4SS Core Mods
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // User Mods
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // Game Content
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // PalSchema System
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // PalSchema Mods
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // Spacer
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // Size estimate
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // Buttons

            var lblTitle = new Label
            {
                Text = "Select what to include in the backup:",
                AutoSize = true,
                Padding = new Padding(0, 0, 0, 10)
            };

            chkUE4SSCore = new CheckBox
            {
                Text = "UE4SS Core Files (dwmapi.dll, ue4ss folder)",
                Checked = Settings.IncludeUE4SSCore,
                AutoSize = true,
                Enabled = _ue4ssStatus.Status != UE4SSInstallStatus.NotInstalled
            };

            chkUE4SSCoreMods = new CheckBox
            {
                Text = $"UE4SS Core Mods ({_ue4ssStatus.CoreModsPresent}/{_ue4ssStatus.CoreModsExpected})",
                Checked = Settings.IncludeUE4SSCoreMods,
                AutoSize = true,
                Enabled = _ue4ssStatus.CoreModsPresent > 0
            };

            chkUserMods = new CheckBox
            {
                Text = $"User Mods ({_ue4ssStatus.UserMods.Count} installed)",
                Checked = Settings.IncludeUserMods,
                AutoSize = true,
                Enabled = _ue4ssStatus.UserMods.Count > 0
            };

            chkGameContentMods = new CheckBox
            {
                Text = "Game Content Mods (Pak files, ~mods, LogicMods)",
                Checked = Settings.IncludeGameContentMods,
                AutoSize = true
            };

            chkPalSchemaSystem = new CheckBox
            {
                Text = "PalSchema System",
                Checked = Settings.IncludePalSchemaSystem,
                AutoSize = true,
                Enabled = _palSchemaStatus.IsInstalled
            };

            chkPalSchemaMods = new CheckBox
            {
                Text = $"PalSchema Mods ({_palSchemaStatus.Mods.Count} installed)",
                Checked = Settings.IncludePalSchemaMods,
                AutoSize = true,
                Enabled = _palSchemaStatus.Mods.Count > 0
            };

            lblEstimatedSize = new Label
            {
                Text = $"Estimated backup size: {FormatFileSize(_estimatedSize)}",
                AutoSize = true,
                Padding = new Padding(0, 10, 0, 0)
            };

            // Update size estimate when checkboxes change
            var updateSize = new EventHandler((s, e) => UpdateSizeEstimate());
            chkUE4SSCore.CheckedChanged += updateSize;
            chkUE4SSCoreMods.CheckedChanged += updateSize;
            chkUserMods.CheckedChanged += updateSize;
            chkGameContentMods.CheckedChanged += updateSize;
            chkPalSchemaSystem.CheckedChanged += updateSize;
            chkPalSchemaMods.CheckedChanged += updateSize;

            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft,
                AutoSize = true
            };

            var btnOK = new ThemedButton { Text = "OK" };
            var btnCancel = new ThemedButton { Text = "Cancel" };

            btnOK.Click += (s, e) =>
            {
                Settings = new BackupSettings
                {
                    IncludeUE4SSCore = chkUE4SSCore.Checked,
                    IncludeUE4SSCoreMods = chkUE4SSCoreMods.Checked,
                    IncludeUserMods = chkUserMods.Checked,
                    IncludeGameContentMods = chkGameContentMods.Checked,
                    IncludePalSchemaSystem = chkPalSchemaSystem.Checked,
                    IncludePalSchemaMods = chkPalSchemaMods.Checked
                };
                DialogResult = DialogResult.OK;
            };

            btnCancel.Click += (s, e) => DialogResult = DialogResult.Cancel;

            buttonPanel.Controls.AddRange(new Control[] { btnCancel, btnOK });

            layout.Controls.Add(lblTitle, 0, 0);
            layout.Controls.Add(chkUE4SSCore, 0, 1);
            layout.Controls.Add(chkUE4SSCoreMods, 0, 2);
            layout.Controls.Add(chkUserMods, 0, 3);
            layout.Controls.Add(chkGameContentMods, 0, 4);
            layout.Controls.Add(chkPalSchemaSystem, 0, 5);
            layout.Controls.Add(chkPalSchemaMods, 0, 6);
            layout.Controls.Add(lblEstimatedSize, 0, 8);
            layout.Controls.Add(buttonPanel, 0, 9);

            Controls.Add(layout);
        }

        private void UpdateSizeEstimate()
        {
            // This is a rough estimate - in real implementation, you'd calculate actual sizes
            long estimate = 0;
            if (chkUE4SSCore.Checked) estimate += 10 * 1024 * 1024; // 10MB
            if (chkUE4SSCoreMods.Checked) estimate += 50 * 1024 * 1024; // 50MB
            if (chkUserMods.Checked) estimate += 100 * 1024 * 1024; // 100MB
            if (chkGameContentMods.Checked) estimate += 500 * 1024 * 1024; // 500MB
            if (chkPalSchemaSystem.Checked) estimate += 20 * 1024 * 1024; // 20MB
            if (chkPalSchemaMods.Checked) estimate += 50 * 1024 * 1024; // 50MB

            lblEstimatedSize.Text = $"Estimated backup size: {FormatFileSize(estimate)}";
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len /= 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}