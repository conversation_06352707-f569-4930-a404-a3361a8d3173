using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ModInstallerApp.Models;
using ModInstallerApp.Services;

namespace ModInstallerApp.UI
{
    /// <summary>
    /// Main mod management form with grid-style interface
    /// </summary>
    public partial class ModManagerForm : Form
    {
        private readonly ModManagerService _modManager;
        private readonly EnhancedLogger _logger;
        
        // UI Components
        private ModGridControl _modGrid = null!;
        private TextBox _searchBox = null!;
        private ComboBox _categoryFilter = null!;
        private ComboBox _typeFilter = null!;
        private ComboBox _stateFilter = null!;
        private ComboBox _profileSelector = null!;
        private Button _newProfileButton = null!;
        private Button _saveProfileButton = null!;
        private Button _deleteProfileButton = null!;
        private CheckBox _enabledOnlyFilter = null!;
        private CheckBox _conflictsOnlyFilter = null!;
        private ComboBox _sortByCombo = null!;
        private CheckBox _sortDescendingCheck = null!;
        private Label _modCountLabel = null!;
        private Panel _detailsPanel = null!;
        
        // Current filter state
        private ModFilter _currentFilter = new();
        private List<ModItem> _filteredMods = new();

        public ModManagerForm(ModManagerService modManager, EnhancedLogger logger)
        {
            _modManager = modManager ?? throw new ArgumentNullException(nameof(modManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            InitializeComponent();
            SetupEventHandlers();
            Theme.Apply(this);
        }

        private void InitializeComponent()
        {
            Text = "Mod Manager - Advanced Grid Interface";
            Size = new Size(1200, 800);
            StartPosition = FormStartPosition.CenterScreen;
            BackColor = Theme.Surface;
            ForeColor = Theme.TextLight;
            Font = Theme.BodyFont;

            // Create main layout
            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 3,
                ColumnCount = 1
            };
            
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // Toolbar
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 70)); // Grid
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 30)); // Details

            // Create toolbar
            var toolbar = CreateToolbar();
            mainLayout.Controls.Add(toolbar, 0, 0);

            // Create mod grid
            _modGrid = new ModGridControl
            {
                Dock = DockStyle.Fill,
                Settings = new ModGridSettings
                {
                    TileSize = 180,
                    TileSpacing = 8,
                    ShowThumbnails = true,
                    ShowMetadata = true,
                    ShowStatus = true,
                    ViewMode = ModGridViewMode.Grid
                }
            };
            mainLayout.Controls.Add(_modGrid, 0, 1);

            // Create details panel
            _detailsPanel = CreateDetailsPanel();
            mainLayout.Controls.Add(_detailsPanel, 0, 2);

            Controls.Add(mainLayout);
        }

        private Panel CreateToolbar()
        {
            var toolbar = new Panel
            {
                Height = 120,
                Dock = DockStyle.Top,
                BackColor = Theme.Track,
                Padding = new Padding(10)
            };

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 3,
                ColumnCount = 8
            };

            // Row 1: Search and basic filters
            _searchBox = new TextBox
            {
                PlaceholderText = "Search mods...",
                Width = 200,
                Anchor = AnchorStyles.Left | AnchorStyles.Right
            };

            _categoryFilter = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 120
            };
            _categoryFilter.Items.Add("All Categories");
            foreach (var category in Enum.GetValues<ModCategory>())
            {
                _categoryFilter.Items.Add(category.ToString());
            }
            _categoryFilter.SelectedIndex = 0;

            _typeFilter = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 120
            };
            _typeFilter.Items.Add("All Types");
            foreach (var type in Enum.GetValues<ModType>())
            {
                _typeFilter.Items.Add(type.ToString());
            }
            _typeFilter.SelectedIndex = 0;

            _stateFilter = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 120
            };
            _stateFilter.Items.Add("All States");
            foreach (var state in Enum.GetValues<ModState>())
            {
                _stateFilter.Items.Add(state.ToString());
            }
            _stateFilter.SelectedIndex = 0;

            // Row 2: Profile management
            _profileSelector = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 200
            };

            _newProfileButton = new ThemedButton { Text = "New Profile" };
            _saveProfileButton = new ThemedButton { Text = "Save Profile" };
            _deleteProfileButton = new ThemedButton { Text = "Delete Profile" };
            var manageProfilesButton = new ThemedButton { Text = "Manage Profiles..." };
            manageProfilesButton.Click += OnManageProfiles;

            // Row 3: Advanced filters and sorting
            _enabledOnlyFilter = new CheckBox { Text = "Enabled Only", AutoSize = true };
            _conflictsOnlyFilter = new CheckBox { Text = "Conflicts Only", AutoSize = true };

            _sortByCombo = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Width = 120
            };
            foreach (var criteria in Enum.GetValues<ModSortCriteria>())
            {
                _sortByCombo.Items.Add(criteria.ToString());
            }
            _sortByCombo.SelectedIndex = 0;

            _sortDescendingCheck = new CheckBox { Text = "Descending", AutoSize = true };

            _modCountLabel = new Label
            {
                Text = "0 mods",
                AutoSize = true,
                ForeColor = Theme.TextLight
            };

            var advancedSearchButton = new ThemedButton { Text = "Advanced Search..." };
            advancedSearchButton.Click += OnAdvancedSearch;

            var loadOrderButton = new ThemedButton { Text = "Load Order..." };
            loadOrderButton.Click += OnManageLoadOrder;

            // Add controls to layout
            layout.Controls.Add(new Label { Text = "Search:", AutoSize = true }, 0, 0);
            layout.Controls.Add(_searchBox, 1, 0);
            layout.Controls.Add(_categoryFilter, 2, 0);
            layout.Controls.Add(_typeFilter, 3, 0);
            layout.Controls.Add(_stateFilter, 4, 0);
            layout.Controls.Add(advancedSearchButton, 5, 0);
            layout.Controls.Add(loadOrderButton, 6, 0);
            layout.Controls.Add(_modCountLabel, 7, 0);

            layout.Controls.Add(new Label { Text = "Profile:", AutoSize = true }, 0, 1);
            layout.Controls.Add(_profileSelector, 1, 1);
            layout.Controls.Add(_newProfileButton, 2, 1);
            layout.Controls.Add(_saveProfileButton, 3, 1);
            layout.Controls.Add(_deleteProfileButton, 4, 1);
            layout.Controls.Add(manageProfilesButton, 5, 1);

            layout.Controls.Add(_enabledOnlyFilter, 0, 2);
            layout.Controls.Add(_conflictsOnlyFilter, 1, 2);
            layout.Controls.Add(new Label { Text = "Sort by:", AutoSize = true }, 2, 2);
            layout.Controls.Add(_sortByCombo, 3, 2);
            layout.Controls.Add(_sortDescendingCheck, 4, 2);

            toolbar.Controls.Add(layout);
            return toolbar;
        }

        private Panel CreateDetailsPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Theme.Track,
                Padding = new Padding(10)
            };

            var label = new Label
            {
                Text = "Select a mod to view details",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = Theme.TextLight
            };

            panel.Controls.Add(label);
            return panel;
        }

        private void SetupEventHandlers()
        {
            // Search and filter events
            _searchBox.TextChanged += (s, e) => ApplyFilters();
            _categoryFilter.SelectedIndexChanged += (s, e) => ApplyFilters();
            _typeFilter.SelectedIndexChanged += (s, e) => ApplyFilters();
            _stateFilter.SelectedIndexChanged += (s, e) => ApplyFilters();
            _enabledOnlyFilter.CheckedChanged += (s, e) => ApplyFilters();
            _conflictsOnlyFilter.CheckedChanged += (s, e) => ApplyFilters();
            _sortByCombo.SelectedIndexChanged += (s, e) => ApplyFilters();
            _sortDescendingCheck.CheckedChanged += (s, e) => ApplyFilters();

            // Profile events
            _profileSelector.SelectedIndexChanged += OnProfileSelected;
            _newProfileButton.Click += OnNewProfile;
            _saveProfileButton.Click += OnSaveProfile;
            _deleteProfileButton.Click += OnDeleteProfile;

            // Grid events
            _modGrid.ModSelected += OnModSelected;
            _modGrid.ModDoubleClicked += OnModDoubleClicked;
            _modGrid.ModStateChanged += OnModStateChanged;
            _modGrid.ModReordered += OnModReordered;

            // Service events
            _modManager.ModsUpdated += OnModsUpdated;
            _modManager.ProfileChanged += OnProfileChanged;
            _modManager.ModStateChanged += OnModStateChangedFromService;
        }

        public async void LoadData()
        {
            try
            {
                await _modManager.InitializeAsync();
                RefreshProfiles();
                ApplyFilters();
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to load mod manager data", "ModManagerForm", ex);
                MessageBox.Show($"Failed to load mod data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ApplyFilters()
        {
            _currentFilter.SearchText = _searchBox.Text;
            
            // Category filter
            _currentFilter.Categories.Clear();
            if (_categoryFilter.SelectedIndex > 0)
            {
                var selectedCategory = (ModCategory)(_categoryFilter.SelectedIndex - 1);
                _currentFilter.Categories.Add(selectedCategory);
            }

            // Type filter
            _currentFilter.ModTypes.Clear();
            if (_typeFilter.SelectedIndex > 0)
            {
                var selectedType = (ModType)(_typeFilter.SelectedIndex - 1);
                _currentFilter.ModTypes.Add(selectedType);
            }

            // State filter
            _currentFilter.States.Clear();
            if (_stateFilter.SelectedIndex > 0)
            {
                var selectedState = (ModState)(_stateFilter.SelectedIndex - 1);
                _currentFilter.States.Add(selectedState);
            }

            _currentFilter.ShowEnabledOnly = _enabledOnlyFilter.Checked;
            _currentFilter.ShowConflictsOnly = _conflictsOnlyFilter.Checked;
            _currentFilter.SortBy = (ModSortCriteria)_sortByCombo.SelectedIndex;
            _currentFilter.SortDescending = _sortDescendingCheck.Checked;

            _filteredMods = _modManager.FilterMods(_currentFilter);
            _modGrid.Mods = _filteredMods;
            
            _modCountLabel.Text = $"{_filteredMods.Count} mods";
        }

        private void OnAdvancedSearch(object? sender, EventArgs e)
        {
            using var dialog = new AdvancedSearchDialog();
            dialog.LoadFromFilter(_currentFilter);

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                _currentFilter = dialog.Filter;
                _filteredMods = _modManager.FilterMods(_currentFilter);
                _modGrid.Mods = _filteredMods;
                _modCountLabel.Text = $"{_filteredMods.Count} mods";

                // Update simple filter controls to reflect advanced filter
                UpdateSimpleFiltersFromAdvanced();
            }
        }

        private void UpdateSimpleFiltersFromAdvanced()
        {
            _searchBox.Text = _currentFilter.SearchText;

            // Update category filter
            if (_currentFilter.Categories.Count == 1)
            {
                var category = _currentFilter.Categories[0];
                _categoryFilter.SelectedIndex = (int)category + 1;
            }
            else
            {
                _categoryFilter.SelectedIndex = 0; // All Categories
            }

            // Update type filter
            if (_currentFilter.ModTypes.Count == 1)
            {
                var type = _currentFilter.ModTypes[0];
                _typeFilter.SelectedIndex = (int)type + 1;
            }
            else
            {
                _typeFilter.SelectedIndex = 0; // All Types
            }

            // Update state filter
            if (_currentFilter.States.Count == 1)
            {
                var state = _currentFilter.States[0];
                _stateFilter.SelectedIndex = (int)state + 1;
            }
            else
            {
                _stateFilter.SelectedIndex = 0; // All States
            }

            _enabledOnlyFilter.Checked = _currentFilter.ShowEnabledOnly;
            _conflictsOnlyFilter.Checked = _currentFilter.ShowConflictsOnly;
            _sortByCombo.SelectedIndex = (int)_currentFilter.SortBy;
            _sortDescendingCheck.Checked = _currentFilter.SortDescending;
        }

        private void OnManageProfiles(object? sender, EventArgs e)
        {
            var profiles = _modManager.GetAllProfiles();
            using var dialog = new ProfileManagerDialog(profiles);
            dialog.ShowDialog();

            // Refresh profiles after dialog closes
            RefreshProfiles();
        }

        private void OnManageLoadOrder(object? sender, EventArgs e)
        {
            var mods = _modManager.GetAllMods();
            using var dialog = new LoadOrderDialog(mods);
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                // Refresh the grid to show updated load orders
                ApplyFilters();
            }
        }

        private void RefreshProfiles()
        {
            _profileSelector.Items.Clear();
            _profileSelector.Items.Add("No Profile");

            foreach (var profile in _modManager.Profiles)
            {
                _profileSelector.Items.Add(profile.Name);
                if (profile.IsActive)
                {
                    _profileSelector.SelectedIndex = _profileSelector.Items.Count - 1;
                }
            }

            if (_profileSelector.SelectedIndex == -1)
            {
                _profileSelector.SelectedIndex = 0;
            }
        }

        // ── EVENT HANDLERS ──

        private async void OnProfileSelected(object? sender, EventArgs e)
        {
            if (_profileSelector.SelectedIndex <= 0)
                return;

            var profileName = _profileSelector.SelectedItem?.ToString();
            var profile = _modManager.Profiles.FirstOrDefault(p => p.Name == profileName);
            if (profile != null)
            {
                await _modManager.LoadProfileAsync(profile.Id);
            }
        }

        private async void OnNewProfile(object? sender, EventArgs e)
        {
            var dialog = new TextInputDialog("New Profile", "Enter profile name:");
            if (dialog.ShowDialog() == DialogResult.OK && !string.IsNullOrWhiteSpace(dialog.InputText))
            {
                try
                {
                    await _modManager.CreateProfileAsync(dialog.InputText);
                    RefreshProfiles();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Failed to create profile: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void OnSaveProfile(object? sender, EventArgs e)
        {
            if (_modManager.ActiveProfile == null)
            {
                MessageBox.Show("No active profile to save.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // Profile is automatically saved when mods are modified
                MessageBox.Show("Profile saved successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to save profile: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OnDeleteProfile(object? sender, EventArgs e)
        {
            if (_modManager.ActiveProfile == null)
            {
                MessageBox.Show("No active profile to delete.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var result = MessageBox.Show($"Are you sure you want to delete the profile '{_modManager.ActiveProfile.Name}'?",
                "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // TODO: Implement profile deletion
                MessageBox.Show("Profile deletion not yet implemented.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void OnModSelected(object? sender, ModItem mod)
        {
            ShowModDetails(mod);
        }

        private void OnModDoubleClicked(object? sender, ModItem mod)
        {
            // Toggle enabled state on double-click
            _ = _modManager.SetModEnabledAsync(mod.Id, !mod.IsEnabled);
        }

        private void OnModStateChanged(object? sender, ModItem mod)
        {
            // This is called when the grid wants to change a mod's state
            _ = _modManager.SetModEnabledAsync(mod.Id, !mod.IsEnabled);
        }

        private void OnModReordered(object? sender, (ModItem source, ModItem target) reorder)
        {
            // Implement load order changes
            var sourceIndex = _filteredMods.IndexOf(reorder.source);
            var targetIndex = _filteredMods.IndexOf(reorder.target);

            if (sourceIndex >= 0 && targetIndex >= 0)
            {
                _ = _modManager.SetModLoadOrderAsync(reorder.source.Id, reorder.target.LoadOrder);
            }
        }

        private void OnModsUpdated(object? sender, List<ModItem> mods)
        {
            if (InvokeRequired)
            {
                Invoke(() => OnModsUpdated(sender, mods));
                return;
            }

            ApplyFilters();
        }

        private void OnProfileChanged(object? sender, ModProfile profile)
        {
            if (InvokeRequired)
            {
                Invoke(() => OnProfileChanged(sender, profile));
                return;
            }

            RefreshProfiles();
        }

        private void OnModStateChangedFromService(object? sender, ModItem mod)
        {
            if (InvokeRequired)
            {
                Invoke(() => OnModStateChangedFromService(sender, mod));
                return;
            }

            _modGrid.Invalidate(); // Refresh the grid to show state changes
        }

        private void ShowModDetails(ModItem mod)
        {
            _detailsPanel.Controls.Clear();

            var detailsLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 8,
                ColumnCount = 2,
                Padding = new Padding(10)
            };

            // Add mod details
            detailsLayout.Controls.Add(new Label { Text = "Name:", Font = new Font(Theme.BodyFont, FontStyle.Bold) }, 0, 0);
            detailsLayout.Controls.Add(new Label { Text = mod.Name }, 1, 0);

            detailsLayout.Controls.Add(new Label { Text = "Version:", Font = new Font(Theme.BodyFont, FontStyle.Bold) }, 0, 1);
            detailsLayout.Controls.Add(new Label { Text = mod.Version }, 1, 1);

            detailsLayout.Controls.Add(new Label { Text = "Author:", Font = new Font(Theme.BodyFont, FontStyle.Bold) }, 0, 2);
            detailsLayout.Controls.Add(new Label { Text = mod.Author }, 1, 2);

            detailsLayout.Controls.Add(new Label { Text = "Category:", Font = new Font(Theme.BodyFont, FontStyle.Bold) }, 0, 3);
            detailsLayout.Controls.Add(new Label { Text = mod.Category.ToString() }, 1, 3);

            detailsLayout.Controls.Add(new Label { Text = "Size:", Font = new Font(Theme.BodyFont, FontStyle.Bold) }, 0, 4);
            detailsLayout.Controls.Add(new Label { Text = mod.FormattedSize }, 1, 4);

            detailsLayout.Controls.Add(new Label { Text = "Status:", Font = new Font(Theme.BodyFont, FontStyle.Bold) }, 0, 5);
            detailsLayout.Controls.Add(new Label { Text = mod.StatusText }, 1, 5);

            detailsLayout.Controls.Add(new Label { Text = "Installed:", Font = new Font(Theme.BodyFont, FontStyle.Bold) }, 0, 6);
            detailsLayout.Controls.Add(new Label { Text = mod.InstallDate.ToString("yyyy-MM-dd HH:mm") }, 1, 6);

            // Add action buttons
            var buttonPanel = new FlowLayoutPanel { FlowDirection = FlowDirection.LeftToRight, AutoSize = true };

            var enableButton = new ThemedButton
            {
                Text = mod.IsEnabled ? "Disable" : "Enable",
                Tag = mod
            };
            enableButton.Click += async (s, e) => await _modManager.SetModEnabledAsync(mod.Id, !mod.IsEnabled);

            var uninstallButton = new ThemedButton
            {
                Text = "Uninstall",
                Tag = mod,
                Enabled = mod.CanUninstall
            };

            var rollbackButton = new ThemedButton
            {
                Text = "Rollback",
                Tag = mod,
                Enabled = mod.CanRollback
            };

            buttonPanel.Controls.AddRange(new Control[] { enableButton, uninstallButton, rollbackButton });
            detailsLayout.Controls.Add(buttonPanel, 1, 7);

            _detailsPanel.Controls.Add(detailsLayout);
        }
    }

    /// <summary>
    /// Simple text input dialog
    /// </summary>
    public class TextInputDialog : Form
    {
        public string InputText { get; private set; } = "";

        public TextInputDialog(string title, string prompt)
        {
            Text = title;
            Size = new Size(400, 150);
            StartPosition = FormStartPosition.CenterParent;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 3,
                ColumnCount = 1,
                Padding = new Padding(10)
            };

            var promptLabel = new Label { Text = prompt, AutoSize = true };
            var textBox = new TextBox { Dock = DockStyle.Fill };

            var buttonPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.RightToLeft,
                AutoSize = true,
                Dock = DockStyle.Right
            };

            var okButton = new Button { Text = "OK", DialogResult = DialogResult.OK };
            var cancelButton = new Button { Text = "Cancel", DialogResult = DialogResult.Cancel };

            buttonPanel.Controls.AddRange(new Control[] { cancelButton, okButton });

            layout.Controls.Add(promptLabel, 0, 0);
            layout.Controls.Add(textBox, 0, 1);
            layout.Controls.Add(buttonPanel, 0, 2);

            Controls.Add(layout);
            AcceptButton = okButton;
            CancelButton = cancelButton;

            okButton.Click += (s, e) => { InputText = textBox.Text; Close(); };
        }
    }
}
