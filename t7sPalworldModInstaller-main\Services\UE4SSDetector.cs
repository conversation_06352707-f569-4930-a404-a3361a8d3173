using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    public class UE4SSDetector : IDisposable
    {
        private readonly string _palRoot;
        private readonly CacheManager _cache;
        private bool _disposed = false;
        
        public static readonly string[] RequiredCoreMods = new[]
        {
            "BPML_GenericFunctions",
            "BPModLoaderMod",
            "CheatManagerEnablerMod",
            "ConsoleCommandsMod",
            "ConsoleEnablerMod",
            "Keybinds",
            "LineTraceMod",
            "shared",
            "SplitScreenMod"
        };

        public UE4SSDetector(string palRoot, CacheManager cache)
        {
            _palRoot = ValidatePath(palRoot) ?? throw new ArgumentException("Invalid Palworld root path", nameof(palRoot));
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        }

        // ✅ NEW: Method to invalidate cache when files change
        public void InvalidateCache()
        {
            ThrowIfDisposed();
            
            var ue4ssCacheKey = $"ue4ss_status_{_palRoot.GetHashCode()}";
            var palSchemaCacheKey = $"palschema_status_{_palRoot.GetHashCode()}";
            
            _cache.InvalidateCache(ue4ssCacheKey);
            _cache.InvalidateCache(palSchemaCacheKey);
        }

        // ✅ ENHANCED: Force fresh detection (bypass cache completely)
        public UE4SSStatus DetectUE4SSFresh()
        {
            ThrowIfDisposed();
            InvalidateCache();
            return DetectUE4SS();
        }

        public PalSchemaStatus DetectPalSchemaFresh()
        {
            ThrowIfDisposed();
            InvalidateCache();
            return DetectPalSchema();
        }

        public async Task<UE4SSStatus> DetectUE4SSAsync()
        {
            ThrowIfDisposed();
            return await Task.Run(() => DetectUE4SS());
        }

        public UE4SSStatus DetectUE4SS()
        {
            ThrowIfDisposed();
            
            var cacheKey = $"ue4ss_status_{_palRoot.GetHashCode()}";
            var cached = _cache.Get<UE4SSStatus>(cacheKey);
            
            if (cached != null && (DateTime.Now - cached.LastChecked).TotalMinutes < 5)
            {
                return cached;
            }

            var status = new UE4SSStatus { LastChecked = DateTime.Now };
            
            try
            {
                var binariesPath = Path.Combine(_palRoot, "Pal", "Binaries", "Win64");
                if (!Directory.Exists(binariesPath))
                {
                    status.Status = UE4SSInstallStatus.NotInstalled;
                    _cache.Set(cacheKey, status, TimeSpan.FromMinutes(5));
                    return status;
                }

                // Check proxy DLL
                status.HasProxyDll = File.Exists(Path.Combine(binariesPath, "dwmapi.dll"));

                // Check UE4SS folder
                var ue4ssPath = Path.Combine(binariesPath, "ue4ss");
                status.HasUE4SSFolder = Directory.Exists(ue4ssPath);

                if (status.HasUE4SSFolder)
                {
                    // Check core files
                    status.HasUE4SSDll = File.Exists(Path.Combine(ue4ssPath, "UE4SS.dll"));
                    status.HasSettingsIni = File.Exists(Path.Combine(ue4ssPath, "UE4SS-settings.ini"));
                    status.HasLicense = File.Exists(Path.Combine(ue4ssPath, "LICENSE"));

                    // Check mods
                    var modsPath = Path.Combine(ue4ssPath, "Mods");
                    if (Directory.Exists(modsPath))
                    {
                        // ✅ FIX: Check for core mod files (mods.json, mods.txt)
                        status.HasModsJson = File.Exists(Path.Combine(modsPath, "mods.json"));
                        status.HasModsTxt = File.Exists(Path.Combine(modsPath, "mods.txt"));

                        var allMods = Directory.GetDirectories(modsPath)
                            .Select(Path.GetFileName)
                            .Where(m => !string.IsNullOrEmpty(m))
                            .ToList();
                        
                        foreach (var mod in RequiredCoreMods)
                        {
                            if (allMods.Contains(mod, StringComparer.OrdinalIgnoreCase))
                            {
                                status.CoreMods.Add(mod);
                            }
                        }

                        status.CoreModsPresent = status.CoreMods.Count;
                        status.UserMods = allMods.Where(m => !string.IsNullOrEmpty(m) && !RequiredCoreMods.Contains(m, StringComparer.OrdinalIgnoreCase)).Cast<string>().ToList();
                    }
                }

                // Determine overall status (enhanced logic)
                if (!status.HasProxyDll && !status.HasUE4SSFolder)
                {
                    status.Status = UE4SSInstallStatus.NotInstalled;
                }
                else if (status.HasProxyDll && status.HasUE4SSFolder && status.HasUE4SSDll && 
                         status.HasSettingsIni && status.HasModsJson && status.HasModsTxt &&
                         status.CoreModsPresent == status.CoreModsExpected)
                {
                    status.Status = UE4SSInstallStatus.FullyInstalled;
                }
                else if (status.HasProxyDll || status.HasUE4SSFolder)
                {
                    status.Status = UE4SSInstallStatus.PartiallyInstalled;
                }
                else
                {
                    status.Status = UE4SSInstallStatus.Unknown;
                }
            }
            catch (Exception)
            {
                // Log error but don't crash
                status.Status = UE4SSInstallStatus.Unknown;
            }

            _cache.Set(cacheKey, status, TimeSpan.FromMinutes(5));
            return status;
        }

        public async Task<PalSchemaStatus> DetectPalSchemaAsync()
        {
            ThrowIfDisposed();
            return await Task.Run(() => DetectPalSchema());
        }

        /// <summary>
        /// Gets the UE4SS installation path
        /// </summary>
        public string GetUE4SSPath()
        {
            ThrowIfDisposed();
            return Path.Combine(_palRoot, "Pal", "Binaries", "Win64", "ue4ss");
        }

        /// <summary>
        /// Gets the UE4SS Mods directory path
        /// </summary>
        public string GetModsPath()
        {
            ThrowIfDisposed();
            return Path.Combine(GetUE4SSPath(), "Mods");
        }

        /// <summary>
        /// Gets the PalSchema installation path
        /// </summary>
        public string GetPalSchemaPath()
        {
            ThrowIfDisposed();
            return Path.Combine(GetModsPath(), "palschema");
        }

        public PalSchemaStatus DetectPalSchema()
        {
            ThrowIfDisposed();
            
            var cacheKey = $"palschema_status_{_palRoot.GetHashCode()}";
            var cached = _cache.Get<PalSchemaStatus>(cacheKey);
            
            if (cached != null && (DateTime.Now - cached.LastChecked).TotalMinutes < 5)
            {
                return cached;
            }

            var status = new PalSchemaStatus { LastChecked = DateTime.Now };
            
            try
            {
                var palSchemaPath = Path.Combine(_palRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods", "palschema");
                
                status.IsInstalled = Directory.Exists(palSchemaPath);
                status.FolderPath = palSchemaPath;

                if (status.IsInstalled)
                {
                    var modsPath = Path.Combine(palSchemaPath, "mods");
                    if (Directory.Exists(modsPath))
                    {
                        foreach (var modDir in Directory.GetDirectories(modsPath))
                        {
                            var modName = Path.GetFileName(modDir);
                            if (string.IsNullOrEmpty(modName))
                                continue;

                            var mod = new PalSchemaMod
                            {
                                Name = modName,
                                Path = modDir,
                                HasBlueprints = Directory.Exists(Path.Combine(modDir, "blueprints")),
                                HasItems = Directory.Exists(Path.Combine(modDir, "items")),
                                HasRaw = Directory.Exists(Path.Combine(modDir, "raw"))
                            };
                            
                            mod.IsValid = mod.HasBlueprints || mod.HasItems || mod.HasRaw;
                            status.Mods.Add(mod);
                        }
                    }
                }
            }
            catch (Exception)
            {
                // Log error but don't crash
                status.IsInstalled = false;
            }

            _cache.Set(cacheKey, status, TimeSpan.FromMinutes(5));
            return status;
        }

        // ✅ NEW: Secure process validation - no command injection
        public bool IsGameRunning()
        {
            ThrowIfDisposed();
            
            try
            {
                // Secure process checking without command injection
                var palworldProcesses = System.Diagnostics.Process.GetProcessesByName("Palworld");
                return palworldProcesses.Length > 0;
            }
            catch
            {
                return false;
            }
        }

        // ✅ NEW: Secure process termination
        public async Task<bool> TerminateGameProcessesAsync()
        {
            ThrowIfDisposed();
            
            return await Task.Run(() =>
            {
                try
                {
                    var processes = System.Diagnostics.Process.GetProcessesByName("Palworld");
                    bool success = true;
                    
                    foreach (var process in processes)
                    {
                        try
                        {
                            using (process)
                            {
                                if (!process.HasExited)
                                {
                                    process.Kill(true);
                                    process.WaitForExit(5000); // Wait up to 5 seconds
                                }
                            }
                        }
                        catch
                        {
                            success = false;
                        }
                    }
                    
                    return success;
                }
                catch
                {
                    return false;
                }
            });
        }

        // ✅ NEW: Validate file integrity without command injection
        public bool ValidateGameIntegrity()
        {
            ThrowIfDisposed();
            
            try
            {
                var gameExePath = Path.Combine(_palRoot, "Palworld.exe");
                return File.Exists(gameExePath);
            }
            catch
            {
                return false;
            }
        }

        private static string? ValidatePath(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
                return null;
                
            try
            {
                // Secure path validation to prevent path traversal
                string canonicalPath = Path.GetFullPath(path);
                
                // Ensure the path exists and contains a Palworld game
                if (!Directory.Exists(canonicalPath))
                    return null;
                    
                var gameExePath = Path.Combine(canonicalPath, "Palworld.exe");
                if (!File.Exists(gameExePath))
                    return null;
                    
                return canonicalPath;
            }
            catch
            {
                return null;
            }
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(UE4SSDetector));
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Clean up resources
                _disposed = true;
            }
        }

        ~UE4SSDetector()
        {
            Dispose(false);
        }
    }
}