using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    public class SmartBackupEngine : IDisposable
    {
        private readonly string _palRoot;
        private readonly UE4SSDetector _detector;
        private readonly BackupSettings _settings;
        private bool _disposed = false;
        
        // ✅ Performance optimization constants
        private const int BUFFER_SIZE = 1024 * 1024; // 1MB buffer
        private static readonly int MAX_PARALLEL_OPERATIONS = Environment.ProcessorCount; // Use processor count to optimize parallelism
        private const int BATCH_SIZE = 50; // Process files in batches
        
        private static readonly Dictionary<BackupCategory, string[]> CategoryPaths = new()
        {
            [BackupCategory.UE4SSCore] = new[]
            {
                @"Pal\Binaries\Win64\dwmapi.dll",
                @"Pal\Binaries\Win64\ue4ss\UE4SS.dll",
                @"Pal\Binaries\Win64\ue4ss\UE4SS-settings.ini",
                @"Pal\Binaries\Win64\ue4ss\LICENSE"
            },
            [BackupCategory.GameContentMods] = new[]
            {
                @"Pal\Content\Paks\~mods",
                @"Pal\Content\Paks\LogicMods"
            }
        };

        public SmartBackupEngine(string palRoot, UE4SSDetector detector, BackupSettings settings)
        {
            _palRoot = ValidatePath(palRoot) ?? throw new ArgumentException("Invalid Palworld root path", nameof(palRoot));
            _detector = detector ?? throw new ArgumentNullException(nameof(detector));
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
        }

        public List<string> GetFilesToBackup()
        {
            ThrowIfDisposed();
            
            var files = new List<string>();
            var ue4ssStatus = _detector.DetectUE4SS();
            var palSchemaStatus = _detector.DetectPalSchema();

            // UE4SS Core Files
            if (_settings.IncludeUE4SSCore)
            {
                files.AddRange(CategoryPaths[BackupCategory.UE4SSCore]
                    .Select(p => Path.Combine(_palRoot, p))
                    .Where(File.Exists));
            }

            // UE4SS Core Mods
            if (_settings.IncludeUE4SSCoreMods)
            {
                var modsPath = Path.Combine(_palRoot, @"Pal\Binaries\Win64\ue4ss\Mods");
                if (Directory.Exists(modsPath))
                {
                    foreach (var coreMod in ue4ssStatus.CoreMods)
                    {
                        var modPath = Path.Combine(modsPath, coreMod);
                        if (Directory.Exists(modPath))
                        {
                            files.AddRange(Directory.GetFiles(modPath, "*", SearchOption.AllDirectories));
                        }
                    }
                }
            }

            // User Mods
            if (_settings.IncludeUserMods)
            {
                var modsPath = Path.Combine(_palRoot, @"Pal\Binaries\Win64\ue4ss\Mods");
                if (Directory.Exists(modsPath))
                {
                    foreach (var userMod in ue4ssStatus.UserMods)
                    {
                        // Skip PalSchema if not included
                        if (userMod.Equals("palschema", StringComparison.OrdinalIgnoreCase) && 
                            !_settings.IncludePalSchemaSystem)
                        {
                            continue;
                        }

                        var modPath = Path.Combine(modsPath, userMod);
                        if (Directory.Exists(modPath))
                        {
                            files.AddRange(Directory.GetFiles(modPath, "*", SearchOption.AllDirectories));
                        }
                    }
                }
            }

            // Game Content Mods
            if (_settings.IncludeGameContentMods)
            {
                foreach (var path in CategoryPaths[BackupCategory.GameContentMods])
                {
                    var fullPath = Path.Combine(_palRoot, path);
                    if (Directory.Exists(fullPath))
                    {
                        files.AddRange(Directory.GetFiles(fullPath, "*", SearchOption.AllDirectories));
                    }
                }

                // Loose pak files
                var paksPath = Path.Combine(_palRoot, @"Pal\Content\Paks");
                if (Directory.Exists(paksPath))
                {
                    files.AddRange(Directory.GetFiles(paksPath, "*.pak")
                        .Where(f => !Path.GetFileName(f).Equals("Pal-Windows.pak", StringComparison.OrdinalIgnoreCase)));
                }
            }

            // PalSchema System
            if (_settings.IncludePalSchemaSystem && palSchemaStatus.IsInstalled)
            {
                var palSchemaPath = Path.Combine(_palRoot, @"Pal\Binaries\Win64\ue4ss\Mods\palschema");
                if (Directory.Exists(palSchemaPath))
                {
                    // Exclude mods subfolder if not including PalSchema mods
                    var palSchemaFiles = Directory.GetFiles(palSchemaPath, "*", SearchOption.AllDirectories);
                    if (!_settings.IncludePalSchemaMods)
                    {
                        var modsSubPath = Path.Combine(palSchemaPath, "mods");
                        palSchemaFiles = palSchemaFiles.Where(f => !f.StartsWith(modsSubPath, StringComparison.OrdinalIgnoreCase)).ToArray();
                    }
                    files.AddRange(palSchemaFiles);
                }
            }

            // PalSchema Mods
            if (_settings.IncludePalSchemaMods && palSchemaStatus.IsInstalled)
            {
                var palSchemaModsPath = Path.Combine(_palRoot, @"Pal\Binaries\Win64\ue4ss\Mods\palschema\mods");
                if (Directory.Exists(palSchemaModsPath))
                {
                    files.AddRange(Directory.GetFiles(palSchemaModsPath, "*", SearchOption.AllDirectories));
                }
            }

            return files.Distinct().ToList();
        }

        public long EstimateBackupSize()
        {
            ThrowIfDisposed();
            
            try
            {
                return GetFilesToBackup().AsParallel()
                    .WithDegreeOfParallelism(MAX_PARALLEL_OPERATIONS)
                    .Sum(f =>
                    {
                        try
                        {
                            return new FileInfo(f).Length;
                        }
                        catch
                        {
                            return 0L;
                        }
                    });
            }
            catch
            {
                return 0L;
            }
        }

        // ✅ NEW: High-performance async backup with optimized I/O
        public async Task<BackupResult> CreateBackupAsync(string backupPath, IProgress<BackupProgress>? progress = null, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            
            if (string.IsNullOrWhiteSpace(backupPath))
                throw new ArgumentException("Backup path cannot be null or empty", nameof(backupPath));

            var result = new BackupResult
            {
                StartTime = DateTime.Now,
                BackupPath = backupPath
            };

            try
            {
                var filesToBackup = GetFilesToBackup();
                var totalSize = EstimateBackupSize();
                
                result.TotalFiles = filesToBackup.Count;
                result.TotalBytes = totalSize;

                Directory.CreateDirectory(backupPath);

                // Process files in batches for better performance
                var processedBytes = 0L;
                var processedFiles = 0;

                var batches = filesToBackup
                    .Select((file, index) => new { File = file, Index = index })
                    .GroupBy(x => x.Index / BATCH_SIZE)
                    .Select(g => g.Select(x => x.File).ToList())
                    .ToList();

                foreach (var batch in batches)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    // Process batch in parallel with controlled concurrency
                    var semaphore = new SemaphoreSlim(MAX_PARALLEL_OPERATIONS, MAX_PARALLEL_OPERATIONS);
                    var batchTasks = batch.Select(async sourceFile =>
                    {
                        await semaphore.WaitAsync(cancellationToken);
                        try
                        {
                            var relativePath = Path.GetRelativePath(_palRoot, sourceFile);
                            var destinationFile = Path.Combine(backupPath, relativePath);
                            
                            // Ensure destination directory exists
                            var destDir = Path.GetDirectoryName(destinationFile);
                            if (!string.IsNullOrEmpty(destDir))
                            {
                                Directory.CreateDirectory(destDir);
                            }

                            // High-performance file copy with large buffer
                            await CopyFileAsync(sourceFile, destinationFile, cancellationToken);
                            
                            var fileSize = new FileInfo(sourceFile).Length;
                            
                            lock (result)
                            {
                                result.CopiedFiles.Add(relativePath);
                                processedFiles++;
                                processedBytes += fileSize;
                                
                                // Report progress
                                progress?.Report(new BackupProgress
                                {
                                    ProcessedFiles = processedFiles,
                                    TotalFiles = result.TotalFiles,
                                    ProcessedBytes = processedBytes,
                                    TotalBytes = result.TotalBytes,
                                    CurrentFile = relativePath
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            lock (result)
                            {
                                result.FailedFiles.Add(sourceFile);
                                result.Errors.Add($"Failed to copy {sourceFile}: {ex.Message}");
                            }
                        }
                        finally
                        {
                            semaphore.Release();
                        }
                    });

                    await Task.WhenAll(batchTasks);
                }

                // Create and save metadata
                var metadata = CreateBackupMetadata("Async backup");
                var metadataPath = Path.Combine(backupPath, "backup_metadata.json");
                await SaveMetadataAsync(metadata, metadataPath);

                result.EndTime = DateTime.Now;
                result.Success = result.FailedFiles.Count == 0;
                result.ProcessedFiles = processedFiles;
                result.ProcessedBytes = processedBytes;

                return result;
            }
            catch (Exception ex)
            {
                result.EndTime = DateTime.Now;
                result.Success = false;
                result.Errors.Add($"Backup operation failed: {ex.Message}");
                throw;
            }
        }

        // ✅ Optimized file copy with large buffer and async I/O
        private static async Task CopyFileAsync(string sourcePath, string destinationPath, CancellationToken cancellationToken)
        {
            using var sourceStream = new FileStream(sourcePath, FileMode.Open, FileAccess.Read, FileShare.Read, BUFFER_SIZE, FileOptions.SequentialScan);
            using var destinationStream = new FileStream(destinationPath, FileMode.Create, FileAccess.Write, FileShare.None, BUFFER_SIZE, FileOptions.SequentialScan);
            
            await sourceStream.CopyToAsync(destinationStream, BUFFER_SIZE, cancellationToken);
        }

        // ✅ NEW: Optimized metadata saving
        private async Task SaveMetadataAsync(BackupMetadata metadata, string metadataPath)
        {
            var options = new JsonSerializerOptions 
            { 
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            
            var json = JsonSerializer.Serialize(metadata, options);
            await File.WriteAllTextAsync(metadataPath, json);
        }

        // ✅ Enhanced backup verification
        public async Task<BackupVerificationResult> VerifyBackupAsync(string backupPath, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            
            var result = new BackupVerificationResult
            {
                StartTime = DateTime.Now,
                BackupPath = backupPath
            };

            try
            {
                if (!Directory.Exists(backupPath))
                {
                    result.IsValid = false;
                    result.Errors.Add("Backup directory does not exist");
                    return result;
                }

                // Load and verify metadata
                var metadataPath = Path.Combine(backupPath, "backup_metadata.json");
                if (!File.Exists(metadataPath))
                {
                    result.IsValid = false;
                    result.Errors.Add("Backup metadata not found");
                    return result;
                }

                var metadataJson = await File.ReadAllTextAsync(metadataPath, cancellationToken);
                var metadata = JsonSerializer.Deserialize<BackupMetadata>(metadataJson);
                
                if (metadata == null)
                {
                    result.IsValid = false;
                    result.Errors.Add("Invalid backup metadata");
                    return result;
                }

                result.Metadata = metadata;

                // Verify all backed up files exist
                var missingFiles = new List<string>();
                var semaphore = new SemaphoreSlim(MAX_PARALLEL_OPERATIONS, MAX_PARALLEL_OPERATIONS);
                
                var verificationTasks = metadata.BackedUpFiles.Select(async relativePath =>
                {
                    await semaphore.WaitAsync(cancellationToken);
                    try
                    {
                        var filePath = Path.Combine(backupPath, relativePath);
                        if (!File.Exists(filePath))
                        {
                            lock (missingFiles)
                            {
                                missingFiles.Add(relativePath);
                            }
                        }
                        else
                        {
                            lock (result)
                            {
                                result.VerifiedFiles++;
                            }
                        }
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                });

                await Task.WhenAll(verificationTasks);

                result.MissingFiles = missingFiles;
                result.IsValid = missingFiles.Count == 0;
                result.EndTime = DateTime.Now;

                if (!result.IsValid)
                {
                    result.Errors.Add($"{missingFiles.Count} files are missing from backup");
                }

                return result;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add($"Verification failed: {ex.Message}");
                result.EndTime = DateTime.Now;
                return result;
            }
        }

        public BackupMetadata CreateBackupMetadata(string reason = "Manual backup")
        {
            ThrowIfDisposed();
            
            return new BackupMetadata
            {
                BackupDate = DateTime.Now,
                BackupReason = reason,
                UE4SSStatus = _detector.DetectUE4SS(),
                PalSchemaStatus = _detector.DetectPalSchema(),
                BackupSettings = _settings,
                BackedUpFiles = GetFilesToBackup().Select(f => Path.GetRelativePath(_palRoot, f)).ToList(),
                TotalSize = EstimateBackupSize(),
                BackupVersion = "1.4.0",
                GameInstallPath = _palRoot
            };
        }

        private static string? ValidatePath(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
                return null;
                
            try
            {
                string canonicalPath = Path.GetFullPath(path);
                
                if (!Directory.Exists(canonicalPath))
                    return null;
                    
                var gameExePath = Path.Combine(canonicalPath, "Palworld.exe");
                if (!File.Exists(gameExePath))
                    return null;
                    
                return canonicalPath;
            }
            catch
            {
                return null;
            }
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(SmartBackupEngine));
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _disposed = true;
            }
        }

        ~SmartBackupEngine()
        {
            Dispose(false);
        }
    }

    // ✅ New models for enhanced backup functionality
    public class BackupResult
    {
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public string BackupPath { get; set; } = "";
        public bool Success { get; set; }
        public int TotalFiles { get; set; }
        public int ProcessedFiles { get; set; }
        public long TotalBytes { get; set; }
        public long ProcessedBytes { get; set; }
        public List<string> CopiedFiles { get; set; } = new();
        public List<string> FailedFiles { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? DateTime.Now.Subtract(StartTime);
        public double ProgressPercent => TotalFiles > 0 ? (double)ProcessedFiles / TotalFiles * 100 : 0;
    }

    public class BackupProgress
    {
        public int ProcessedFiles { get; set; }
        public int TotalFiles { get; set; }
        public long ProcessedBytes { get; set; }
        public long TotalBytes { get; set; }
        public string CurrentFile { get; set; } = "";
        public double FileProgressPercent => TotalFiles > 0 ? (double)ProcessedFiles / TotalFiles * 100 : 0;
        public double ByteProgressPercent => TotalBytes > 0 ? (double)ProcessedBytes / TotalBytes * 100 : 0;
    }

    public class BackupVerificationResult
    {
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public string BackupPath { get; set; } = "";
        public bool IsValid { get; set; }
        public BackupMetadata? Metadata { get; set; }
        public int VerifiedFiles { get; set; }
        public List<string> MissingFiles { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? DateTime.Now.Subtract(StartTime);
    }
}