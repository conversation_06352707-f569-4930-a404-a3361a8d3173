# User Guide - t7's Palworld Mod Installer

Welcome to the comprehensive user guide for the Palworld Mod Installer! This guide will help you get started and make the most of all the features.

## 📋 Table of Contents

1. [Getting Started](#getting-started)
2. [Basic Operations](#basic-operations)
3. [Advanced Features](#advanced-features)
4. [PalSchema Management](#palschema-management)
5. [Troubleshooting](#troubleshooting)
6. [FAQ](#faq)

## 🚀 Getting Started

### First Launch

1. **Download and Install**
   - Download the latest release from GitHub
   - Extract to your preferred directory
   - Run `PalworldModInstaller.exe`

2. **Initial Setup**
   - The application will automatically detect your Palworld installation
   - If not detected, you'll be prompted to select your Palworld folder
   - The installer will check for UE4SS and guide you through setup if needed

3. **Welcome Dialog**
   - First-time users will see a welcome dialog with quick start tips
   - Review the safety information and backup recommendations

### System Requirements

- **Operating System**: Windows 10/11 (64-bit)
- **Framework**: .NET 8.0 Runtime (automatically installed if missing)
- **Game**: Palworld (Steam or Epic Games Store)
- **Disk Space**: 50MB for application + space for mod backups
- **Memory**: 512MB RAM minimum, 1GB recommended

## 🎮 Basic Operations

### Installing Mods

#### Method 1: Drag and Drop
1. Download mod files (ZIP, RAR, 7Z formats supported)
2. Drag the mod archive into the main window
3. The installer will automatically detect the mod type and install it

#### Method 2: File Browser
1. Click "Install Mod" button
2. Browse and select your mod file
3. Follow the installation wizard

#### Method 3: Batch Installation
1. Select multiple mod files in the file browser
2. The installer will process them sequentially
3. Review the installation summary

### Managing Installed Mods

#### Viewing Installed Mods
- The main window shows all installed mods
- Use the filter options to find specific mods
- Sort by name, date installed, or status

#### Enabling/Disabling Mods
- Use the checkbox next to each mod to enable/disable
- Changes take effect immediately
- Disabled mods remain installed but inactive

#### Uninstalling Mods
1. Select the mod you want to remove
2. Click "Uninstall" or right-click and select "Remove"
3. Confirm the removal in the dialog

### Backup and Restore

#### Automatic Backups
- The installer automatically creates backups before major changes
- Backups include game files and mod configurations
- Backup location: `%AppData%/PalworldModInstaller/Backups`

#### Manual Backups
1. Click "Create Backup" in the main toolbar
2. Enter a descriptive name for the backup
3. Select what to include (game files, mods, settings)
4. Wait for the backup to complete

#### Restoring from Backup
1. Click "Restore" in the main toolbar
2. Select the backup you want to restore from
3. Choose what to restore (full or partial)
4. Confirm the restoration

## 🔧 Advanced Features

### Mod Profiles

#### Creating Profiles
1. Open "Mod Manager" from the main menu
2. Click "New Profile"
3. Enter a name and description
4. Select which mods to include
5. Save the profile

#### Switching Profiles
1. Open the profile dropdown in the main toolbar
2. Select the profile you want to activate
3. The installer will automatically enable/disable mods as needed

#### Sharing Profiles
1. Right-click on a profile and select "Export"
2. Choose export format (JSON or PMCX)
3. Share the exported file with other users
4. Others can import using "Import Profile"

### Conflict Detection

#### Automatic Detection
- The installer automatically detects mod conflicts
- Conflicts are highlighted in red in the mod list
- Hover over the warning icon for details

#### Resolving Conflicts
1. Click on a conflicted mod to see details
2. Review the conflict resolution suggestions
3. Choose to disable conflicting mods or adjust load order
4. Apply the changes

### Load Order Management

#### Understanding Load Order
- Mods are loaded in a specific order
- Later mods can override earlier ones
- Critical for mods that modify the same files

#### Adjusting Load Order
1. Open "Load Order" from the main menu
2. Drag and drop mods to reorder them
3. Use the arrow buttons for fine adjustments
4. Click "Apply" to save changes

## 🎯 PalSchema Management

### What is PalSchema?
PalSchema is an advanced configuration system for Palworld that allows deep customization of game mechanics, items, and behaviors.

### Configuration Editor

#### Visual Editor
1. Click "PalSchema Config" in the main toolbar
2. Use the visual editor to modify settings
3. Changes are validated in real-time
4. Preview changes before applying

#### JSON Editor
1. Switch to the "JSON Editor" tab
2. Edit the configuration directly
3. Syntax highlighting and validation included
4. Auto-completion for known properties

#### Property Editor
1. Use the "Property Editor" for guided configuration
2. Organized by categories (Pals, Items, World, etc.)
3. Tooltips explain each setting
4. Input validation prevents errors

### Configuration Profiles

#### Creating PalSchema Profiles
1. Configure your desired settings
2. Click "Save as Profile"
3. Enter a name and description
4. The profile is saved for future use

#### Comparing Profiles
1. Select two profiles to compare
2. Click "Compare" to see differences
3. Review changes highlighted in the diff view
4. Merge changes if desired

### Advanced Tools

#### Configuration Validation
- Automatic validation of all settings
- Warnings for deprecated properties
- Error detection for invalid values
- Performance impact analysis

#### Optimization Suggestions
- The system analyzes your configuration
- Suggests optimizations for better performance
- Identifies potential conflicts
- Recommends best practices

## 🛠️ Troubleshooting

### Common Issues

#### "Palworld not detected"
**Solution:**
1. Ensure Palworld is installed
2. Check that `Palworld.exe` exists in the game directory
3. Manually select the Palworld folder if auto-detection fails

#### "UE4SS not found"
**Solution:**
1. The installer can automatically install UE4SS
2. Click "Install UE4SS" when prompted
3. Alternatively, download UE4SS manually and place in the correct directory

#### "Mod installation failed"
**Possible Causes:**
- Corrupted mod file
- Insufficient disk space
- File permissions issues
- Antivirus interference

**Solutions:**
1. Re-download the mod file
2. Free up disk space
3. Run as administrator
4. Add exception to antivirus

#### "Game crashes after mod installation"
**Solutions:**
1. Check the diagnostic tools for conflicts
2. Disable recently installed mods one by one
3. Restore from a backup
4. Check mod compatibility

### Diagnostic Tools

#### System Diagnostic
1. Open "Diagnostic Tools" from the Help menu
2. Click "Run System Diagnostic"
3. Review the generated report
4. Follow the recommended fixes

#### Common Issue Detection
1. The system automatically detects common problems
2. Auto-fix options are provided where safe
3. Manual fix instructions for complex issues
4. Export diagnostic reports for support

#### Performance Analysis
1. Monitor mod impact on game performance
2. Identify resource-heavy mods
3. Get optimization recommendations
4. Track performance over time

### Getting Help

#### Built-in Help
- Tooltips throughout the interface
- Context-sensitive help (F1 key)
- Status bar shows current operation details

#### Community Support
- GitHub Issues for bug reports
- GitHub Discussions for questions
- Discord community for real-time help
- User forums for mod-specific help

#### Log Files
- Location: `%AppData%/PalworldModInstaller/Logs`
- Include log files when reporting issues
- Logs are automatically rotated and cleaned

## ❓ FAQ

### General Questions

**Q: Is this safe to use?**
A: Yes, the installer includes comprehensive safety features including automatic backups, conflict detection, and validation.

**Q: Will this affect my save games?**
A: The installer only modifies game files, not save data. However, some mods may affect save compatibility.

**Q: Can I use this with multiplayer?**
A: Some mods work in multiplayer, others don't. Check mod descriptions and ensure all players have compatible mods.

### Technical Questions

**Q: What mod formats are supported?**
A: ZIP, RAR, 7Z, and direct folder installation.

**Q: How do I update mods?**
A: Download the new version and install over the existing mod. The installer will handle the update.

**Q: Can I install mods manually and still use this tool?**
A: Yes, the installer will detect manually installed mods and include them in management.

### Troubleshooting Questions

**Q: The application won't start**
A: Ensure .NET 8.0 Runtime is installed. Check Windows Event Viewer for error details.

**Q: Mods aren't working in game**
A: Verify UE4SS is properly installed and enabled. Check the diagnostic tools for issues.

**Q: How do I completely uninstall everything?**
A: Use the "Complete Uninstall" option in settings, or restore from a pre-mod backup.

## 📞 Support

If you need additional help:
- Check the troubleshooting section above
- Search existing GitHub issues
- Create a new issue with detailed information
- Join the community Discord for real-time help

Remember to include your system information and log files when reporting issues!
