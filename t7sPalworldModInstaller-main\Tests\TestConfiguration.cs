using System;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using System.Threading;
using NUnit.Framework;

namespace ModInstallerApp.Tests
{
    /// <summary>
    /// Global test configuration and setup
    /// </summary>
    [SetUpFixture]
    public class TestConfiguration
    {
        public static string TestDataRoot { get; private set; } = string.Empty;
        public static string TestResourcesPath { get; private set; } = string.Empty;

        [OneTimeSetUp]
        public void GlobalSetUp()
        {
            // Set up global test environment
            TestDataRoot = Path.Combine(Path.GetTempPath(), "PalworldModInstallerTests", DateTime.Now.ToString("yyyyMMdd_HHmmss"));
            TestResourcesPath = Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location)!, "TestResources");
            
            Directory.CreateDirectory(TestDataRoot);
            Directory.CreateDirectory(TestResourcesPath);
            
            // Create test resources
            CreateTestResources();
            
            TestContext.WriteLine($"Global test setup completed. Test data root: {TestDataRoot}");
        }

        [OneTimeTearDown]
        public void GlobalTearDown()
        {
            try
            {
                if (Directory.Exists(TestDataRoot))
                {
                    Directory.Delete(TestDataRoot, true);
                }
                TestContext.WriteLine("Global test cleanup completed");
            }
            catch (Exception ex)
            {
                TestContext.WriteLine($"Warning: Could not clean up test data root: {ex.Message}");
            }
        }

        private void CreateTestResources()
        {
            // Create sample mod archives for testing
            CreateSampleUE4SSMod();
            CreateSamplePalSchemaMod();
            CreateSamplePAKMod();
            CreateMaliciousTestArchive();
        }

        private void CreateSampleUE4SSMod()
        {
            var modPath = Path.Combine(TestResourcesPath, "SampleUE4SSMod");
            Directory.CreateDirectory(modPath);
            
            File.WriteAllText(Path.Combine(modPath, "main.lua"), @"
-- Sample UE4SS Mod
print('Sample UE4SS Mod loaded')

-- Register mod hooks
RegisterHook('/Script/Pal.PalPlayerCharacter:GetMaxHP', function(self)
    return 1000 -- Increase max HP
end)
");
            
            File.WriteAllText(Path.Combine(modPath, "enabled.txt"), "");
            File.WriteAllText(Path.Combine(modPath, "mod.json"), @"{
    ""name"": ""Sample UE4SS Mod"",
    ""version"": ""1.0.0"",
    ""description"": ""A sample UE4SS mod for testing"",
    ""author"": ""Test Author""
}");
        }

        private void CreateSamplePalSchemaMod()
        {
            var modPath = Path.Combine(TestResourcesPath, "SamplePalSchemaMod");
            var blueprintsPath = Path.Combine(modPath, "blueprints");
            var itemsPath = Path.Combine(modPath, "items");
            var rawPath = Path.Combine(modPath, "raw");
            
            Directory.CreateDirectory(blueprintsPath);
            Directory.CreateDirectory(itemsPath);
            Directory.CreateDirectory(rawPath);
            
            File.WriteAllText(Path.Combine(modPath, "mod.json"), @"{
    ""name"": ""Sample PalSchema Mod"",
    ""version"": ""1.0.0"",
    ""description"": ""A sample PalSchema mod for testing"",
    ""author"": ""Test Author"",
    ""palschema_version"": ""1.0.0""
}");
            
            File.WriteAllText(Path.Combine(itemsPath, "custom_item.json"), @"{
    ""id"": ""custom_item_001"",
    ""name"": ""Custom Test Item"",
    ""description"": ""A custom item for testing"",
    ""type"": ""consumable"",
    ""rarity"": ""common""
}");
            
            File.WriteAllText(Path.Combine(blueprintsPath, "custom_blueprint.json"), @"{
    ""id"": ""custom_blueprint_001"",
    ""name"": ""Custom Test Blueprint"",
    ""description"": ""A custom blueprint for testing"",
    ""category"": ""structures""
}");
        }

        private void CreateSamplePAKMod()
        {
            var pakPath = Path.Combine(TestResourcesPath, "SamplePAKMod.pak");
            
            // Create a mock PAK file (in reality this would be a proper UE4 PAK file)
            File.WriteAllText(pakPath, "Mock PAK file content for testing");
        }

        private void CreateMaliciousTestArchive()
        {
            var maliciousPath = Path.Combine(TestResourcesPath, "MaliciousArchive");
            Directory.CreateDirectory(maliciousPath);
            
            // Create files that would trigger security warnings
            File.WriteAllText(Path.Combine(maliciousPath, "malicious.exe"), "Fake executable");
            File.WriteAllText(Path.Combine(maliciousPath, "script.bat"), "@echo off\necho Malicious batch file");
            
            // Create path traversal attempt
            var traversalDir = Path.Combine(maliciousPath, "..", "..", "traversal_attempt");
            try
            {
                Directory.CreateDirectory(traversalDir);
                File.WriteAllText(Path.Combine(traversalDir, "malicious.txt"), "Path traversal attempt");
            }
            catch
            {
                // Ignore if path traversal creation fails
            }
        }
    }

    /// <summary>
    /// Test categories for organizing tests
    /// </summary>
    public static class TestCategories
    {
        public const string Unit = "Unit";
        public const string Integration = "Integration";
        public const string Security = "Security";
        public const string Performance = "Performance";
        public const string UI = "UI";
        public const string ErrorRecovery = "ErrorRecovery";
    }

    /// <summary>
    /// Test utilities and helper methods
    /// </summary>
    public static class TestUtilities
    {
        /// <summary>
        /// Creates a temporary directory for test use
        /// </summary>
        public static string CreateTempDirectory(string prefix = "test")
        {
            var tempPath = Path.Combine(TestConfiguration.TestDataRoot, $"{prefix}_{Guid.NewGuid():N}");
            Directory.CreateDirectory(tempPath);
            return tempPath;
        }

        /// <summary>
        /// Creates a temporary file with specified content
        /// </summary>
        public static string CreateTempFile(string content, string extension = ".txt")
        {
            var tempPath = Path.Combine(TestConfiguration.TestDataRoot, $"temp_{Guid.NewGuid():N}{extension}");
            File.WriteAllText(tempPath, content);
            return tempPath;
        }

        /// <summary>
        /// Waits for a condition to be true with timeout
        /// </summary>
        public static async Task<bool> WaitForConditionAsync(Func<bool> condition, TimeSpan timeout, TimeSpan? interval = null)
        {
            interval ??= TimeSpan.FromMilliseconds(100);
            var endTime = DateTime.UtcNow.Add(timeout);
            
            while (DateTime.UtcNow < endTime)
            {
                if (condition())
                    return true;
                
                await Task.Delay(interval.Value);
            }
            
            return false;
        }

        /// <summary>
        /// Asserts that an async operation completes within the specified timeout
        /// </summary>
        public static async Task AssertCompletesWithinAsync(Func<Task> operation, TimeSpan timeout)
        {
            using var cts = new CancellationTokenSource(timeout);
            
            try
            {
                await operation();
            }
            catch (OperationCanceledException) when (cts.Token.IsCancellationRequested)
            {
                Assert.Fail($"Operation did not complete within {timeout.TotalSeconds} seconds");
            }
        }

        /// <summary>
        /// Generates test data for performance testing
        /// </summary>
        public static byte[] GenerateTestData(int sizeInBytes)
        {
            var random = new Random(42); // Use fixed seed for reproducible tests
            var data = new byte[sizeInBytes];
            random.NextBytes(data);
            return data;
        }

        /// <summary>
        /// Measures execution time of an operation
        /// </summary>
        public static async Task<TimeSpan> MeasureExecutionTimeAsync(Func<Task> operation)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            await operation();
            stopwatch.Stop();
            return stopwatch.Elapsed;
        }

        /// <summary>
        /// Verifies that a directory structure matches expected layout
        /// </summary>
        public static void VerifyDirectoryStructure(string rootPath, params string[] expectedPaths)
        {
            Assert.That(Directory.Exists(rootPath), $"Root directory should exist: {rootPath}");
            
            foreach (var expectedPath in expectedPaths)
            {
                var fullPath = Path.Combine(rootPath, expectedPath);
                var exists = File.Exists(fullPath) || Directory.Exists(fullPath);
                Assert.That(exists, $"Expected path should exist: {fullPath}");
            }
        }
    }
}
