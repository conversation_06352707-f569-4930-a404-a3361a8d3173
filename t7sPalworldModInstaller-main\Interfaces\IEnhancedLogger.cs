using System;

namespace ModInstallerApp.Interfaces
{
    /// <summary>
    /// Interface for enhanced logging operations
    /// </summary>
    public interface IEnhancedLogger : IDisposable
    {
        /// <summary>
        /// Logs an informational message
        /// </summary>
        /// <param name="message">Message to log</param>
        void LogInfo(string message);

        /// <summary>
        /// Logs a warning message
        /// </summary>
        /// <param name="message">Warning message to log</param>
        void LogWarning(string message);

        /// <summary>
        /// Logs an error message
        /// </summary>
        /// <param name="message">Error message to log</param>
        void LogError(string message);

        /// <summary>
        /// Logs an error with exception details
        /// </summary>
        /// <param name="message">Error message to log</param>
        /// <param name="exception">Exception to log</param>
        void LogError(string message, Exception exception);

        /// <summary>
        /// Logs a debug message
        /// </summary>
        /// <param name="message">Debug message to log</param>
        void LogDebug(string message);

        /// <summary>
        /// Logs a verbose message
        /// </summary>
        /// <param name="message">Verbose message to log</param>
        void LogVerbose(string message);

        /// <summary>
        /// Logs a success message
        /// </summary>
        /// <param name="message">Success message to log</param>
        void LogSuccess(string message);

        /// <summary>
        /// Logs a critical error message
        /// </summary>
        /// <param name="message">Critical error message to log</param>
        void LogCritical(string message);

        /// <summary>
        /// Logs a critical error with exception details
        /// </summary>
        /// <param name="message">Critical error message to log</param>
        /// <param name="exception">Exception to log</param>
        void LogCritical(string message, Exception exception);

        /// <summary>
        /// Flushes any pending log entries
        /// </summary>
        void Flush();

        /// <summary>
        /// Gets or sets the minimum log level
        /// </summary>
        LogLevel MinimumLogLevel { get; set; }
    }

    /// <summary>
    /// Log levels for filtering
    /// </summary>
    public enum LogLevel
    {
        Verbose = 0,
        Debug = 1,
        Info = 2,
        Warning = 3,
        Error = 4,
        Critical = 5
    }
}
