using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Security;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using ModInstallerApp.Models;
using ModInstallerApp.Services;

namespace ModInstallerApp.UI
{
    public class InstallerForm : Form, IDisposable
    {
        const string VERSION = "1.4.0";
        const string DRIVE_URL = "https://drive.google.com/file/d/18NA2mcNTSZV6qkOFDHx69vISh4UN85Dk/view?usp=sharing";
        const string DISCORD_XD = "https://discord.xdreamserver.com";
        const string DISCORD_GL = "https://www.gladiate.net";
        const string MANIFEST = "mod_manifest.json";
        const string VANILLA_PAK = "Pal-Windows.pak";

        static readonly string CHANGELOG =
@"v1.4.0 – 2025-06-14 (Phase 1.1)
• Added UE4SS detection system
• Added PalSchema detection
• Implemented smart backup logic with categories
• Added selective backup UI with options dialog
• Enhanced UI with status indicators
• Added backup metadata system
• Improved logging and diagnostics
• Smart restore with conflict resolution
• Auto-maintenance and integrity checking
• Performance monitoring and metrics
• Backup optimization features

v1.3.0 – 2025-06-14 (Phase 0.1)
• Added smart caching system - remembers Palworld directory
• Auto-detection of Steam/Epic installations
• Enhanced UI with recent installations dropdown
• Improved first-run experience
• Session state management (window position/size)
• Application data management and settings
• Removed Verify Integrity feature (replaced by UE4SS detection)

v1.2.2 – 2025-06-07
• Backup now always copy-then-delete (no directory moves) to avoid ransomware heuristics.
• explorer.exe launches via UseShellExecute = true for reputation.
• Process termination still optional (user prompt).";

        static readonly string[] MOD_PATHS =
        {
            @"Pal\Binaries\Win64\mods",
            @"Pal\Binaries\Win64\ue4ss",
            @"Pal\Binaries\Win64\UE4SS_Signatures",
            @"Pal\Binaries\Win64\dwmapi.dll",
            @"Pal\Content\Paks\~mods",
            @"Pal\Content\Paks\LogicMods"
        };

        static readonly string BACKUP_ROOT =
            Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile),
                         @"Downloads\Palworld Mods Backup");

        static readonly string DEFAULT_STEAM =
            @"C:\Program Files (x86)\Steam\steamapps\common";

        readonly TextBox txtPath = new() { ReadOnly = true, Dock = DockStyle.Fill };
        readonly ComboBox cmbRecentPaths = new()
        {
            Dock = DockStyle.Fill,
            DropDownStyle = ComboBoxStyle.DropDownList
        };
        readonly ThemedProgressBar bar = new() { Dock = DockStyle.Fill, Height = 22 };
        readonly RichTextBox logBox = new()
        {
            Dock = DockStyle.Fill,
            ReadOnly = true,
            BackColor = Theme.Surface,
            BorderStyle = BorderStyle.FixedSingle,
            Font = new Font("Consolas", 9),
            ForeColor = Theme.TextLight
        };
        readonly TableLayoutPanel root = new();

        // Status indicators
        readonly StatusIndicator ue4ssIndicator = new();
        readonly StatusIndicator palSchemaIndicator = new();

        // Services
        private readonly AppDataManager _appData = AppDataManager.Instance;
        private UE4SSDetector? _currentDetector;
        private MaintenanceEngine? _maintenanceEngine;
        private RestoreEngine? _restoreEngine;
        private EnhancedLogger? _logger;
        private PalSchemaConfigurationService? _palSchemaConfigService;
        private PalSchemaProfileManager? _palSchemaProfileManager;
        private PalSchemaAdvancedTools? _palSchemaAdvancedTools;

        // Advanced services
        private ModManagerService? _modManagerService;
        private ModCollectionService? _modCollectionService;
        private DiagnosticService? _diagnosticService;
        private EnhancedInstallationEngine? _installationEngine;

        // ✅ NEW: Cancellation token support
        private CancellationTokenSource? _operationCancellationSource;
        private bool _disposed = false;

        public InstallerForm()
        {
            InitializeServices();
            
            Icon appIcon = CreateAppIcon();
            Icon = appIcon;

            Text = "t7's Palworld Mod-Installer";
            MinimumSize = new Size(740, 560);
            StartPosition = FormStartPosition.CenterScreen;

            // Load cached window state
            var windowState = _appData.GetWindowState();
            if (_appData.Settings.Preferences.RememberWindowPosition)
            {
                Location = windowState.Location;
                Size = windowState.Size;
                WindowState = windowState.IsMaximized ? FormWindowState.Maximized : FormWindowState.Normal;
            }

            Theme.Apply(this);

            BuildLayout(appIcon);
            InitializeCachedData();

            Resize += (_, __) => CenterForm();
            Shown += OnFormShown;

            // ✅ FIX: Proper event handler cleanup to prevent memory leaks
            FormClosing += OnFormClosing;
        }

        private async void OnFormShown(object? sender, EventArgs e)
        {
            try
            {
                CenterForm();
                if (_appData.Settings.IsFirstRun)
                {
                    await ShowFirstRunExperienceAsync();
                }
                else
                {
                    Log($"Installer ready. Welcome back! (Startup #{_appData.Settings.StartupCount})");
                }
                await UpdateStatusIndicatorsAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error during form shown", "UI", ex);
            }
        }

        private void OnFormClosing(object? sender, FormClosingEventArgs e)
        {
            try
            {
                // Cancel any running operations
                _operationCancellationSource?.Cancel();
                
                SaveApplicationState();
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error during form closing", "UI", ex);
            }
        }

        private void InitializeServices()
        {
            // Initialize enhanced logging
            _logger = new EnhancedLogger(_appData.AppDataPath);
            _logger.LogEntryAdded += OnLogEntryAdded;

            _logger.LogInfo("Application starting", "System", new { Version = VERSION });
        }

        private void InitializePalSchemaServices(string palRoot)
        {
            try
            {
                if (_currentDetector != null && _logger != null)
                {
                    var cacheManager = new CacheManager(_appData.AppDataPath);
                    _palSchemaConfigService = new PalSchemaConfigurationService(palRoot, _currentDetector, _logger, cacheManager);
                    _palSchemaProfileManager = new PalSchemaProfileManager(_palSchemaConfigService, _logger);
                    _palSchemaAdvancedTools = new PalSchemaAdvancedTools(_palSchemaConfigService, _palSchemaProfileManager, _logger);

                    _logger.LogInfo("PalSchema services initialized", "PalSchema");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to initialize PalSchema services", "PalSchema", ex);
            }
        }

        private async Task InitializeAdvancedServices(string palRoot)
        {
            try
            {
                if (_currentDetector != null && _logger != null)
                {
                    var cacheManager = new CacheManager(_appData.AppDataPath);

                    // Initialize installation engine
                    _installationEngine = new EnhancedInstallationEngine(palRoot, _currentDetector, cacheManager, _logger);

                    // Initialize mod manager service
                    _modManagerService = new ModManagerService(palRoot, _installationEngine, _logger);
                    await _modManagerService.InitializeAsync();

                    // Initialize mod collection service (requires PalSchema profile manager)
                    if (_palSchemaProfileManager != null)
                    {
                        _modCollectionService = new ModCollectionService(palRoot, _modManagerService, _palSchemaProfileManager, _logger);
                        await _modCollectionService.InitializeAsync();
                    }

                    // Initialize diagnostic service
                    _diagnosticService = new DiagnosticService(palRoot, _currentDetector, _modManagerService, _logger);

                    _logger.LogInfo("Advanced services initialized", "AdvancedServices");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to initialize advanced services", "AdvancedServices", ex);
            }
        }

        private void OnLogEntryAdded(LogEntry logEntry)
        {
            // ✅ FIX: Ensure UI updates are on UI thread
            if (logBox.InvokeRequired)
            {
                logBox.Invoke(() => OnLogEntryAdded(logEntry));
                return;
            }

            var timestamp = logEntry.Timestamp.ToString("HH:mm:ss");
            var levelIcon = logEntry.Level switch
            {
                LogLevel.Error => "❌",
                LogLevel.Warning => "⚠️",
                LogLevel.Info => "ℹ️",
                LogLevel.Operation => "⚙️",
                LogLevel.Debug => "🔍",
                _ => "📝"
            };

            logBox.AppendText($"[{timestamp}] {levelIcon} {logEntry.Message}\n");
            logBox.ScrollToCaret();
        }

        // ── UI builders ──
        LinkLabel MakeLink(string text, string url)
        {
            var ll = new LinkLabel { Text = text, AutoSize = true, LinkColor = Theme.Accent2, Margin = new Padding(0, 0, 15, 0) };
            ll.LinkClicked += (_, __) =>
            {
                try
                {
                    Process.Start(new ProcessStartInfo(url) { UseShellExecute = true });
                }
                catch (Exception ex)
                {
                    _logger?.LogError($"Failed to open link: {url}", "UI", ex);
                }
            };
            return ll;
        }

        void BuildLayout(Icon appIcon)
        {
            root.AutoSize = true;
            root.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            root.Anchor = AnchorStyles.None;
            root.ColumnCount = 1;
            root.RowCount = 15;

            // Define row styles
            root.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 0: Header
            root.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 1: Status indicators
            root.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 2: Path selection
            root.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 3: Recent paths
            root.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 4: Main buttons
            root.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 5: Backup buttons
            root.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 6: Maintenance buttons
            root.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 7: PalSchema buttons
            root.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 8: Advanced features buttons
            root.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 9: Progress bar
            root.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // 10: Log box (expandable)
            root.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 11: Links
            root.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 12: Changelog button
            root.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 13: Version
            root.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // 14: Credits

            // Header with icon and title
            var headerPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            var iconPicture = new PictureBox { Image = appIcon.ToBitmap(), Size = new Size(48, 48), SizeMode = PictureBoxSizeMode.Zoom };
            var titleLabel = new Label { Text = "t7's Palworld Mod-Installer", Font = Theme.HeadingFont, AutoSize = true, Margin = new Padding(10, 10, 0, 0) };
            headerPanel.Controls.AddRange(new Control[] { iconPicture, titleLabel });

            // Status indicators panel
            var statusPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            statusPanel.Controls.AddRange(new Control[] { ue4ssIndicator, palSchemaIndicator });

            // Path selection
            var pathPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            var btnBrowse = new ThemedButton { Text = "Browse for Palworld.exe" };
            btnBrowse.Click += BrowseForPalworld;
            pathPanel.Controls.AddRange(new Control[] { new Label { Text = "Game Path:", AutoSize = true, Margin = new Padding(0, 6, 5, 0) }, txtPath, btnBrowse });

            // Recent paths
            var recentPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            cmbRecentPaths.SelectedIndexChanged += RecentPathSelected;
            recentPanel.Controls.AddRange(new Control[] { new Label { Text = "Recent:", AutoSize = true, Margin = new Padding(0, 6, 5, 0) }, cmbRecentPaths });

            // Main buttons
            var mainButtonPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            var btnInstall = new ThemedButton { Text = "📦 Install Mods" };
            var btnRemove = new ThemedButton { Text = "🗑️ Remove All Mods" };
            var btnRefresh = new ThemedButton { Text = "🔄 Refresh Status" };
            btnInstall.Click += InstallMods;
            btnRemove.Click += RemoveMods;
            btnRefresh.Click += RefreshStatus;
            mainButtonPanel.Controls.AddRange(new Control[] { btnInstall, btnRemove, btnRefresh });

            // Backup buttons
            var backupButtonPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            var btnBackup = new ThemedButton { Text = "💾 Smart Backup" };
            var btnRestore = new ThemedButton { Text = "📂 Restore Backup" };
            var btnManageBackups = new ThemedButton { Text = "🗂️ Manage Backups" };
            btnBackup.Click += CreateBackup;
            btnRestore.Click += RestoreBackup;
            btnManageBackups.Click += ManageBackups;
            backupButtonPanel.Controls.AddRange(new Control[] { btnBackup, btnRestore, btnManageBackups });

            // Maintenance buttons
            var maintenanceButtonPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            var btnMaintenance = new ThemedButton { Text = "🔧 Run Maintenance" };
            var btnIntegrityCheck = new ThemedButton { Text = "🔍 Integrity Check" };
            var btnCleanup = new ThemedButton { Text = "🧹 Cleanup Files" };
            var btnDiagnostics = new ThemedButton { Text = "📊 Export Diagnostics" };
            btnMaintenance.Click += RunFullMaintenance;
            btnIntegrityCheck.Click += RunIntegrityCheck;
            btnCleanup.Click += RunCleanup;
            btnDiagnostics.Click += ExportDiagnostics;
            maintenanceButtonPanel.Controls.AddRange(new Control[] { btnMaintenance, btnIntegrityCheck, btnCleanup, btnDiagnostics });

            // PalSchema management buttons
            var palSchemaButtonPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            var btnPalSchemaConfig = new ThemedButton { Text = "⚙️ PalSchema Config" };
            var btnPalSchemaProfiles = new ThemedButton { Text = "📋 Schema Profiles" };
            var btnPalSchemaTools = new ThemedButton { Text = "🔧 Schema Tools" };
            btnPalSchemaConfig.Click += OpenPalSchemaConfiguration;
            btnPalSchemaProfiles.Click += OpenPalSchemaProfiles;
            btnPalSchemaTools.Click += OpenPalSchemaTools;
            palSchemaButtonPanel.Controls.AddRange(new Control[] { btnPalSchemaConfig, btnPalSchemaProfiles, btnPalSchemaTools });

            // Advanced features buttons
            var advancedButtonPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            var btnModManager = new ThemedButton { Text = "🎮 Mod Manager" };
            var btnCollectionSharing = new ThemedButton { Text = "📦 Collection Sharing" };
            var btnDiagnosticTools = new ThemedButton { Text = "🔧 Diagnostic Tools" };
            btnModManager.Click += OpenModManager;
            btnCollectionSharing.Click += OpenCollectionSharing;
            btnDiagnosticTools.Click += OpenDiagnosticTools;
            advancedButtonPanel.Controls.AddRange(new Control[] { btnModManager, btnCollectionSharing, btnDiagnosticTools });

            // Links
            var linksPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            linksPanel.Controls.AddRange(new Control[] {
                MakeLink("📁 Download Mods", DRIVE_URL),
                MakeLink("💬 XDream Discord", DISCORD_XD),
                MakeLink("🎮 Gladiate Discord", DISCORD_GL)
            });

            // Changelog button
            var changelogPanel = new FlowLayoutPanel { AutoSize = true, FlowDirection = FlowDirection.LeftToRight };
            var btnChangelog = new ThemedButton { Text = "📋 View Changelog" };
            btnChangelog.Click += ShowChangelog;
            changelogPanel.Controls.Add(btnChangelog);

            // Version and credits
            var versionLabel = new Label { Text = $"Version {VERSION}", AutoSize = true, Font = Theme.SmallFont };
            var creditsLabel = new Label { Text = "Made with ❤️ by t7", AutoSize = true, Font = Theme.SmallFont };

            // Add all to root layout
            root.Controls.Add(headerPanel, 0, 0);
            root.Controls.Add(statusPanel, 0, 1);
            root.Controls.Add(pathPanel, 0, 2);
            root.Controls.Add(recentPanel, 0, 3);
            root.Controls.Add(mainButtonPanel, 0, 4);
            root.Controls.Add(backupButtonPanel, 0, 5);
            root.Controls.Add(maintenanceButtonPanel, 0, 6);
            root.Controls.Add(palSchemaButtonPanel, 0, 7);
            root.Controls.Add(advancedButtonPanel, 0, 8);
            root.Controls.Add(bar, 0, 9);
            root.Controls.Add(logBox, 0, 10);
            root.Controls.Add(linksPanel, 0, 11);
            root.Controls.Add(changelogPanel, 0, 12);
            root.Controls.Add(versionLabel, 0, 13);
            root.Controls.Add(creditsLabel, 0, 14);

            Controls.Add(root);
        }

        // ── Event handlers ──
        private void BrowseForPalworld(object? sender, EventArgs e)
        {
            try
            {
                using var ofd = new OpenFileDialog
                {
                    Title = "Select Palworld.exe",
                    Filter = "Palworld Executable|Palworld.exe",
                    InitialDirectory = DEFAULT_STEAM
                };

                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    txtPath.Text = ofd.FileName;
                    _appData.SetLastPalworldPath(ofd.FileName);
                    CreateDetectorForPath(Path.GetDirectoryName(ofd.FileName)!);
                    _logger?.LogInfo($"Game path set to: {ofd.FileName}", "UI");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error browsing for Palworld", "UI", ex);
                ShowUserFriendlyError("Failed to browse for Palworld.exe", ex);
            }
        }

        private async void RecentPathSelected(object? sender, EventArgs e)
        {
            try
            {
                if (cmbRecentPaths.SelectedItem is PalworldInstallation selectedInstall)
                {
                    txtPath.Text = selectedInstall.Path;
                    _appData.SetLastPalworldPath(selectedInstall.Path);
                    CreateDetectorForPath(Path.GetDirectoryName(selectedInstall.Path)!);
                    _logger?.LogInfo($"Selected: {selectedInstall.DisplayName}", "UI");
                    await UpdateStatusIndicatorsAsync();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error selecting recent path", "UI", ex);
            }
        }

        // ── MOD INSTALLATION LOGIC ──
        private async void InstallMods(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateGamePath()) return;

                MessageBox.Show("Select any .zip that contains a 'Pal' folder inside.",
                                "Choose Mod-Pack ZIP", MessageBoxButtons.OK, MessageBoxIcon.Information);

                using var ofd = new OpenFileDialog { Filter = "ZIP files (*.zip)|*.zip|All files|*.*" };
                if (ofd.ShowDialog() != DialogResult.OK) return;

                string zipPath = ofd.FileName;

                // Validate ZIP file
                using var fsCheck = File.OpenRead(zipPath);
                if (!(fsCheck.ReadByte() == 0x50 && fsCheck.ReadByte() == 0x4B && 
                      fsCheck.ReadByte() == 0x03 && fsCheck.ReadByte() == 0x04))
                {
                    MessageBox.Show("Selected file is not a valid ZIP.");
                    return;
                }

                await InstallModPackAsync(zipPath);
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error in InstallMods", "UI", ex);
                ShowUserFriendlyError("Failed to install mods", ex);
            }
        }

        // ✅ FIX: Proper async implementation to prevent UI thread blocking
        private async Task InstallModPackAsync(string zipPath)
        {
            _operationCancellationSource?.Cancel();
            _operationCancellationSource = new CancellationTokenSource();
            var cancellationToken = _operationCancellationSource.Token;

            try
            {
                EnableUI(false);
                
                var duration = await _logger!.MeasureOperationAsync("ModInstallation", async () =>
                {
                    // Check if Palworld is running
                    if (_currentDetector?.IsGameRunning() == true)
                    {
                        var result = MessageBox.Show("Palworld is running – close it?", "Process", 
                                                    MessageBoxButtons.YesNo);
                        if (result == DialogResult.Yes)
                        {
                            await _currentDetector.TerminateGameProcessesAsync();
                            _logger.LogInfo("Process Palworld.exe terminated", "Process");
                        }
                        else
                        {
                            MessageBox.Show("Cannot install mods while Palworld is running.");
                            return;
                        }
                    }

                    string tempDir = zipPath + ".tmpdir";
                    
                    try
                    {
                        // Clean up temp directory if it exists
                        if (Directory.Exists(tempDir))
                            Directory.Delete(tempDir, true);

                        // Extract ZIP with path validation
                        await Task.Run(() =>
                        {
                            string canonicalTempDir = Path.GetFullPath(tempDir);
                            using var archive = ZipFile.OpenRead(zipPath);
                            
                            foreach (var entry in archive.Entries)
                            {
                                cancellationToken.ThrowIfCancellationRequested();
                                
                                // Prevent path traversal
                                string destinationPath = Path.GetFullPath(Path.Combine(canonicalTempDir, entry.FullName));
                                if (!destinationPath.StartsWith(canonicalTempDir, StringComparison.OrdinalIgnoreCase))
                                {
                                    throw new SecurityException($"Path traversal detected: {entry.FullName}");
                                }

                                if (!string.IsNullOrEmpty(entry.Name))
                                {
                                    Directory.CreateDirectory(Path.GetDirectoryName(destinationPath)!);
                                    entry.ExtractToFile(destinationPath, true);
                                }
                            }
                        }, cancellationToken);

                        _logger.LogInfo("Extracted ZIP", "ModInstallation");

                        // Find 'Pal' folder in extracted content
                        string? srcPalFolder = Directory.GetDirectories(tempDir, "Pal", SearchOption.AllDirectories)
                                                       .FirstOrDefault();
                        
                        if (srcPalFolder == null)
                        {
                            MessageBox.Show("ZIP lacks a 'Pal' folder.");
                            return;
                        }

                        string dstPalFolder = Path.Combine(GetPalRoot(), "Pal");
                        var filesToCopy = Directory.GetFiles(srcPalFolder, "*", SearchOption.AllDirectories);
                        
                        await UpdateProgressBarAsync(0, filesToCopy.Length);

                        // Copy files from mod pack to game directory
                        for (int i = 0; i < filesToCopy.Length; i++)
                        {
                            cancellationToken.ThrowIfCancellationRequested();
                            
                            var sourceFile = filesToCopy[i];
                            string relativePath = Path.GetRelativePath(srcPalFolder, sourceFile);
                            string destinationFile = Path.Combine(dstPalFolder, relativePath);
                            
                            // Validate destination path
                            string canonicalDestination = Path.GetFullPath(destinationFile);
                            string canonicalDstFolder = Path.GetFullPath(dstPalFolder);
                            if (!canonicalDestination.StartsWith(canonicalDstFolder, StringComparison.OrdinalIgnoreCase))
                            {
                                throw new SecurityException($"Path traversal detected: {relativePath}");
                            }
                            
                            Directory.CreateDirectory(Path.GetDirectoryName(destinationFile)!);
                            File.Copy(sourceFile, destinationFile, overwrite: true);
                            
                            _logger.LogDebug($"Copy file {Path.Combine("Pal", relativePath)}", "ModInstallation");
                            await UpdateProgressBarAsync(i + 1, filesToCopy.Length);
                        }

                        await UpdateProgressBarAsync(0, 0);
                        
                        // Save manifest of installed files
                        SaveModManifest();
                        _logger.LogInfo($"Installation finished. Copied {filesToCopy.Length} files", "ModInstallation");

                        // Invalidate cache after installation
                        _currentDetector?.InvalidateCache();
                        _logger.LogDebug("Cache invalidated after installation", "Cache");
                    }
                    finally
                    {
                        // Clean up temp directory
                        if (Directory.Exists(tempDir))
                        {
                            try
                            {
                                Directory.Delete(tempDir, true);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning($"Failed to cleanup temp directory: {ex.Message}", "Cleanup");
                            }
                        }
                    }
                }, new { ZipFile = Path.GetFileName(zipPath) });

                if (!cancellationToken.IsCancellationRequested)
                {
                    MessageBox.Show("Mod-pack installed!");
                    await UpdateStatusIndicatorsAsync();
                }
            }
            catch (OperationCanceledException)
            {
                _logger?.LogInfo("Installation cancelled by user", "ModInstallation");
            }
            catch (Exception ex)
            {
                _logger?.LogError("Installation failed", "ModInstallation", ex);
                ShowUserFriendlyError("Failed to install mod pack", ex);
            }
            finally
            {
                EnableUI(true);
            }
        }

        // ── MOD REMOVAL LOGIC ──
        private async void RemoveMods(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateGamePath()) return;

                var result = MessageBox.Show(
                    "This will remove all installed mods. Continue?",
                    "Confirm Removal",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    await RemoveAllModsAsync();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error in RemoveMods", "UI", ex);
                ShowUserFriendlyError("Failed to remove mods", ex);
            }
        }

        // ✅ FIX: Proper async implementation
        private async Task RemoveAllModsAsync()
        {
            _operationCancellationSource?.Cancel();
            _operationCancellationSource = new CancellationTokenSource();
            var cancellationToken = _operationCancellationSource.Token;

            try
            {
                EnableUI(false);
                
                var duration = await _logger!.MeasureOperationAsync("ModRemoval", async () =>
                {
                    // Check if Palworld is running
                    if (_currentDetector?.IsGameRunning() == true)
                    {
                        var result = MessageBox.Show("Palworld is running – close it?", "Process", 
                                                    MessageBoxButtons.YesNo);
                        if (result == DialogResult.Yes)
                        {
                            await _currentDetector.TerminateGameProcessesAsync();
                            _logger.LogInfo("Process Palworld.exe terminated", "Process");
                        }
                        else
                        {
                            MessageBox.Show("Cannot remove mods while Palworld is running.");
                            return;
                        }
                    }

                    await Task.Run(() =>
                    {
                        string palRoot = GetPalRoot();
                        int filesDeleted = 0;
                        
                        _logger.LogInfo("Removing all mods...", "ModRemoval");

                        // Remove mod directories and files
                        foreach (var modPath in MOD_PATHS)
                        {
                            cancellationToken.ThrowIfCancellationRequested();
                            
                            string fullPath = Path.Combine(palRoot, modPath);
                            
                            if (Directory.Exists(fullPath))
                            {
                                // Count and log all files being deleted
                                var filesToDelete = Directory.GetFiles(fullPath, "*", SearchOption.AllDirectories);
                                foreach (var file in filesToDelete)
                                {
                                    _logger.LogDebug($"Delete file {Path.GetRelativePath(palRoot, file)}", "ModRemoval");
                                    filesDeleted++;
                                }
                                Directory.Delete(fullPath, true);
                            }
                            else if (File.Exists(fullPath))
                            {
                                _logger.LogDebug($"Delete file {modPath}", "ModRemoval");
                                File.Delete(fullPath);
                                filesDeleted++;
                            }
                        }

                        // Remove loose pak files (but keep vanilla)
                        string paksPath = Path.Combine(palRoot, @"Pal\Content\Paks");
                        if (Directory.Exists(paksPath))
                        {
                            foreach (var pakFile in Directory.GetFiles(paksPath, "*.pak"))
                            {
                                cancellationToken.ThrowIfCancellationRequested();
                                
                                if (!Path.GetFileName(pakFile).Equals(VANILLA_PAK, StringComparison.OrdinalIgnoreCase))
                                {
                                    _logger.LogDebug($"Delete file {Path.GetRelativePath(palRoot, pakFile)}", "ModRemoval");
                                    File.Delete(pakFile);
                                    filesDeleted++;
                                }
                            }
                        }

                        // Remove manifest
                        string manifestPath = Path.Combine(palRoot, MANIFEST);
                        if (File.Exists(manifestPath))
                        {
                            File.Delete(manifestPath);
                            _logger.LogDebug("Manifest deleted", "ModRemoval");
                        }

                        _logger.LogInfo($"All mods removed! Deleted {filesDeleted} files", "ModRemoval");

                        // Invalidate cache after removal
                        _currentDetector?.InvalidateCache();
                        _logger.LogDebug("Cache invalidated after mod removal", "Cache");
                    }, cancellationToken);
                });

                if (!cancellationToken.IsCancellationRequested)
                {
                    MessageBox.Show("All mods have been removed!");
                    await UpdateStatusIndicatorsAsync();
                }
            }
            catch (OperationCanceledException)
            {
                _logger?.LogInfo("Removal cancelled by user", "ModRemoval");
            }
            catch (Exception ex)
            {
                _logger?.LogError("Removal failed", "ModRemoval", ex);
                ShowUserFriendlyError("Failed to remove mods", ex);
            }
            finally
            {
                EnableUI(true);
            }
        }

        private async void RefreshStatus(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateGamePath()) return;
                
                EnableUI(false);
                
                await Task.Run(() =>
                {
                    _logger?.MeasureOperation("StatusRefresh", () =>
                    {
                        _currentDetector?.InvalidateCache();
                    });
                });
                
                await UpdateStatusIndicatorsAsync();
                _logger?.LogInfo("Status refreshed!", "UI");
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error refreshing status", "UI", ex);
                ShowUserFriendlyError("Failed to refresh status", ex);
            }
            finally
            {
                EnableUI(true);
            }
        }

        private async void CreateBackup(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateGamePath()) return;

                var palRoot = Path.GetDirectoryName(txtPath.Text)!;
                var ue4ssStatus = _currentDetector!.DetectUE4SS();
                var palSchemaStatus = _currentDetector.DetectPalSchema();

                // Show backup options dialog
                using var dialog = new BackupOptionsDialog(_appData.Settings.BackupSettings, ue4ssStatus, palSchemaStatus, 0);
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    _appData.Settings.BackupSettings = dialog.Settings;
                    _appData.SaveSettings();

                    _logger?.LogInfo("Creating smart backup...", "Backup");
                    
                    var engine = new SmartBackupEngine(palRoot, _currentDetector, dialog.Settings);
                    var filesToBackup = engine.GetFilesToBackup();
                    
                    _logger?.LogInfo($"Found {filesToBackup.Count} files to backup", "Backup");
                    
                    await CreateSmartBackupAsync(engine, filesToBackup);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error creating backup", "UI", ex);
                ShowUserFriendlyError("Failed to create backup", ex);
            }
        }

        // ✅ FIX: Proper async implementation
        private async Task CreateSmartBackupAsync(SmartBackupEngine backupEngine, List<string> filesToBackup)
        {
            _operationCancellationSource?.Cancel();
            _operationCancellationSource = new CancellationTokenSource();
            var cancellationToken = _operationCancellationSource.Token;

            try
            {
                EnableUI(false);
                
                var duration = await _logger!.MeasureOperationAsync("SmartBackup", async () =>
                {
                    // Check if Palworld is running
                    if (_currentDetector?.IsGameRunning() == true)
                    {
                        var result = MessageBox.Show("Palworld is running – close it?", "Process", 
                                                    MessageBoxButtons.YesNo);
                        if (result == DialogResult.Yes)
                        {
                            await _currentDetector.TerminateGameProcessesAsync();
                            _logger.LogInfo("Process Palworld.exe terminated", "Process");
                        }
                        else
                        {
                            return; // User declined
                        }
                    }

                    await Task.Run(() =>
                    {
                        string backupDir = Path.Combine(BACKUP_ROOT, DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss"));
                        Directory.CreateDirectory(backupDir);

                        // Create and save metadata
                        var metadata = backupEngine.CreateBackupMetadata("Manual backup via UI");
                        var metadataPath = Path.Combine(backupDir, "backup_metadata.json");
                        var metadataJson = JsonSerializer.Serialize(metadata, new JsonSerializerOptions { WriteIndented = true });
                        File.WriteAllText(metadataPath, metadataJson);
                        _logger.LogDebug("Backup metadata saved", "Backup");

                        string palRoot = GetPalRoot();

                        for (int i = 0; i < filesToBackup.Count; i++)
                        {
                            cancellationToken.ThrowIfCancellationRequested();
                            
                            var sourceFile = filesToBackup[i];
                            var relativePath = Path.GetRelativePath(palRoot, sourceFile);
                            var destinationFile = Path.Combine(backupDir, relativePath);
                            
                            Directory.CreateDirectory(Path.GetDirectoryName(destinationFile)!);
                            File.Copy(sourceFile, destinationFile, true);
                            _logger.LogDebug($"Copy file {relativePath}", "Backup");
                            
                            // Update progress on UI thread
                            Invoke(() => UpdateProgressBar(i + 1, filesToBackup.Count));
                        }
                        
                        Invoke(() => UpdateProgressBar(0, 0));

                        _logger.LogInfo($"Smart backup complete. Backed up {filesToBackup.Count} files to {backupDir}", "Backup");
                        
                        // Open backup location
                        var psi = new ProcessStartInfo("explorer") { Arguments = $"/select,\"{backupDir}\"", UseShellExecute = true };
                        Process.Start(psi);
                    }, cancellationToken);
                }, new { FileCount = filesToBackup.Count });
            }
            catch (OperationCanceledException)
            {
                _logger?.LogInfo("Backup cancelled by user", "Backup");
            }
            catch (Exception ex)
            {
                _logger?.LogError("Backup failed", "Backup", ex);
                throw;
            }
            finally
            {
                EnableUI(true);
            }
        }

        // ✅ Complete restore functionality with conflict resolution
        private async void RestoreBackup(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateGamePath()) return;

                using var ofd = new OpenFileDialog
                {
                    Title = "Select Backup to Restore",
                    Filter = "Backup Files|*.zip|Backup Directories|*.*",
                    InitialDirectory = BACKUP_ROOT
                };

                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    var backupPath = ofd.FileName;
                    
                    // If user selected a file but it's in a backup directory, use the directory instead
                    if (File.Exists(backupPath) && Directory.Exists(Path.GetDirectoryName(backupPath)))
                    {
                        var parentDir = Path.GetDirectoryName(backupPath)!;
                        if (File.Exists(Path.Combine(parentDir, "backup_metadata.json")))
                        {
                            backupPath = parentDir;
                        }
                    }

                    await RestoreBackupAsync(backupPath);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error restoring backup", "UI", ex);
                ShowUserFriendlyError("Failed to restore backup", ex);
            }
        }

        private async Task RestoreBackupAsync(string backupPath)
        {
            try
            {
                if (_restoreEngine == null)
                {
                    var palRoot = Path.GetDirectoryName(txtPath.Text)!;
                    _restoreEngine = new RestoreEngine(palRoot, _currentDetector!, _appData.Cache, msg => _logger!.LogInfo(msg, "Restore"));
                }

                _logger?.LogInfo($"Starting restore from: {Path.GetFileName(backupPath)}", "Restore");

                // Load backup metadata to check for conflicts
                var metadataPath = Directory.Exists(backupPath) 
                    ? Path.Combine(backupPath, "backup_metadata.json")
                    : null;

                if (metadataPath != null && File.Exists(metadataPath))
                {
                    var json = await File.ReadAllTextAsync(metadataPath);
                    var metadata = JsonSerializer.Deserialize<BackupMetadata>(json);
                    
                    if (metadata != null)
                    {
                        // Check for conflicts
                        var currentUE4SS = _currentDetector!.DetectUE4SS();
                        var currentPalSchema = _currentDetector.DetectPalSchema();
                        
                        var hasConflicts = metadata.UE4SSStatus.Status != currentUE4SS.Status ||
                                          metadata.UE4SSStatus.CoreModsPresent != currentUE4SS.CoreModsPresent ||
                                          metadata.UE4SSStatus.UserMods.Count != currentUE4SS.UserMods.Count ||
                                          metadata.PalSchemaStatus.IsInstalled != currentPalSchema.IsInstalled ||
                                          metadata.PalSchemaStatus.Mods.Count != currentPalSchema.Mods.Count;

                        var resolution = BackupConflictResolution.BackupFirst;

                        if (hasConflicts)
                        {
                            using var conflictDialog = new ConflictResolutionDialog(metadata, currentUE4SS, currentPalSchema);
                            if (conflictDialog.ShowDialog() == DialogResult.OK)
                            {
                                resolution = conflictDialog.Resolution;
                            }
                            else
                            {
                                _logger?.LogInfo("Restore cancelled by user", "Restore");
                                return;
                            }
                        }

                        // Perform the restore with the chosen resolution
                        var operation = await _restoreEngine.RestoreBackupAsync(backupPath, resolution);
                        
                        if (operation.Status == RestoreOperationStatus.Completed)
                        {
                            MessageBox.Show($"Restore completed successfully!\n\nRestored {operation.FilesProcessed} files in {operation.Duration.TotalSeconds:F1} seconds.", 
                                          "Restore Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            await UpdateStatusIndicatorsAsync();
                        }
                        else
                        {
                            MessageBox.Show($"Restore failed: {operation.ErrorMessage}", 
                                          "Restore Failed", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Invalid backup: metadata not found.", "Invalid Backup", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Restore operation failed", "Restore", ex);
                ShowUserFriendlyError("Restore failed", ex);
            }
        }

        private void ManageBackups(object? sender, EventArgs e)
        {
            try
            {
                if (_restoreEngine == null)
                {
                    var palRoot = Path.GetDirectoryName(txtPath.Text)!;
                    _restoreEngine = new RestoreEngine(palRoot, _currentDetector!, _appData.Cache, msg => _logger!.LogInfo(msg, "Restore"));
                }

                var backups = _restoreEngine.GetAvailableBackups(BACKUP_ROOT);
                
                if (backups.Count == 0)
                {
                    MessageBox.Show("No backups found.", "Backup Management", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Simple backup list for now - could be enhanced with a proper UI
                var backupList = string.Join("\n", backups.Select(b => $"{b.DisplayName} - {b.FormattedSize}"));
                MessageBox.Show($"Available Backups:\n\n{backupList}\n\nUse 'Restore Backup' to restore a specific backup.", 
                              "Backup Management", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                _logger?.LogInfo($"Displayed {backups.Count} available backups", "Backup");
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error managing backups", "UI", ex);
                ShowUserFriendlyError("Failed to manage backups", ex);
            }
        }

        // ✅ NEW: Maintenance system integration
        private async void RunFullMaintenance(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateGamePath()) return;

                if (_maintenanceEngine == null)
                {
                    var palRoot = Path.GetDirectoryName(txtPath.Text)!;
                    _maintenanceEngine = new MaintenanceEngine(palRoot, _currentDetector!, msg => _logger!.LogInfo(msg, "Maintenance"));
                }

                EnableUI(false);
                _logger?.LogInfo("Starting full maintenance scan...", "Maintenance");

                var report = await Task.Run(() => _maintenanceEngine.PerformFullMaintenance());
                ShowMaintenanceReport(report);
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error running maintenance", "UI", ex);
                ShowUserFriendlyError("Failed to run maintenance", ex);
            }
            finally
            {
                EnableUI(true);
            }
        }

        private async void RunIntegrityCheck(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateGamePath()) return;

                if (_maintenanceEngine == null)
                {
                    var palRoot = Path.GetDirectoryName(txtPath.Text)!;
                    _maintenanceEngine = new MaintenanceEngine(palRoot, _currentDetector!, msg => _logger!.LogInfo(msg, "Maintenance"));
                }

                EnableUI(false);

                var ue4ssIntegrity = await Task.Run(() => _maintenanceEngine.CheckUE4SSIntegrity());
                var palSchemaIntegrity = await Task.Run(() => _maintenanceEngine.CheckPalSchemaIntegrity());

                var message = new StringBuilder();
                message.AppendLine("Integrity Check Results:\n");
                
                message.AppendLine($"UE4SS: {ue4ssIntegrity.OverallStatus}");
                if (ue4ssIntegrity.Issues.Count > 0)
                {
                    message.AppendLine("Issues:");
                    foreach (var issue in ue4ssIntegrity.Issues)
                        message.AppendLine($"  • {issue}");
                }
                
                message.AppendLine($"\nPalSchema: {palSchemaIntegrity.OverallStatus}");
                if (palSchemaIntegrity.Issues.Count > 0)
                {
                    message.AppendLine("Issues:");
                    foreach (var issue in palSchemaIntegrity.Issues)
                        message.AppendLine($"  • {issue}");
                }

                MessageBox.Show(message.ToString(), "Integrity Check", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error running integrity check", "UI", ex);
                ShowUserFriendlyError("Failed to run integrity check", ex);
            }
            finally
            {
                EnableUI(true);
            }
        }

        private async void RunCleanup(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateGamePath()) return;

                if (_maintenanceEngine == null)
                {
                    var palRoot = Path.GetDirectoryName(txtPath.Text)!;
                    _maintenanceEngine = new MaintenanceEngine(palRoot, _currentDetector!, msg => _logger!.LogInfo(msg, "Maintenance"));
                }

                EnableUI(false);

                var orphans = await Task.Run(() => _maintenanceEngine.FindOrphanedFiles());
                
                if (orphans.Count == 0)
                {
                    MessageBox.Show("No orphaned files found.", "Cleanup", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var totalSize = orphans.Sum(o => o.Size);
                var result = MessageBox.Show(
                    $"Found {orphans.Count} orphaned files ({FormatFileSize(totalSize)}).\n\nRemove these files?", 
                    "Cleanup Orphaned Files", 
                    MessageBoxButtons.YesNo, 
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await Task.Run(() => _maintenanceEngine.CleanupOrphanedFiles(orphans, false));
                    MessageBox.Show($"Cleaned up {orphans.Count} orphaned files.", "Cleanup Complete", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    await UpdateStatusIndicatorsAsync();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error running cleanup", "UI", ex);
                ShowUserFriendlyError("Failed to run cleanup", ex);
            }
            finally
            {
                EnableUI(true);
            }
        }

        private async void ExportDiagnostics(object? sender, EventArgs e)
        {
            try
            {
                using var sfd = new SaveFileDialog
                {
                    Title = "Export Diagnostic Report",
                    Filter = "JSON files (*.json)|*.json|All files (*.*)|*.*",
                    DefaultExt = "json",
                    FileName = $"palworld_diagnostics_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.json"
                };

                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    EnableUI(false);
                    
                    await _logger!.ExportDiagnosticReport(sfd.FileName);
                    MessageBox.Show($"Diagnostic report exported to:\n{sfd.FileName}", 
                                  "Export Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    // Open the location
                    var psi = new ProcessStartInfo("explorer") { Arguments = $"/select,\"{sfd.FileName}\"", UseShellExecute = true };
                    Process.Start(psi);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to export diagnostics", "Diagnostics", ex);
                ShowUserFriendlyError("Failed to export diagnostics", ex);
            }
            finally
            {
                EnableUI(true);
            }
        }

        // ── PalSchema Management Event Handlers ──
        private async void OpenPalSchemaConfiguration(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateGamePath()) return;

                // Initialize PalSchema services if not already done
                if (_palSchemaConfigService == null)
                {
                    InitializePalSchemaServices(GetPalRoot());
                }

                if (_palSchemaConfigService == null)
                {
                    MessageBox.Show("Failed to initialize PalSchema services. Please check your Palworld installation.",
                        "Service Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Load configurations
                await _palSchemaConfigService.LoadConfigurationsAsync();

                // Open configuration dialog
                using var dialog = new PalSchemaConfigurationDialog(_palSchemaConfigService, _logger!);
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    _logger?.LogInfo("PalSchema configuration updated", "PalSchema");
                    await UpdateStatusIndicatorsAsync();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to open PalSchema configuration", "PalSchema", ex);
                ShowUserFriendlyError("Failed to open PalSchema configuration", ex);
            }
        }

        private async void OpenPalSchemaProfiles(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateGamePath()) return;

                // Initialize PalSchema services if not already done
                if (_palSchemaProfileManager == null)
                {
                    InitializePalSchemaServices(GetPalRoot());
                }

                if (_palSchemaProfileManager == null)
                {
                    MessageBox.Show("Failed to initialize PalSchema services. Please check your Palworld installation.",
                        "Service Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Load profiles
                await _palSchemaProfileManager.LoadProfilesAsync();

                // For now, show a simple message - in a full implementation, this would open a profile management dialog
                var profiles = _palSchemaProfileManager.Profiles;
                var activeProfile = _palSchemaProfileManager.ActiveProfile;

                var message = $"PalSchema Profile Manager\n\n";
                message += $"Total Profiles: {profiles.Count}\n";
                message += $"Active Profile: {activeProfile?.Name ?? "None"}\n\n";
                message += "Profile management UI will be available in a future update.";

                MessageBox.Show(message, "PalSchema Profiles", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to open PalSchema profiles", "PalSchema", ex);
                ShowUserFriendlyError("Failed to open PalSchema profiles", ex);
            }
        }

        private async void OpenPalSchemaTools(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateGamePath()) return;

                // Initialize PalSchema services if not already done
                if (_palSchemaAdvancedTools == null)
                {
                    InitializePalSchemaServices(GetPalRoot());
                }

                if (_palSchemaAdvancedTools == null || _palSchemaConfigService == null)
                {
                    MessageBox.Show("Failed to initialize PalSchema services. Please check your Palworld installation.",
                        "Service Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Get available configurations for analysis
                var configurations = _palSchemaConfigService.GetConfigurations();
                if (!configurations.Any())
                {
                    MessageBox.Show("No PalSchema configurations found. Please create a configuration first.",
                        "No Configurations", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // For now, show analysis of the first configuration
                var config = configurations.First();
                EnableUI(false);

                try
                {
                    var lintResults = await _palSchemaAdvancedTools.LintConfigurationAsync(config);
                    var performanceMetrics = await _palSchemaAdvancedTools.AnalyzePerformanceAsync(config);
                    var optimizations = await _palSchemaAdvancedTools.GenerateOptimizationSuggestionsAsync(config);

                    var message = $"PalSchema Analysis Results for: {config.Name}\n\n";
                    message += $"Linting Issues: {lintResults.Count}\n";
                    message += $"Performance Complexity: {performanceMetrics.ConfigurationComplexity}/10\n";
                    message += $"Memory Usage: {performanceMetrics.EstimatedMemoryUsage:F1} KB\n";
                    message += $"CPU Impact: {performanceMetrics.EstimatedCpuImpact:F1}%\n";
                    message += $"Optimization Suggestions: {optimizations.Count}\n\n";

                    if (lintResults.Any())
                    {
                        message += "Top Issues:\n";
                        foreach (var issue in lintResults.Take(3))
                        {
                            message += $"  • {issue.Severity}: {issue.Message}\n";
                        }
                    }

                    MessageBox.Show(message, "PalSchema Analysis", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                finally
                {
                    EnableUI(true);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to open PalSchema tools", "PalSchema", ex);
                ShowUserFriendlyError("Failed to open PalSchema tools", ex);
            }
            finally
            {
                EnableUI(true);
            }
        }

        // ── Advanced Features Event Handlers ──
        private async void OpenModManager(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateGamePath()) return;

                // Initialize advanced services if not already done
                if (_modManagerService == null)
                {
                    var palRoot = GetPalRoot();
                    InitializePalSchemaServices(palRoot);
                    await InitializeAdvancedServices(palRoot);
                }

                if (_modManagerService == null || _logger == null)
                {
                    MessageBox.Show("Failed to initialize mod management services. Please check your Palworld installation.",
                        "Service Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Open mod manager form
                using var modManagerForm = new ModManagerForm(_modManagerService, _logger);
                modManagerForm.ShowDialog();

                // Refresh status after mod manager closes
                await UpdateStatusIndicatorsAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to open mod manager", "ModManager", ex);
                ShowUserFriendlyError("Failed to open mod manager", ex);
            }
        }

        private async void OpenCollectionSharing(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateGamePath()) return;

                // Initialize advanced services if not already done
                if (_modCollectionService == null)
                {
                    var palRoot = GetPalRoot();
                    InitializePalSchemaServices(palRoot);
                    await InitializeAdvancedServices(palRoot);
                }

                if (_modCollectionService == null || _logger == null)
                {
                    MessageBox.Show("Failed to initialize mod collection services. Please check your Palworld installation.",
                        "Service Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Initialize community rating service
                var ratingService = new CommunityRatingService(_appData.AppDataPath, _logger);

                // Open collection sharing dialog
                using var collectionDialog = new ModCollectionSharingDialog(_modCollectionService, ratingService, _logger);
                collectionDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to open collection sharing", "CollectionSharing", ex);
                ShowUserFriendlyError("Failed to open collection sharing", ex);
            }
        }

        private async void OpenDiagnosticTools(object? sender, EventArgs e)
        {
            try
            {
                if (!ValidateGamePath()) return;

                // Initialize advanced services if not already done
                if (_diagnosticService == null)
                {
                    var palRoot = GetPalRoot();
                    InitializePalSchemaServices(palRoot);
                    await InitializeAdvancedServices(palRoot);
                }

                if (_diagnosticService == null || _logger == null)
                {
                    MessageBox.Show("Failed to initialize diagnostic services. Please check your Palworld installation.",
                        "Service Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Initialize common issue detection service
                var issueDetectionService = new CommonIssueDetectionService(GetPalRoot(), _currentDetector!, _modManagerService!, _logger);

                // Open diagnostic tools dialog
                using var diagnosticDialog = new DiagnosticToolsDialog(_diagnosticService, issueDetectionService, _logger);
                diagnosticDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to open diagnostic tools", "DiagnosticTools", ex);
                ShowUserFriendlyError("Failed to open diagnostic tools", ex);
            }
        }

        private void ShowMaintenanceReport(MaintenanceReport report)
        {
            var message = new StringBuilder();
            message.AppendLine($"Maintenance Report - {report.ScanDate:yyyy-MM-dd HH:mm:ss}\n");

            // Overall status
            message.AppendLine($"UE4SS Status: {report.UE4SSIntegrity.OverallStatus}");
            message.AppendLine($"PalSchema Status: {report.PalSchemaIntegrity.OverallStatus}");
            message.AppendLine($"Orphaned Files: {report.OrphanedFiles.Count}");
            message.AppendLine($"Corrupted Mods: {report.CorruptedMods.Count}");
            message.AppendLine($"Total Mods Size: {FormatFileSize(report.DiskUsage.TotalModsSize)}\n");

            // High priority recommendations
            var highPriorityRecs = report.Recommendations.Where(r => r.Priority >= RecommendationPriority.High).ToList();
            if (highPriorityRecs.Count > 0)
            {
                message.AppendLine("High Priority Recommendations:");
                foreach (var rec in highPriorityRecs)
                {
                    message.AppendLine($"  • {rec.Title}: {rec.Description}");
                }
                message.AppendLine();
            }

            // Critical issues
            if (report.HasCriticalIssues)
            {
                message.AppendLine("⚠️ CRITICAL ISSUES DETECTED ⚠️");
                foreach (var issue in report.Issues)
                {
                    message.AppendLine($"  • {issue}");
                }
                message.AppendLine();
            }

            var icon = report.HasCriticalIssues ? MessageBoxIcon.Warning : MessageBoxIcon.Information;
            MessageBox.Show(message.ToString(), "Maintenance Report", MessageBoxButtons.OK, icon);
        }

        private void ShowChangelog(object? sender, EventArgs e)
        {
            try
            {
                using var changelogForm = new Form
                {
                    Text = "Changelog",
                    Size = new Size(600, 500),
                    StartPosition = FormStartPosition.CenterParent
                };

                using var textBox = new RichTextBox
                {
                    Dock = DockStyle.Fill,
                    ReadOnly = true,
                    Font = new Font("Consolas", 9),
                    Text = CHANGELOG,
                    BackColor = Theme.Surface,
                    ForeColor = Theme.TextLight
                };

                Theme.Apply(changelogForm);
                changelogForm.Controls.Add(textBox);
                changelogForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error showing changelog", "UI", ex);
            }
        }

        // ── Helper methods ──
        private bool ValidateGamePath()
        {
            if (string.IsNullOrEmpty(txtPath.Text) || !File.Exists(txtPath.Text))
            {
                MessageBox.Show("Please select a valid Palworld.exe path first.", "Invalid Path", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }
            return true;
        }

        private string GetPalRoot()
        {
            return Path.GetFullPath(Path.GetDirectoryName(txtPath.Text)!);
        }

        private void SaveModManifest()
        {
            try
            {
                var manifestDict = new Dictionary<string, string>();
                string palRoot = GetPalRoot();
                string palFolder = Path.Combine(palRoot, "Pal");

                foreach (var file in Directory.GetFiles(palFolder, "*", SearchOption.AllDirectories))
                {
                    if (Path.GetFileName(file).Equals(VANILLA_PAK, StringComparison.OrdinalIgnoreCase)) 
                        continue;
                    
                    string relativePath = Path.GetRelativePath(palRoot, file);
                    manifestDict[relativePath] = ComputeSha256Hash(file);
                }

                string manifestPath = Path.Combine(palRoot, MANIFEST);
                string manifestJson = JsonSerializer.Serialize(manifestDict, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(manifestPath, manifestJson);
                _logger?.LogDebug("Manifest saved", "ModInstallation");
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to save manifest", "ModInstallation", ex);
            }
        }

        private static string ComputeSha256Hash(string filePath)
        {
            using var sha256 = SHA256.Create();
            using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            
            var buffer = new byte[1 << 20]; // 1MB buffer
            int bytesRead;
            
            while ((bytesRead = fileStream.Read(buffer, 0, buffer.Length)) > 0)
            {
                sha256.TransformBlock(buffer, 0, bytesRead, null, 0);
            }
            
            sha256.TransformFinalBlock(Array.Empty<byte>(), 0, 0);
            return BitConverter.ToString(sha256.Hash!).Replace("-", "").ToLowerInvariant();
        }

        private void InitializeCachedData()
        {
            try
            {
                // Load recent installations and populate combo box
                var recentInstallations = _appData.GetValidInstallations();
                cmbRecentPaths.Items.Clear();
                
                if (recentInstallations.Count > 0)
                {
                    foreach (var installation in recentInstallations)
                    {
                        cmbRecentPaths.Items.Add(installation);
                    }
                    cmbRecentPaths.DisplayMember = "DisplayName";
                    cmbRecentPaths.ValueMember = "Path";
                    
                    // Set the most recent as selected
                    cmbRecentPaths.SelectedIndex = 0;
                    if (cmbRecentPaths.SelectedItem is PalworldInstallation selectedInstallation)
                    {
                        txtPath.Text = selectedInstallation.Path;
                        CreateDetectorForPath(Path.GetDirectoryName(selectedInstallation.Path)!);
                    }
                }
                else
                {
                    // Try auto-detection
                    var autoDetected = _appData.AutoDetectPalworldInstallations();
                    if (autoDetected.Count > 0)
                    {
                        txtPath.Text = autoDetected[0];
                        _appData.AddRecentInstallation(autoDetected[0]);
                        CreateDetectorForPath(Path.GetDirectoryName(autoDetected[0])!);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error initializing cached data", "UI", ex);
            }
        }

        private async Task ShowFirstRunExperienceAsync()
        {
            try
            {
                _logger?.LogInfo("Welcome to t7's Palworld Mod-Installer v" + VERSION, "FirstRun");
                _logger?.LogInfo("This tool helps you manage UE4SS mods and PalSchema for Palworld.", "FirstRun");
                
                if (string.IsNullOrEmpty(txtPath.Text))
                {
                    _logger?.LogInfo("Please select your Palworld.exe location to get started.", "FirstRun");
                    // Auto-try detection
                    await Task.Run(() =>
                    {
                        var autoDetected = _appData.AutoDetectPalworldInstallations();
                        if (autoDetected.Count > 0)
                        {
                            Invoke(() =>
                            {
                                _logger?.LogInfo($"Auto-detected Palworld installation: {autoDetected[0]}", "AutoDetection");
                                txtPath.Text = autoDetected[0];
                                _appData.AddRecentInstallation(autoDetected[0]);
                            });
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error in first run experience", "UI", ex);
            }
        }

        private async Task UpdateStatusIndicatorsAsync()
        {
            try
            {
                if (_currentDetector == null)
                {
                    ue4ssIndicator.StatusText = "UE4SS: Not detected";
                    ue4ssIndicator.StatusColor = Theme.Error;
                    palSchemaIndicator.StatusText = "PalSchema: Not detected";
                    palSchemaIndicator.StatusColor = Theme.Error;
                    return;
                }

                var ue4ssStatus = await _currentDetector.DetectUE4SSAsync();
                var palSchemaStatus = await _currentDetector.DetectPalSchemaAsync();

                // UE4SS Status
                switch (ue4ssStatus.Status)
                {
                    case UE4SSInstallStatus.FullyInstalled:
                        ue4ssIndicator.StatusText = $"UE4SS: Fully Installed ({ue4ssStatus.CoreModsPresent}/{ue4ssStatus.CoreModsExpected} core mods)";
                        ue4ssIndicator.StatusColor = Theme.Success;
                        break;
                    case UE4SSInstallStatus.PartiallyInstalled:
                        ue4ssIndicator.StatusText = $"UE4SS: Partially Installed ({ue4ssStatus.CoreModsPresent}/{ue4ssStatus.CoreModsExpected} core mods)";
                        ue4ssIndicator.StatusColor = Theme.Warning;
                        break;
                    case UE4SSInstallStatus.NotInstalled:
                        ue4ssIndicator.StatusText = "UE4SS: Not Installed";
                        ue4ssIndicator.StatusColor = Theme.Error;
                        break;
                    default:
                        ue4ssIndicator.StatusText = "UE4SS: Unknown Status";
                        ue4ssIndicator.StatusColor = Theme.Warning;
                        break;
                }

                // PalSchema Status
                if (palSchemaStatus.IsInstalled)
                {
                    palSchemaIndicator.StatusText = $"PalSchema: Installed ({palSchemaStatus.Mods.Count} mods)";
                    palSchemaIndicator.StatusColor = Theme.Success;
                }
                else
                {
                    palSchemaIndicator.StatusText = "PalSchema: Not Installed";
                    palSchemaIndicator.StatusColor = Theme.Error;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error updating status indicators", "UI", ex);
            }
        }

        private void CreateDetectorForPath(string palRoot)
        {
            try
            {
                if (Directory.Exists(palRoot))
                {
                    // Dispose old detector
                    _currentDetector?.Dispose();

                    _currentDetector = new UE4SSDetector(palRoot, _appData.Cache);

                    // Initialize other engines
                    _maintenanceEngine = new MaintenanceEngine(palRoot, _currentDetector, msg => _logger!.LogInfo(msg, "Maintenance"));
                    _restoreEngine = new RestoreEngine(palRoot, _currentDetector, _appData.Cache, msg => _logger!.LogInfo(msg, "Restore"));

                    // Initialize PalSchema services
                    InitializePalSchemaServices(palRoot);

                    // Initialize advanced services asynchronously
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await InitializeAdvancedServices(palRoot);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError("Error initializing advanced services", "AdvancedServices", ex);
                        }
                    });

                    _ = UpdateStatusIndicatorsAsync();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error creating detector for path", "UI", ex);
            }
        }

        private void SaveApplicationState()
        {
            try
            {
                // Save window state
                if (WindowState == FormWindowState.Normal)
                {
                    _appData.SaveWindowState(Location, Size, false);
                }
                else if (WindowState == FormWindowState.Maximized)
                {
                    _appData.SaveWindowState(RestoreBounds.Location, RestoreBounds.Size, true);
                }
                
                // Save current path
                if (!string.IsNullOrEmpty(txtPath.Text))
                {
                    _appData.SetLastPalworldPath(txtPath.Text);
                }

                _logger?.LogInfo("Application state saved", "System");
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error saving application state", "UI", ex);
            }
        }

        // ✅ NEW: UI helper methods
        private void EnableUI(bool enabled)
        {
            if (InvokeRequired)
            {
                Invoke(() => EnableUI(enabled));
                return;
            }

            foreach (Control control in root.Controls)
            {
                if (control is FlowLayoutPanel panel)
                {
                    foreach (Control subControl in panel.Controls)
                    {
                        if (subControl is ThemedButton)
                            subControl.Enabled = enabled;
                    }
                }
            }
        }

        private void UpdateProgressBar(int current, int total)
        {
            if (InvokeRequired)
            {
                Invoke(() => UpdateProgressBar(current, total));
                return;
            }

            bar.Maximum = total;
            bar.Value = current;
        }

        private async Task UpdateProgressBarAsync(int current, int total)
        {
            await Task.Run(() => UpdateProgressBar(current, total));
        }

        private void ShowUserFriendlyError(string message, Exception ex)
        {
            var userMessage = $"{message}\n\nTechnical details: {ex.Message}";
            MessageBox.Show(userMessage, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void Log(string message) => _logger?.LogInfo(message, "UI");

        private void CenterForm()
        {
            root.Location = new Point(Math.Max(0, (ClientSize.Width - root.PreferredSize.Width) / 2),
                                     Math.Max(0, (ClientSize.Height - root.PreferredSize.Height) / 2));
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len /= 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        private static Icon CreateAppIcon()
        {
            // Create a simple icon programmatically
            using var bmp = new Bitmap(32, 32);
            using var g = Graphics.FromImage(bmp);
            
            g.Clear(Color.Transparent);
            g.SmoothingMode = SmoothingMode.AntiAlias;
            
            // Draw a simple mod icon
            using var brush = new SolidBrush(Theme.Accent);
            using var pen = new Pen(Theme.Accent2, 2);
            
            g.FillRectangle(brush, 8, 8, 16, 16);
            g.DrawRectangle(pen, 8, 8, 16, 16);
            
            return Icon.FromHandle(bmp.GetHicon());
        }

        // ✅ FIX: Proper disposal pattern to prevent memory leaks
        protected override void Dispose(bool disposing)
        {
            if (disposing && !_disposed)
            {
                // Cancel any running operations
                _operationCancellationSource?.Cancel();
                _operationCancellationSource?.Dispose();

                // Dispose services
                _currentDetector?.Dispose();
                _palSchemaConfigService?.Dispose();
                _palSchemaProfileManager?.Dispose();
                _palSchemaAdvancedTools?.Dispose();
                _modManagerService?.Dispose();
                _modCollectionService?.Dispose();
                _diagnosticService?.Dispose();
                _installationEngine?.Dispose();
                _logger?.Dispose();

                // Unsubscribe from events to prevent memory leaks
                if (_logger != null)
                    _logger.LogEntryAdded -= OnLogEntryAdded;

                _disposed = true;
            }
            base.Dispose(disposing);
        }
    }
}