using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Security;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    public class EnhancedInstallationEngine : IDisposable
    {
        private readonly string _palRoot;
        private readonly UE4SSDetector _detector;
        private readonly CacheManager _cache;
        private readonly EnhancedLogger _logger;
        private readonly List<IArchiveExtractor> _extractors;
        private readonly AdvancedModAnalyzer _modAnalyzer;
        private readonly AdvancedConflictDetector _conflictDetector;
        private bool _disposed = false;

        public EnhancedInstallationEngine(string palRoot, UE4SSDetector detector, CacheManager cache, EnhancedLogger logger)
        {
            _palRoot = ValidatePath(palRoot) ?? throw new ArgumentException("Invalid Palworld root path", nameof(palRoot));
            _detector = detector ?? throw new ArgumentNullException(nameof(detector));
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // Initialize advanced analyzers
            _modAnalyzer = new AdvancedModAnalyzer(logger, detector);
            _conflictDetector = new AdvancedConflictDetector(palRoot, detector, logger);

            // Initialize archive extractors
            _extractors = new List<IArchiveExtractor>
            {
                new ZipExtractor(),
                new SevenZipExtractor(),
                new RarExtractor()
            };
        }

        public async Task<InstallationResult> InstallModAsync(string archivePath, InstallationOptions options, CancellationToken cancellationToken = default)
        {
            return await InstallModAsync(archivePath, options, null, cancellationToken);
        }

        public async Task<InstallationResult> InstallModAsync(string archivePath, InstallationOptions options, IProgress<InstallationProgress>? progress, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            if (string.IsNullOrWhiteSpace(archivePath))
                throw new ArgumentException("Archive path cannot be null or empty", nameof(archivePath));

            if (!File.Exists(archivePath))
                throw new FileNotFoundException($"Archive file not found: {archivePath}");

            var operation = new InstallationOperation
            {
                ArchivePath = archivePath,
                TargetPath = _palRoot,
                Options = options,
                Status = InstallationStatus.Analyzing
            };

            var progressReporter = new InstallationProgress
            {
                OperationId = operation.Id,
                Status = operation.Status,
                CurrentOperation = "Initializing installation...",
                StartTime = DateTime.Now
            };

            _logger.StartPerformanceTracking($"ModInstallation_{Path.GetFileName(archivePath)}");

            try
            {
                _logger.LogOperation("ModInstallation", $"Starting installation of {Path.GetFileName(archivePath)}");

                // Step 1: Security validation
                operation.Status = InstallationStatus.ValidatingCompatibility;
                progressReporter.Status = operation.Status;
                progressReporter.CurrentOperation = "Validating archive security...";
                progress?.Report(progressReporter);

                await ValidateArchiveSecurityAsync(archivePath, cancellationToken);

                // Step 2: Detect archive format and extract
                operation.Status = InstallationStatus.Extracting;
                progressReporter.Status = operation.Status;
                progressReporter.CurrentOperation = "Extracting archive...";
                progress?.Report(progressReporter);

                var tempDir = await ExtractArchiveAsync(archivePath, cancellationToken);
                operation.ExtractedPath = tempDir;

                // Step 3: Advanced mod structure analysis
                operation.Status = InstallationStatus.Analyzing;
                progressReporter.Status = operation.Status;
                progressReporter.CurrentOperation = "Analyzing mod structure...";
                progress?.Report(progressReporter);

                var advancedStructure = await _modAnalyzer.AnalyzeModStructureAsync(tempDir, cancellationToken);

                // Convert to basic structure for compatibility
                var modStructure = ConvertToBasicStructure(advancedStructure);
                operation.DetectedStructure = modStructure;

                // Count total files for progress tracking
                operation.TotalFiles = modStructure.Files.Count +
                                     modStructure.UE4SSMods.Sum(m => Directory.Exists(m.Path) ? Directory.GetFiles(m.Path, "*", SearchOption.AllDirectories).Length : 0) +
                                     modStructure.PalSchemaMods.Sum(m => Directory.Exists(m.Path) ? Directory.GetFiles(m.Path, "*", SearchOption.AllDirectories).Length : 0) +
                                     modStructure.PakFiles.Count;

                progressReporter.TotalFiles = operation.TotalFiles;
                progressReporter.CurrentOperation = $"Detected {modStructure.ModType} mod with {operation.TotalFiles} files";
                progress?.Report(progressReporter);

                _logger.LogInfo($"Detected mod type: {modStructure.ModType}", "ModAnalysis",
                    new { ModStructure = modStructure });

                // Step 4: Validate compatibility
                operation.Status = InstallationStatus.ValidatingCompatibility;
                progressReporter.Status = operation.Status;
                progressReporter.CurrentOperation = "Validating mod compatibility...";
                progress?.Report(progressReporter);

                var compatibility = await ValidateCompatibilityAsync(modStructure, cancellationToken);
                operation.CompatibilityResult = compatibility;

                if (!compatibility.IsCompatible && !options.ForceInstall)
                {
                    throw new InvalidOperationException($"Mod is not compatible: {compatibility.Reason}");
                }

                // Step 5: Advanced conflict detection
                operation.Status = InstallationStatus.CheckingConflicts;
                progressReporter.Status = operation.Status;
                progressReporter.CurrentOperation = "Checking for conflicts...";
                progress?.Report(progressReporter);

                var conflictResult = await _conflictDetector.DetectConflictsAsync(advancedStructure, cancellationToken);

                // Convert to basic conflicts for compatibility
                var conflicts = ConvertToBasicConflicts(conflictResult);
                operation.Conflicts = conflicts;

                // Log advanced analysis results
                _logger.LogInfo($"Advanced analysis: Complexity {advancedStructure.ComplexityLevel}, " +
                              $"Frameworks: {string.Join(", ", advancedStructure.DetectedFrameworks)}, " +
                              $"Conflicts: {conflictResult.Conflicts.Count}", "AdvancedAnalysis");

                if (conflicts.Count > 0 && options.ConflictResolution == ConflictResolution.Ask)
                {
                    // Return to caller for conflict resolution
                    operation.Status = InstallationStatus.AwaitingConflictResolution;
                    progressReporter.Status = operation.Status;
                    progressReporter.CurrentOperation = $"Awaiting conflict resolution ({conflicts.Count} conflicts found)";
                    progress?.Report(progressReporter);

                    return new InstallationResult { Operation = operation, RequiresConflictResolution = true };
                }

                // Step 6: Create backup if requested
                if (options.CreateBackupBeforeInstall)
                {
                    operation.Status = InstallationStatus.CreatingBackup;
                    progressReporter.Status = operation.Status;
                    progressReporter.CurrentOperation = "Creating backup before installation...";
                    progress?.Report(progressReporter);

                    operation.BackupPath = await CreatePreInstallBackupAsync(modStructure, cancellationToken);
                }

                // Step 7: Install the mod
                operation.Status = InstallationStatus.Installing;
                progressReporter.Status = operation.Status;
                progressReporter.CurrentOperation = "Installing mod files...";
                progress?.Report(progressReporter);

                await PerformInstallationAsync(operation, modStructure, progressReporter, progress, cancellationToken);

                // Step 8: Post-installation tasks
                operation.Status = InstallationStatus.PostProcessing;
                progressReporter.Status = operation.Status;
                progressReporter.CurrentOperation = "Finalizing installation...";
                progress?.Report(progressReporter);

                await PostInstallationTasksAsync(operation, modStructure, cancellationToken);

                operation.Status = InstallationStatus.Completed;
                operation.EndTime = DateTime.Now;
                progressReporter.Status = operation.Status;
                progressReporter.CurrentOperation = "Installation completed successfully";
                progressReporter.ProcessedFiles = operation.TotalFiles;
                progress?.Report(progressReporter);

                _logger.LogOperation("ModInstallation",
                    $"Installation completed successfully in {operation.Duration.TotalSeconds:F1} seconds");

                return new InstallationResult
                {
                    Operation = operation,
                    Success = true,
                    InstalledFiles = operation.InstalledFiles.ToList()
                };
            }
            catch (OperationCanceledException)
            {
                operation.Status = InstallationStatus.Cancelled;
                operation.EndTime = DateTime.Now;
                _logger.LogInfo("Installation cancelled by user", "ModInstallation");
                
                return new InstallationResult 
                { 
                    Operation = operation, 
                    Success = false, 
                    ErrorMessage = "Installation was cancelled" 
                };
            }
            catch (Exception ex)
            {
                operation.Status = InstallationStatus.Failed;
                operation.ErrorMessage = ex.Message;
                operation.EndTime = DateTime.Now;

                _logger.LogError("Installation failed", "ModInstallation", ex, 
                    new { ArchivePath = archivePath, Duration = operation.Duration.TotalSeconds });

                return new InstallationResult 
                { 
                    Operation = operation, 
                    Success = false, 
                    ErrorMessage = ex.Message 
                };
            }
            finally
            {
                _logger.StopPerformanceTracking($"ModInstallation_{Path.GetFileName(archivePath)}");
                
                // Cleanup temp directory
                if (!string.IsNullOrEmpty(operation.ExtractedPath) && Directory.Exists(operation.ExtractedPath))
                {
                    try
                    {
                        await Task.Run(() => Directory.Delete(operation.ExtractedPath, true), cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to cleanup temp directory: {ex.Message}", "Cleanup");
                    }
                }

                // Invalidate cache
                _detector.InvalidateCache();
            }
        }

        public async Task<BatchInstallationResult> InstallMultipleModsAsync(List<string> archivePaths, InstallationOptions options, CancellationToken cancellationToken = default)
        {
            return await InstallMultipleModsAsync(archivePaths, options, null, cancellationToken);
        }

        public async Task<BatchInstallationResult> InstallMultipleModsAsync(List<string> archivePaths, InstallationOptions options, IProgress<BatchInstallationProgress>? progress, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            if (archivePaths == null || archivePaths.Count == 0)
                throw new ArgumentException("Archive paths list cannot be null or empty", nameof(archivePaths));

            var batchResult = new BatchInstallationResult
            {
                TotalMods = archivePaths.Count,
                StartTime = DateTime.Now
            };

            var batchProgress = new BatchInstallationProgress
            {
                TotalMods = archivePaths.Count,
                StartTime = DateTime.Now
            };

            _logger.StartPerformanceTracking("BatchInstallation");
            _logger.LogOperation("BatchInstallation", $"Starting batch installation of {archivePaths.Count} mods");

            try
            {
                for (int i = 0; i < archivePaths.Count; i++)
                {
                    var archivePath = archivePaths[i];
                    cancellationToken.ThrowIfCancellationRequested();

                    batchProgress.CurrentModIndex = i;
                    batchProgress.CurrentModName = Path.GetFileNameWithoutExtension(archivePath);
                    progress?.Report(batchProgress);

                    try
                    {
                        // Create progress reporter for individual mod installation
                        var modProgress = new Progress<InstallationProgress>(p =>
                        {
                            batchProgress.CurrentModProgress = p;
                            progress?.Report(batchProgress);
                        });

                        var result = await InstallModAsync(archivePath, options, modProgress, cancellationToken);
                        batchResult.Results.Add(result);
                        batchProgress.CompletedInstallations.Add(result);

                        if (result.Success)
                        {
                            batchResult.SuccessfulInstalls++;
                        }
                        else
                        {
                            batchResult.FailedInstalls++;
                        }

                        // Update progress
                        batchResult.ProcessedMods = batchResult.Results.Count;
                        batchProgress.CurrentModIndex = i + 1;
                        batchProgress.CurrentModProgress = null; // Clear individual progress
                        progress?.Report(batchProgress);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Batch installation failed for {Path.GetFileName(archivePath)}", "BatchInstallation", ex);
                        batchResult.FailedInstalls++;
                        var failedResult = new InstallationResult
                        {
                            Success = false,
                            ErrorMessage = ex.Message,
                            Operation = new InstallationOperation { ArchivePath = archivePath, Status = InstallationStatus.Failed }
                        };
                        batchResult.Results.Add(failedResult);
                        batchProgress.CompletedInstallations.Add(failedResult);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInfo("Batch installation cancelled by user", "BatchInstallation");
            }

            batchResult.EndTime = DateTime.Now;
            batchResult.Success = batchResult.FailedInstalls == 0;

            _logger.StopPerformanceTracking("BatchInstallation");
            _logger.LogOperation("BatchInstallation",
                $"Batch installation completed: {batchResult.SuccessfulInstalls}/{batchResult.TotalMods} successful");

            return batchResult;
        }

        private async Task ValidateArchiveSecurityAsync(string archivePath, CancellationToken cancellationToken)
        {
            _logger.LogDebug($"Performing comprehensive validation on {Path.GetFileName(archivePath)}", "ArchiveValidation");

            // Perform comprehensive validation (security, malware, content)
            var validationResult = await ArchiveValidationUtils.ValidateArchiveComprehensiveAsync(archivePath);

            if (!validationResult.IsValid)
            {
                var issues = string.Join("; ", validationResult.Issues);
                _logger.LogWarning($"Archive validation failed: {issues}", "ArchiveValidation");

                // Log detailed security information if available
                if (validationResult.SecurityResult != null && !validationResult.SecurityResult.IsSecure)
                {
                    _logger.LogWarning($"Security issues: Archive size: {FormatFileSize(validationResult.SecurityResult.ArchiveSize)}, " +
                                     $"Entries: {validationResult.SecurityResult.EntryCount}, " +
                                     $"Max depth: {validationResult.SecurityResult.MaxDirectoryDepth}", "SecurityValidation");
                }

                // Log malware detection details if available
                if (validationResult.MalwareResult != null && !validationResult.MalwareResult.IsSafe)
                {
                    if (validationResult.MalwareResult.ExecutableFiles.Count > 0)
                    {
                        _logger.LogWarning($"Executable files found: {string.Join(", ", validationResult.MalwareResult.ExecutableFiles)}", "MalwareDetection");
                    }

                    if (validationResult.MalwareResult.SuspiciousFiles.Count > 0)
                    {
                        _logger.LogWarning($"Suspicious files found: {string.Join(", ", validationResult.MalwareResult.SuspiciousFiles)}", "MalwareDetection");
                    }
                }

                throw new SecurityException($"Archive validation failed: {issues}");
            }

            // Log content analysis results
            if (validationResult.ContentResult != null)
            {
                var contentInfo = $"Content analysis: {validationResult.ContentResult.ValidModFileCount}/{validationResult.ContentResult.TotalFileCount} valid mod files";
                if (validationResult.ContentResult.HasPalFolder) contentInfo += ", Pal folder";
                if (validationResult.ContentResult.HasUE4SSMods) contentInfo += ", UE4SS mods";
                if (validationResult.ContentResult.HasPakFiles) contentInfo += ", PAK files";
                if (validationResult.ContentResult.HasPalSchemaMods) contentInfo += ", PalSchema mods";

                _logger.LogInfo(contentInfo, "ContentAnalysis");
            }

            _logger.LogInfo($"Archive passed comprehensive validation: {validationResult.SecurityResult?.EntryCount ?? 0} files, " +
                          $"{FormatFileSize(validationResult.SecurityResult?.ArchiveSize ?? 0)}", "ArchiveValidation");
        }

        private async Task<string> ExtractArchiveAsync(string archivePath, CancellationToken cancellationToken)
        {
            var extractor = _extractors.FirstOrDefault(e => e.CanExtract(archivePath));
            if (extractor == null)
            {
                throw new NotSupportedException($"Archive format not supported: {Path.GetExtension(archivePath)}");
            }

            var tempDir = Path.Combine(Path.GetTempPath(), "PalworldMod_" + Guid.NewGuid().ToString("N")[..8]);
            var canonicalTempDir = Path.GetFullPath(tempDir);
            Directory.CreateDirectory(canonicalTempDir);

            _logger.LogDebug($"Extracting {Path.GetFileName(archivePath)} using {extractor.GetType().Name}", "Extraction");

            try
            {
                await extractor.ExtractAsync(archivePath, canonicalTempDir);
                _logger.LogDebug($"Extraction completed to: {canonicalTempDir}", "Extraction");
                return canonicalTempDir;
            }
            catch
            {
                // Cleanup on failure
                if (Directory.Exists(canonicalTempDir))
                {
                    try
                    {
                        Directory.Delete(canonicalTempDir, true);
                    }
                    catch { }
                }
                throw;
            }
        }

        private async Task<ModStructure> AnalyzeModStructureAsync(string extractedPath, CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                var structure = new ModStructure { RootPath = extractedPath };

                // Look for Pal folder structure (traditional mod)
                var palFolders = Directory.GetDirectories(extractedPath, "Pal", SearchOption.AllDirectories);
                if (palFolders.Length > 0)
                {
                    structure.ModType = ModType.TraditionalMod;
                    structure.ContentPath = palFolders[0];
                    structure.Files = Directory.GetFiles(palFolders[0], "*", SearchOption.AllDirectories)
                                              .Select(f => Path.GetRelativePath(palFolders[0], f))
                                              .ToList();
                }

                cancellationToken.ThrowIfCancellationRequested();

                // Check for UE4SS mod structure
                var ue4ssModFolders = Directory.GetDirectories(extractedPath, "*", SearchOption.AllDirectories)
                    .Where(d => Directory.GetFiles(d, "*.lua", SearchOption.TopDirectoryOnly).Length > 0 ||
                                File.Exists(Path.Combine(d, "enabled.txt")))
                    .ToArray();

                if (ue4ssModFolders.Length > 0)
                {
                    if (structure.ModType == ModType.Unknown)
                        structure.ModType = ModType.UE4SSMod;
                    else
                        structure.ModType = ModType.HybridMod;

                    structure.UE4SSMods = ue4ssModFolders.Select(d => new UE4SSModInfo
                    {
                        Name = Path.GetFileName(d) ?? "",
                        Path = d,
                        HasLuaScripts = Directory.GetFiles(d, "*.lua", SearchOption.AllDirectories).Length > 0,
                        HasEnabledFile = File.Exists(Path.Combine(d, "enabled.txt")),
                        IsValid = true
                    }).ToList();
                }

                cancellationToken.ThrowIfCancellationRequested();

                // Check for PalSchema mod structure
                var palSchemaFolders = Directory.GetDirectories(extractedPath, "*", SearchOption.AllDirectories)
                    .Where(d => Directory.Exists(Path.Combine(d, "blueprints")) ||
                                Directory.Exists(Path.Combine(d, "items")) ||
                                Directory.Exists(Path.Combine(d, "raw")))
                    .ToArray();

                if (palSchemaFolders.Length > 0)
                {
                    if (structure.ModType == ModType.Unknown)
                        structure.ModType = ModType.PalSchemaMod;
                    else
                        structure.ModType = ModType.HybridMod;

                    structure.PalSchemaMods = palSchemaFolders.Select(d => new PalSchemaModInfo
                    {
                        Name = Path.GetFileName(d) ?? "",
                        Path = d,
                        HasBlueprints = Directory.Exists(Path.Combine(d, "blueprints")),
                        HasItems = Directory.Exists(Path.Combine(d, "items")),
                        HasRaw = Directory.Exists(Path.Combine(d, "raw")),
                        IsValid = true
                    }).ToList();
                }

                // Check for loose PAK files
                var pakFiles = Directory.GetFiles(extractedPath, "*.pak", SearchOption.AllDirectories);
                if (pakFiles.Length > 0)
                {
                    if (structure.ModType == ModType.Unknown)
                        structure.ModType = ModType.PakMod;
                    else
                        structure.ModType = ModType.HybridMod;

                    structure.PakFiles = pakFiles.Select(f => new PakFileInfo
                    {
                        Name = Path.GetFileName(f) ?? "",
                        Path = f,
                        Size = new FileInfo(f).Length,
                        IsVanilla = Path.GetFileName(f).Equals("Pal-Windows.pak", StringComparison.OrdinalIgnoreCase)
                    }).ToList();
                }

                // Extract metadata if available
                ExtractModMetadata(structure);

                _logger.LogDebug($"Analyzed mod structure: {structure.ModType}", "ModAnalysis", structure);
                
                return structure;
            }, cancellationToken);
        }

        private void ExtractModMetadata(ModStructure structure)
        {
            // Look for common metadata files
            var metadataFiles = new[] { "mod.json", "metadata.json", "info.json", "readme.txt", "readme.md" };
            
            foreach (var metadataFile in metadataFiles)
            {
                var found = Directory.GetFiles(structure.RootPath, metadataFile, SearchOption.AllDirectories);
                if (found.Length > 0)
                {
                    try
                    {
                        var content = File.ReadAllText(found[0]);
                        if (metadataFile.EndsWith(".json"))
                        {
                            var metadata = JsonSerializer.Deserialize<Dictionary<string, object>>(content);
                            structure.Metadata = metadata ?? new Dictionary<string, object>();
                        }
                        else
                        {
                            structure.ReadmeContent = content;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to parse metadata file {metadataFile}: {ex.Message}", "MetadataExtraction");
                    }
                }
            }
        }

        private async Task<CompatibilityResult> ValidateCompatibilityAsync(ModStructure structure, CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                var result = new CompatibilityResult { IsCompatible = true };
                var issues = new List<string>();

                // Check UE4SS compatibility
                if (structure.UE4SSMods.Count > 0)
                {
                    var ue4ssStatus = _detector.DetectUE4SS();
                    if (ue4ssStatus.Status == UE4SSInstallStatus.NotInstalled)
                    {
                        issues.Add("UE4SS is required but not installed");
                        result.IsCompatible = false;
                    }
                    else if (ue4ssStatus.Status == UE4SSInstallStatus.PartiallyInstalled)
                    {
                        issues.Add("UE4SS installation is incomplete");
                        result.RequiresUE4SSRepair = true;
                    }
                }

                // Check PalSchema compatibility
                if (structure.PalSchemaMods.Count > 0)
                {
                    var palSchemaStatus = _detector.DetectPalSchema();
                    if (!palSchemaStatus.IsInstalled)
                    {
                        issues.Add("PalSchema is required but not installed");
                        result.IsCompatible = false;
                    }
                }

                // Check for invalid file paths
                foreach (var file in structure.Files)
                {
                    if (file.Contains("..") || Path.IsPathRooted(file))
                    {
                        issues.Add($"Invalid file path detected: {file}");
                        result.IsCompatible = false;
                    }
                }

                // Check for conflicting core files
                var coreFiles = new[] { "dwmapi.dll", "UE4SS.dll", "UE4SS-settings.ini" };
                foreach (var coreFile in coreFiles)
                {
                    if (structure.Files.Any(f => Path.GetFileName(f).Equals(coreFile, StringComparison.OrdinalIgnoreCase)))
                    {
                        issues.Add($"Mod contains core UE4SS file: {coreFile}");
                        result.RequiresUE4SSRepair = true;
                    }
                }

                result.Issues = issues;
                result.Reason = string.Join("; ", issues);

                return result;
            }, cancellationToken);
        }

        private async Task<List<InstallationConflict>> DetectInstallationConflictsAsync(ModStructure structure, CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                var conflicts = new List<InstallationConflict>();

                // Check for existing files that would be overwritten
                foreach (var file in structure.Files)
                {
                    var targetPath = Path.Combine(_palRoot, "Pal", file);
                    if (File.Exists(targetPath))
                    {
                        var existingFile = new FileInfo(targetPath);
                        var newFile = new FileInfo(Path.Combine(structure.ContentPath, file));

                        conflicts.Add(new InstallationConflict
                        {
                            FilePath = file,
                            Type = InstallationConflictType.FileExists,
                            ExistingFileDate = existingFile.LastWriteTime,
                            ExistingFileSize = existingFile.Length,
                            NewFileDate = newFile.LastWriteTime,
                            NewFileSize = newFile.Length,
                            Resolution = ConflictResolution.Ask
                        });
                    }
                }

                // Check for UE4SS mod name conflicts
                var ue4ssStatus = _detector.DetectUE4SS();
                foreach (var newMod in structure.UE4SSMods)
                {
                    if (ue4ssStatus.UserMods.Contains(newMod.Name, StringComparer.OrdinalIgnoreCase))
                    {
                        conflicts.Add(new InstallationConflict
                        {
                            FilePath = $"UE4SS Mod: {newMod.Name}",
                            Type = InstallationConflictType.ModNameConflict,
                            Resolution = ConflictResolution.Ask
                        });
                    }

                    if (ue4ssStatus.CoreMods.Contains(newMod.Name, StringComparer.OrdinalIgnoreCase))
                    {
                        conflicts.Add(new InstallationConflict
                        {
                            FilePath = $"Core UE4SS Mod: {newMod.Name}",
                            Type = InstallationConflictType.CoreModConflict,
                            Resolution = ConflictResolution.Skip
                        });
                    }
                }

                // Check for PalSchema mod conflicts
                var palSchemaStatus = _detector.DetectPalSchema();
                foreach (var newMod in structure.PalSchemaMods)
                {
                    if (palSchemaStatus.Mods.Any(m => m.Name.Equals(newMod.Name, StringComparison.OrdinalIgnoreCase)))
                    {
                        conflicts.Add(new InstallationConflict
                        {
                            FilePath = $"PalSchema Mod: {newMod.Name}",
                            Type = InstallationConflictType.ModNameConflict,
                            Resolution = ConflictResolution.Ask
                        });
                    }
                }

                return conflicts;
            }, cancellationToken);
        }

        private async Task<string> CreatePreInstallBackupAsync(ModStructure structure, CancellationToken cancellationToken)
        {
            var backupDir = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.UserProfile),
                @"Downloads\Palworld Mods Backup",
                "PreInstall_" + DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss"));

            Directory.CreateDirectory(backupDir);

            // Create a targeted backup based on what will be affected
            var backupSettings = new BackupSettings
            {
                IncludeUE4SSCore = false,
                IncludeUE4SSCoreMods = false,
                IncludeUserMods = structure.UE4SSMods.Count > 0,
                IncludeGameContentMods = structure.Files.Count > 0 || structure.PakFiles.Count > 0,
                IncludePalSchemaSystem = false,
                IncludePalSchemaMods = structure.PalSchemaMods.Count > 0
            };

            var engine = new SmartBackupEngine(_palRoot, _detector, backupSettings);
            var filesToBackup = engine.GetFilesToBackup();

            await Task.Run(() =>
            {
                foreach (var sourceFile in filesToBackup)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    var relativePath = Path.GetRelativePath(_palRoot, sourceFile);
                    var destinationFile = Path.Combine(backupDir, relativePath);
                    
                    Directory.CreateDirectory(Path.GetDirectoryName(destinationFile)!);
                    File.Copy(sourceFile, destinationFile, true);
                }

                // Save metadata
                var metadata = engine.CreateBackupMetadata("Pre-installation automatic backup");
                var metadataPath = Path.Combine(backupDir, "backup_metadata.json");
                var metadataJson = JsonSerializer.Serialize(metadata, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(metadataPath, metadataJson);
            }, cancellationToken);

            _logger.LogInfo($"Pre-installation backup created: {backupDir}", "Backup");
            return backupDir;
        }

        private async Task PerformInstallationAsync(InstallationOperation operation, ModStructure structure, CancellationToken cancellationToken)
        {
            await PerformInstallationAsync(operation, structure, null, null, cancellationToken);
        }

        private async Task PerformInstallationAsync(InstallationOperation operation, ModStructure structure, InstallationProgress? progressReporter, IProgress<InstallationProgress>? progress, CancellationToken cancellationToken)
        {
            var totalFiles = structure.Files.Count + structure.UE4SSMods.Count + structure.PalSchemaMods.Count + structure.PakFiles.Count;
            operation.TotalFiles = totalFiles;

            // Calculate total bytes for more accurate progress reporting
            long totalBytes = 0;
            foreach (var file in structure.Files)
            {
                var filePath = Path.Combine(structure.RootPath, file);
                if (File.Exists(filePath))
                {
                    totalBytes += new FileInfo(filePath).Length;
                }
            }

            // Add UE4SS mod sizes
            foreach (var mod in structure.UE4SSMods)
            {
                if (Directory.Exists(mod.Path))
                {
                    totalBytes += GetDirectorySize(mod.Path);
                }
            }

            // Add PalSchema mod sizes
            foreach (var mod in structure.PalSchemaMods)
            {
                if (Directory.Exists(mod.Path))
                {
                    totalBytes += GetDirectorySize(mod.Path);
                }
            }

            // Add PAK file sizes
            foreach (var pak in structure.PakFiles)
            {
                if (File.Exists(pak.Path))
                {
                    totalBytes += new FileInfo(pak.Path).Length;
                }
            }

            if (progressReporter != null)
            {
                progressReporter.TotalBytes = totalBytes;
                progressReporter.TotalFiles = totalFiles;
            }

            await Task.Run(async () =>
            {
                // Install traditional mod files
                if (!string.IsNullOrEmpty(structure.ContentPath))
                {
                    var palTargetPath = Path.Combine(_palRoot, "Pal");
                    foreach (var file in structure.Files)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        var sourcePath = Path.Combine(structure.ContentPath, file);
                        var targetPath = Path.Combine(palTargetPath, file);

                        // Validate target path for security
                        var canonicalTarget = Path.GetFullPath(targetPath);
                        var canonicalPalTarget = Path.GetFullPath(palTargetPath);
                        if (!canonicalTarget.StartsWith(canonicalPalTarget, StringComparison.OrdinalIgnoreCase))
                        {
                            throw new SecurityException($"Path traversal detected: {file}");
                        }

                        Directory.CreateDirectory(Path.GetDirectoryName(targetPath)!);

                        // Enhanced file copy with byte tracking
                        var fileSize = new FileInfo(sourcePath).Length;
                        await CopyFileWithProgressAsync(sourcePath, targetPath, cancellationToken);

                        operation.InstalledFiles.Add(targetPath);
                        operation.ProcessedFiles++;

                        // Report progress with byte information
                        if (progressReporter != null && progress != null)
                        {
                            progressReporter.ProcessedFiles = operation.ProcessedFiles;
                            progressReporter.ProcessedBytes += fileSize;
                            progressReporter.CurrentFile = file;
                            progressReporter.LastUpdateTime = DateTime.Now;
                            progress.Report(progressReporter);
                        }

                        _logger.LogDebug($"Installed file: {file}", "ModInstallation");
                    }
                }

                // Install UE4SS mods
                var ue4ssModsPath = Path.Combine(_palRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods");
                foreach (var mod in structure.UE4SSMods)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var targetModPath = Path.Combine(ue4ssModsPath, mod.Name);

                    if (Directory.Exists(targetModPath) && operation.Options.ConflictResolution == ConflictResolution.Skip)
                    {
                        _logger.LogInfo($"Skipped existing UE4SS mod: {mod.Name}", "ModInstallation");
                        continue;
                    }

                    // Copy mod folder with progress tracking
                    var modSize = GetDirectorySize(mod.Path);
                    await CopyDirectoryWithProgressAsync(mod.Path, targetModPath, true, progressReporter, progress, cancellationToken);
                    operation.InstalledFiles.Add(targetModPath);
                    operation.ProcessedFiles++;

                    // Report progress
                    if (progressReporter != null && progress != null)
                    {
                        progressReporter.ProcessedFiles = operation.ProcessedFiles;
                        progressReporter.ProcessedBytes += modSize;
                        progressReporter.CurrentFile = $"UE4SS mod: {mod.Name}";
                        progressReporter.LastUpdateTime = DateTime.Now;
                        progress.Report(progressReporter);
                    }

                    _logger.LogInfo($"Installed UE4SS mod: {mod.Name}", "ModInstallation");
                }

                // Install PalSchema mods
                var palSchemaModsPath = Path.Combine(_palRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods", "palschema", "mods");
                if (Directory.Exists(palSchemaModsPath))
                {
                    foreach (var mod in structure.PalSchemaMods)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        var targetModPath = Path.Combine(palSchemaModsPath, mod.Name);

                        if (Directory.Exists(targetModPath) && operation.Options.ConflictResolution == ConflictResolution.Skip)
                        {
                            _logger.LogInfo($"Skipped existing PalSchema mod: {mod.Name}", "ModInstallation");
                            continue;
                        }

                        CopyDirectory(mod.Path, targetModPath, true, cancellationToken);
                        operation.InstalledFiles.Add(targetModPath);
                        operation.ProcessedFiles++;

                        // Report progress
                        if (progressReporter != null && progress != null)
                        {
                            progressReporter.ProcessedFiles = operation.ProcessedFiles;
                            progressReporter.CurrentFile = $"PalSchema mod: {mod.Name}";
                            progressReporter.LastUpdateTime = DateTime.Now;
                            progress.Report(progressReporter);
                        }

                        _logger.LogInfo($"Installed PalSchema mod: {mod.Name}", "ModInstallation");
                    }
                }

                // Install PAK files
                var paksPath = Path.Combine(_palRoot, "Pal", "Content", "Paks");
                foreach (var pak in structure.PakFiles.Where(p => !p.IsVanilla))
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var targetPath = Path.Combine(paksPath, pak.Name);

                    if (File.Exists(targetPath) && operation.Options.ConflictResolution == ConflictResolution.Skip)
                    {
                        _logger.LogInfo($"Skipped existing PAK file: {pak.Name}", "ModInstallation");
                        continue;
                    }

                    var pakSize = new FileInfo(pak.Path).Length;
                    await CopyFileWithProgressAsync(pak.Path, targetPath, cancellationToken);
                    operation.InstalledFiles.Add(targetPath);
                    operation.ProcessedFiles++;

                    // Report progress
                    if (progressReporter != null && progress != null)
                    {
                        progressReporter.ProcessedFiles = operation.ProcessedFiles;
                        progressReporter.ProcessedBytes += pakSize;
                        progressReporter.CurrentFile = $"PAK file: {pak.Name}";
                        progressReporter.LastUpdateTime = DateTime.Now;
                        progress.Report(progressReporter);
                    }

                    _logger.LogInfo($"Installed PAK file: {pak.Name}", "ModInstallation");
                }
            }, cancellationToken);

            _logger.LogInfo($"Installation completed: {operation.ProcessedFiles}/{operation.TotalFiles} items installed", "ModInstallation");
        }

        private async Task PostInstallationTasksAsync(InstallationOperation operation, ModStructure structure, CancellationToken cancellationToken)
        {
            await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                // Update mod manifest
                UpdateModManifest(operation, structure);

                // Log installation record
                LogInstallationRecord(operation, structure);

                // Update cache
                _detector.InvalidateCache();
            }, cancellationToken);
        }

        private void UpdateModManifest(InstallationOperation operation, ModStructure structure)
        {
            try
            {
                var manifestPath = Path.Combine(_palRoot, "mod_manifest.json");
                var manifest = new Dictionary<string, InstallationRecord>();

                if (File.Exists(manifestPath))
                {
                    try
                    {
                        var existingJson = File.ReadAllText(manifestPath);
                        var existingManifest = JsonSerializer.Deserialize<Dictionary<string, object>>(existingJson) ?? new();

                        // Convert existing records to InstallationRecord objects
                        foreach (var kvp in existingManifest)
                        {
                            try
                            {
                                var recordJson = JsonSerializer.Serialize(kvp.Value);
                                var record = JsonSerializer.Deserialize<InstallationRecord>(recordJson);
                                if (record != null)
                                {
                                    manifest[kvp.Key] = record;
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning($"Failed to convert existing record {kvp.Key}: {ex.Message}", "Manifest");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to read existing manifest: {ex.Message}", "Manifest");
                    }
                }

                // Create enhanced installation record with proper ID
                var installRecord = new InstallationRecord
                {
                    Id = operation.Id,
                    ModName = ExtractModName(structure),
                    ArchiveName = Path.GetFileName(operation.ArchivePath),
                    ArchivePath = operation.ArchivePath,
                    InstallDate = DateTime.Now,
                    ModType = structure.ModType,
                    Version = ExtractVersion(structure),
                    Author = ExtractAuthor(structure),
                    InstalledFiles = operation.InstalledFiles.Select(f => Path.GetRelativePath(_palRoot, f)).ToList(),
                    UE4SSMods = structure.UE4SSMods.Select(m => m.Name).ToList(),
                    PalSchemaMods = structure.PalSchemaMods.Select(m => m.Name).ToList(),
                    PakFiles = structure.PakFiles.Where(p => !p.IsVanilla).Select(p => p.Name).ToList(),
                    BackupPath = operation.BackupPath,
                    CanUninstall = true,
                    CanRollback = !string.IsNullOrEmpty(operation.BackupPath) && Directory.Exists(operation.BackupPath)
                };

                // Use operation ID as the key for easy lookup
                manifest[operation.Id] = installRecord;

                var manifestJson = JsonSerializer.Serialize(manifest, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(manifestPath, manifestJson);
                _logger.LogDebug($"Mod manifest updated with installation ID: {operation.Id}", "Manifest");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to update mod manifest", "Manifest", ex);
            }
        }

        private string ExtractModName(ModStructure structure)
        {
            // Try to extract mod name from metadata
            if (structure.Metadata.TryGetValue("name", out var nameObj) && nameObj is JsonElement nameElement)
            {
                return nameElement.GetString() ?? "Unknown Mod";
            }

            // Fallback to first UE4SS mod name or archive name
            if (structure.UE4SSMods.Count > 0)
            {
                return structure.UE4SSMods[0].Name;
            }

            if (structure.PalSchemaMods.Count > 0)
            {
                return structure.PalSchemaMods[0].Name;
            }

            return "Unknown Mod";
        }

        private string ExtractVersion(ModStructure structure)
        {
            if (structure.Metadata.TryGetValue("version", out var versionObj) && versionObj is JsonElement versionElement)
            {
                return versionElement.GetString() ?? "1.0.0";
            }
            return "1.0.0";
        }

        private string ExtractAuthor(ModStructure structure)
        {
            if (structure.Metadata.TryGetValue("author", out var authorObj) && authorObj is JsonElement authorElement)
            {
                return authorElement.GetString() ?? "Unknown";
            }
            return "Unknown";
        }

        private void LogInstallationRecord(InstallationOperation operation, ModStructure structure)
        {
            var installationRecord = new
            {
                operation.ArchivePath,
                ArchiveName = Path.GetFileName(operation.ArchivePath),
                operation.StartTime,
                operation.EndTime,
                Duration = operation.Duration.TotalSeconds,
                structure.ModType,
                FileCount = operation.ProcessedFiles,
                UE4SSModCount = structure.UE4SSMods.Count,
                PalSchemaModCount = structure.PalSchemaMods.Count,
                PakFileCount = structure.PakFiles.Count,
                operation.BackupPath,
                ConflictCount = operation.Conflicts.Count
            };

            _logger.LogOperation("InstallationComplete", "Mod installation completed", installationRecord);
        }

        private void CopyDirectory(string sourceDir, string targetDir, bool recursive, CancellationToken cancellationToken)
        {
            var dir = new DirectoryInfo(sourceDir);
            DirectoryInfo[] dirs = dir.GetDirectories();

            Directory.CreateDirectory(targetDir);

            foreach (FileInfo file in dir.GetFiles())
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                string targetFilePath = Path.Combine(targetDir, file.Name);
                file.CopyTo(targetFilePath, true);
            }

            if (recursive)
            {
                foreach (DirectoryInfo subDir in dirs)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    string newTargetDir = Path.Combine(targetDir, subDir.Name);
                    CopyDirectory(subDir.FullName, newTargetDir, true, cancellationToken);
                }
            }
        }

        private static string? ValidatePath(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
                return null;
                
            try
            {
                // Secure path validation to prevent path traversal
                string canonicalPath = Path.GetFullPath(path);
                
                // Ensure the path exists and contains a Palworld game
                if (!Directory.Exists(canonicalPath))
                    return null;
                    
                var gameExePath = Path.Combine(canonicalPath, "Palworld.exe");
                if (!File.Exists(gameExePath))
                    return null;
                    
                return canonicalPath;
            }
            catch
            {
                return null;
            }
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(EnhancedInstallationEngine));
        }

        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len /= 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        private ModStructure ConvertToBasicStructure(AdvancedModStructure advanced)
        {
            return new ModStructure
            {
                RootPath = advanced.RootPath,
                ContentPath = advanced.ContentPath,
                ModType = advanced.ModType,
                Files = advanced.Files,
                UE4SSMods = advanced.UE4SSMods,
                PalSchemaMods = advanced.PalSchemaMods,
                PakFiles = advanced.PakFiles,
                Metadata = advanced.Metadata,
                ReadmeContent = advanced.ReadmeContent,
                TotalSize = advanced.TotalSize,
                Author = advanced.Author,
                Version = advanced.Version,
                Description = advanced.Description,
                Dependencies = advanced.Dependencies,
                Conflicts = advanced.Conflicts,
                Tags = advanced.Tags
            };
        }

        private List<InstallationConflict> ConvertToBasicConflicts(ConflictDetectionResult conflictResult)
        {
            return conflictResult.Conflicts.Select(c => new InstallationConflict
            {
                Type = c.Type,
                Description = c.Description,
                FilePath = c.AffectedFile,
                Severity = c.Severity,
                Resolution = ConflictResolution.Ask,
                RecommendedAction = c.RecommendedResolution,
                CanAutoResolve = c.Severity <= InstallationConflictSeverity.Medium
            }).ToList();
        }

        public async Task<UninstallationResult> UninstallModAsync(string modIdentifier, UninstallationOptions options)
        {
            var result = new UninstallationResult();
            var startTime = DateTime.Now;

            try
            {
                _logger.LogInfo($"Starting uninstallation of mod: {modIdentifier}", "ModUninstallation");

                // Load mod manifest to find installation record
                var manifestPath = Path.Combine(_palRoot, "mod_manifest.json");
                if (!File.Exists(manifestPath))
                {
                    result.ErrorMessage = "No installation manifest found. Cannot uninstall mod.";
                    return result;
                }

                var manifestJson = await File.ReadAllTextAsync(manifestPath);
                var manifest = JsonSerializer.Deserialize<Dictionary<string, object>>(manifestJson) ?? new();

                // Find the installation record
                var installationRecord = FindInstallationRecord(manifest, modIdentifier);
                if (installationRecord == null)
                {
                    result.ErrorMessage = $"Mod '{modIdentifier}' not found in installation manifest.";
                    return result;
                }

                // Create backup if requested
                if (options.CreateBackupBeforeUninstall)
                {
                    var backupPath = Path.Combine(_palRoot, "Backups", $"pre_uninstall_{modIdentifier}_{DateTime.Now:yyyyMMdd_HHmmss}");
                    var backupEngine = new SmartBackupEngine(_palRoot, _detector, new BackupSettings());
                    var backupResult = await backupEngine.CreateBackupAsync(backupPath);

                    if (backupResult.Success)
                    {
                        result.BackupPath = backupPath;
                        _logger.LogInfo($"Pre-uninstall backup created: {backupPath}", "ModUninstallation");
                    }
                    else
                    {
                        _logger.LogWarning("Failed to create pre-uninstall backup, continuing with uninstallation", "ModUninstallation");
                    }
                }

                // Extract file list from installation record
                var installedFiles = ExtractInstalledFiles(installationRecord);

                // Remove files
                await RemoveInstalledFilesAsync(installedFiles, options, result);

                // Remove from manifest
                RemoveFromManifest(manifest, modIdentifier, manifestPath);

                // Invalidate cache
                _detector.InvalidateCache();

                result.Success = result.FailedToRemove.Count == 0;
                result.Duration = DateTime.Now - startTime;

                _logger.LogInfo($"Uninstallation completed: {result.RemovedFiles.Count} files removed, {result.FailedToRemove.Count} failed", "ModUninstallation");

                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.Duration = DateTime.Now - startTime;
                _logger.LogError("Uninstallation failed", "ModUninstallation", ex);
                return result;
            }
        }

        public async Task<RollbackResult> RollbackInstallationAsync(string installationId)
        {
            ThrowIfDisposed();

            var result = new RollbackResult();
            var startTime = DateTime.Now;

            try
            {
                _logger.LogInfo($"Starting rollback for installation: {installationId}", "InstallationRollback");

                // Load installation record from manifest
                var installationRecord = await GetInstallationRecordAsync(installationId);
                if (installationRecord == null)
                {
                    result.ErrorMessage = $"Installation record not found for ID: {installationId}";
                    return result;
                }

                if (!installationRecord.CanRollback)
                {
                    result.ErrorMessage = $"Installation {installationId} cannot be rolled back (no backup available)";
                    return result;
                }

                if (string.IsNullOrEmpty(installationRecord.BackupPath) || !Directory.Exists(installationRecord.BackupPath))
                {
                    result.ErrorMessage = $"Backup not found for installation {installationId}";
                    return result;
                }

                _logger.LogInfo($"Rolling back installation: {installationRecord.ModName} ({installationRecord.ArchiveName})", "InstallationRollback");

                // Step 1: Remove installed files
                var filesToRemove = new List<string>();
                foreach (var relativePath in installationRecord.InstalledFiles)
                {
                    var fullPath = Path.Combine(_palRoot, relativePath);
                    if (File.Exists(fullPath))
                    {
                        filesToRemove.Add(fullPath);
                    }
                }

                _logger.LogInfo($"Removing {filesToRemove.Count} installed files", "InstallationRollback");

                foreach (var file in filesToRemove)
                {
                    try
                    {
                        File.Delete(file);
                        result.RestoredFiles.Add(Path.GetRelativePath(_palRoot, file));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to remove file {file}: {ex.Message}", "InstallationRollback");
                        result.FailedToRestore.Add(Path.GetRelativePath(_palRoot, file));
                    }
                }

                // Step 2: Restore from backup if backup contains files
                if (Directory.Exists(installationRecord.BackupPath))
                {
                    var restoreEngine = new RestoreEngine(_palRoot, _detector, _cache, msg => _logger.LogInfo(msg, "RestoreEngine"));
                    var restoreOperation = await restoreEngine.RestoreBackupAsync(installationRecord.BackupPath, BackupConflictResolution.ForceRestore);

                    if (restoreOperation.Status == RestoreOperationStatus.Completed)
                    {
                        _logger.LogInfo($"Backup restored successfully: {restoreOperation.FilesProcessed} files", "InstallationRollback");
                    }
                    else
                    {
                        _logger.LogWarning($"Backup restore completed with issues: {restoreOperation.Status}", "InstallationRollback");
                    }
                }

                // Step 3: Remove installation from manifest
                await RemoveInstallationFromManifestAsync(installationId);

                // Step 4: Clean up empty directories
                await CleanupEmptyDirectoriesAsync(installationRecord.InstalledFiles);

                result.Success = true;
                result.Duration = DateTime.Now - startTime;

                _logger.LogInfo($"Rollback completed successfully in {result.Duration.TotalSeconds:F1} seconds", "InstallationRollback");

                // Invalidate cache to reflect changes
                _detector.InvalidateCache();
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.Duration = DateTime.Now - startTime;

                _logger.LogError($"Rollback failed for installation {installationId}", "InstallationRollback", ex);
            }

            return result;
        }

        private object? FindInstallationRecord(Dictionary<string, object> manifest, string modIdentifier)
        {
            // Try to find by exact key match first
            if (manifest.ContainsKey(modIdentifier))
            {
                return manifest[modIdentifier];
            }

            // Try to find by archive name in the record
            foreach (var kvp in manifest)
            {
                if (kvp.Value is JsonElement element && element.ValueKind == JsonValueKind.Object)
                {
                    if (element.TryGetProperty("ArchiveName", out var archiveNameProp))
                    {
                        var archiveName = archiveNameProp.GetString();
                        if (string.Equals(archiveName, modIdentifier, StringComparison.OrdinalIgnoreCase) ||
                            string.Equals(Path.GetFileNameWithoutExtension(archiveName), modIdentifier, StringComparison.OrdinalIgnoreCase))
                        {
                            return kvp.Value;
                        }
                    }
                }
            }

            return null;
        }

        private List<string> ExtractInstalledFiles(object installationRecord)
        {
            var files = new List<string>();

            if (installationRecord is JsonElement element && element.ValueKind == JsonValueKind.Object)
            {
                if (element.TryGetProperty("InstalledFiles", out var filesProperty) &&
                    filesProperty.ValueKind == JsonValueKind.Array)
                {
                    foreach (var fileElement in filesProperty.EnumerateArray())
                    {
                        var relativePath = fileElement.GetString();
                        if (!string.IsNullOrEmpty(relativePath))
                        {
                            var fullPath = Path.Combine(_palRoot, relativePath);
                            files.Add(fullPath);
                        }
                    }
                }
            }

            return files;
        }

        private async Task RemoveInstalledFilesAsync(List<string> installedFiles, UninstallationOptions options, UninstallationResult result)
        {
            foreach (var filePath in installedFiles)
            {
                try
                {
                    if (File.Exists(filePath))
                    {
                        // Validate before removal if requested
                        if (options.ValidateBeforeRemoval)
                        {
                            // Check if file should be kept based on patterns
                            var fileName = Path.GetFileName(filePath);
                            if (options.KeepPatterns.Any(pattern =>
                                fileName.Contains(pattern, StringComparison.OrdinalIgnoreCase)))
                            {
                                _logger.LogDebug($"Keeping file due to keep pattern: {filePath}", "ModUninstallation");
                                continue;
                            }
                        }

                        File.Delete(filePath);
                        result.RemovedFiles.Add(filePath);
                        _logger.LogDebug($"Removed file: {filePath}", "ModUninstallation");
                    }
                }
                catch (Exception ex)
                {
                    result.FailedToRemove.Add(filePath);
                    _logger.LogWarning($"Failed to remove file {filePath}: {ex.Message}", "ModUninstallation");
                }
            }

            // Remove empty directories if requested
            if (options.RemoveEmptyDirectories)
            {
                await RemoveEmptyDirectoriesAsync(installedFiles);
            }
        }

        private async Task RemoveEmptyDirectoriesAsync(List<string> installedFiles)
        {
            await Task.Run(() =>
            {
                var directories = installedFiles
                    .Select(Path.GetDirectoryName)
                    .Where(d => !string.IsNullOrEmpty(d))
                    .Cast<string>()
                    .Distinct()
                    .OrderByDescending(d => d.Length) // Remove deepest directories first
                    .ToList();

                foreach (var directory in directories)
                {
                    try
                    {
                        if (Directory.Exists(directory) && !Directory.EnumerateFileSystemEntries(directory).Any())
                        {
                            Directory.Delete(directory);
                            _logger.LogDebug($"Removed empty directory: {directory}", "ModUninstallation");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug($"Could not remove directory {directory}: {ex.Message}", "ModUninstallation");
                    }
                }
            });
        }

        private void RemoveFromManifest(Dictionary<string, object> manifest, string modIdentifier, string manifestPath)
        {
            try
            {
                // Find and remove the installation record
                var keysToRemove = new List<string>();

                foreach (var kvp in manifest)
                {
                    if (kvp.Key == modIdentifier)
                    {
                        keysToRemove.Add(kvp.Key);
                        break;
                    }

                    if (kvp.Value is JsonElement element && element.ValueKind == JsonValueKind.Object)
                    {
                        if (element.TryGetProperty("ArchiveName", out var archiveNameProp))
                        {
                            var archiveName = archiveNameProp.GetString();
                            if (string.Equals(archiveName, modIdentifier, StringComparison.OrdinalIgnoreCase) ||
                                string.Equals(Path.GetFileNameWithoutExtension(archiveName), modIdentifier, StringComparison.OrdinalIgnoreCase))
                            {
                                keysToRemove.Add(kvp.Key);
                                break;
                            }
                        }
                    }
                }

                foreach (var key in keysToRemove)
                {
                    manifest.Remove(key);
                }

                // Save updated manifest
                var manifestJson = JsonSerializer.Serialize(manifest, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(manifestPath, manifestJson);
                _logger.LogDebug("Manifest updated after uninstallation", "ModUninstallation");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to update manifest after uninstallation", "ModUninstallation", ex);
            }
        }

        private string? FindInstallationBackup(string installationId)
        {
            try
            {
                var backupsPath = Path.Combine(_palRoot, "Backups");
                if (!Directory.Exists(backupsPath))
                    return null;

                // Look for backup directories that match the installation ID
                var backupDirs = Directory.GetDirectories(backupsPath)
                    .Where(d => Path.GetFileName(d).Contains(installationId, StringComparison.OrdinalIgnoreCase))
                    .OrderByDescending(d => Directory.GetCreationTime(d))
                    .ToList();

                return backupDirs.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to find backup for installation {installationId}", "InstallationRollback", ex);
                return null;
            }
        }

        private async Task<InstallationRecord?> GetInstallationRecordAsync(string installationId)
        {
            try
            {
                var manifestPath = Path.Combine(_palRoot, "mod_manifest.json");
                if (!File.Exists(manifestPath))
                {
                    return null;
                }

                var manifestJson = await File.ReadAllTextAsync(manifestPath);
                var manifest = JsonSerializer.Deserialize<Dictionary<string, InstallationRecord>>(manifestJson);

                if (manifest != null && manifest.TryGetValue(installationId, out var record))
                {
                    return record;
                }

                // Fallback: try to find by searching through records (for backward compatibility)
                var legacyManifest = JsonSerializer.Deserialize<Dictionary<string, object>>(manifestJson);
                if (legacyManifest != null)
                {
                    foreach (var kvp in legacyManifest)
                    {
                        try
                        {
                            var recordJson = JsonSerializer.Serialize(kvp.Value);
                            var legacyRecord = JsonSerializer.Deserialize<InstallationRecord>(recordJson);
                            if (legacyRecord != null && legacyRecord.Id == installationId)
                            {
                                return legacyRecord;
                            }
                        }
                        catch
                        {
                            // Skip invalid records
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to load installation record {installationId}", "InstallationRollback", ex);
            }

            return null;
        }

        private async Task CleanupEmptyDirectoriesAsync(List<string> installedFiles)
        {
            await Task.Run(() =>
            {
                var directories = new HashSet<string>();

                // Collect all directories that contained installed files
                foreach (var file in installedFiles)
                {
                    var fullPath = Path.Combine(_palRoot, file);
                    var directory = Path.GetDirectoryName(fullPath);
                    if (!string.IsNullOrEmpty(directory))
                    {
                        directories.Add(directory);
                    }
                }

                // Remove empty directories (bottom-up)
                var sortedDirs = directories.OrderByDescending(d => d.Length).ToList();
                foreach (var dir in sortedDirs)
                {
                    try
                    {
                        if (Directory.Exists(dir) && !Directory.EnumerateFileSystemEntries(dir).Any())
                        {
                            Directory.Delete(dir);
                            _logger.LogDebug($"Removed empty directory: {Path.GetRelativePath(_palRoot, dir)}", "InstallationRollback");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug($"Could not remove directory {dir}: {ex.Message}", "InstallationRollback");
                    }
                }
            });
        }

        private async Task RemoveInstallationFromManifestAsync(string installationId)
        {
            try
            {
                var manifestPath = Path.Combine(_palRoot, "mod_manifest.json");
                if (!File.Exists(manifestPath))
                    return;

                var manifestJson = await File.ReadAllTextAsync(manifestPath);
                var manifest = JsonSerializer.Deserialize<Dictionary<string, InstallationRecord>>(manifestJson) ?? new();

                if (manifest.ContainsKey(installationId))
                {
                    manifest.Remove(installationId);
                    var updatedJson = JsonSerializer.Serialize(manifest, new JsonSerializerOptions { WriteIndented = true });
                    await File.WriteAllTextAsync(manifestPath, updatedJson);
                    _logger.LogDebug($"Removed installation {installationId} from manifest", "InstallationRollback");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to remove installation {installationId} from manifest", "InstallationRollback", ex);
            }
        }

        public async Task<List<InstallationRecord>> GetAllInstallationsAsync()
        {
            try
            {
                var manifestPath = Path.Combine(_palRoot, "mod_manifest.json");
                if (!File.Exists(manifestPath))
                {
                    return new List<InstallationRecord>();
                }

                var manifestJson = await File.ReadAllTextAsync(manifestPath);
                var manifest = JsonSerializer.Deserialize<Dictionary<string, InstallationRecord>>(manifestJson);

                if (manifest != null)
                {
                    return manifest.Values.OrderByDescending(r => r.InstallDate).ToList();
                }

                // Fallback for legacy format
                var legacyManifest = JsonSerializer.Deserialize<Dictionary<string, object>>(manifestJson);
                var records = new List<InstallationRecord>();

                if (legacyManifest != null)
                {
                    foreach (var kvp in legacyManifest)
                    {
                        try
                        {
                            var recordJson = JsonSerializer.Serialize(kvp.Value);
                            var record = JsonSerializer.Deserialize<InstallationRecord>(recordJson);
                            if (record != null)
                            {
                                records.Add(record);
                            }
                        }
                        catch
                        {
                            // Skip invalid records
                        }
                    }
                }

                return records.OrderByDescending(r => r.InstallDate).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to load installation records", "InstallationManagement", ex);
                return new List<InstallationRecord>();
            }
        }

        private long GetDirectorySize(string directoryPath)
        {
            if (!Directory.Exists(directoryPath))
                return 0;

            long size = 0;
            try
            {
                var files = Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories);
                foreach (var file in files)
                {
                    try
                    {
                        size += new FileInfo(file).Length;
                    }
                    catch
                    {
                        // Skip files that can't be accessed
                    }
                }
            }
            catch
            {
                // Return 0 if directory can't be accessed
            }
            return size;
        }

        private async Task CopyFileWithProgressAsync(string sourcePath, string destinationPath, CancellationToken cancellationToken)
        {
            const int bufferSize = 1024 * 1024; // 1MB buffer

            using var sourceStream = new FileStream(sourcePath, FileMode.Open, FileAccess.Read, FileShare.Read, bufferSize, FileOptions.SequentialScan);
            using var destinationStream = new FileStream(destinationPath, FileMode.Create, FileAccess.Write, FileShare.None, bufferSize, FileOptions.SequentialScan);

            var buffer = new byte[bufferSize];
            int bytesRead;

            while ((bytesRead = await sourceStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken)) > 0)
            {
                await destinationStream.WriteAsync(buffer, 0, bytesRead, cancellationToken);
                cancellationToken.ThrowIfCancellationRequested();
            }
        }

        private async Task CopyDirectoryWithProgressAsync(string sourcePath, string destinationPath, bool recursive, InstallationProgress? progressReporter, IProgress<InstallationProgress>? progress, CancellationToken cancellationToken)
        {
            var sourceDir = new DirectoryInfo(sourcePath);
            if (!sourceDir.Exists)
                return;

            Directory.CreateDirectory(destinationPath);

            // Copy files
            foreach (var file in sourceDir.GetFiles())
            {
                cancellationToken.ThrowIfCancellationRequested();

                var targetFilePath = Path.Combine(destinationPath, file.Name);
                await CopyFileWithProgressAsync(file.FullName, targetFilePath, cancellationToken);

                // Update progress for individual files within directory
                if (progressReporter != null && progress != null)
                {
                    progressReporter.CurrentFile = Path.GetRelativePath(sourcePath, file.FullName);
                    progressReporter.LastUpdateTime = DateTime.Now;
                    progress.Report(progressReporter);
                }
            }

            // Copy subdirectories recursively
            if (recursive)
            {
                foreach (var subDir in sourceDir.GetDirectories())
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var targetDirPath = Path.Combine(destinationPath, subDir.Name);
                    await CopyDirectoryWithProgressAsync(subDir.FullName, targetDirPath, true, progressReporter, progress, cancellationToken);
                }
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Clean up resources
                _disposed = true;
            }
        }

        ~EnhancedInstallationEngine()
        {
            Dispose(false);
        }
    }
}