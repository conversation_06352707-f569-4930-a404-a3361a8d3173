using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ModInstallerApp.Models
{
    // ── INSTALLATION OPERATION MODELS ──
    
    public class InstallationOperation
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string ArchivePath { get; set; } = "";
        public string TargetPath { get; set; } = "";
        public string ExtractedPath { get; set; } = "";
        public DateTime StartTime { get; set; } = DateTime.Now;
        public DateTime? EndTime { get; set; }
        public InstallationStatus Status { get; set; } = InstallationStatus.Pending;
        public InstallationOptions Options { get; set; } = new();
        public ModStructure? DetectedStructure { get; set; }
        public CompatibilityResult? CompatibilityResult { get; set; }
        public List<InstallationConflict> Conflicts { get; set; } = new();
        public List<string> InstalledFiles { get; set; } = new();
        public int ProcessedFiles { get; set; }
        public int TotalFiles { get; set; }
        public string? BackupPath { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? DateTime.Now.Subtract(StartTime);
        public double ProgressPercent => TotalFiles > 0 ? (double)ProcessedFiles / TotalFiles * 100 : 0;
    }

    public class InstallationOptions
    {
        public bool ForceInstall { get; set; } = false;
        public bool CreateBackupBeforeInstall { get; set; } = true;
        public ConflictResolution ConflictResolution { get; set; } = ConflictResolution.Ask;
        public bool OverwriteExisting { get; set; } = false;
        public bool SkipCompatibilityCheck { get; set; } = false;
        public bool EnableUE4SSIfNeeded { get; set; } = false;
        public bool InstallPalSchemaIfNeeded { get; set; } = false;
        public bool ValidateFileIntegrity { get; set; } = true;
        public List<string> ExcludePatterns { get; set; } = new();
        public Dictionary<string, object> CustomSettings { get; set; } = new();
    }

    public class InstallationResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = "";
        public InstallationOperation? Operation { get; set; }
        public List<string> InstalledFiles { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public bool RequiresConflictResolution { get; set; }
        public bool RequiresRestart { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public class BatchInstallationResult
    {
        public bool Success { get; set; }
        public int TotalMods { get; set; }
        public int ProcessedMods { get; set; }
        public int SuccessfulInstalls { get; set; }
        public int FailedInstalls { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public List<InstallationResult> Results { get; set; } = new();
        public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? DateTime.Now.Subtract(StartTime);
        public double ProgressPercent => TotalMods > 0 ? (double)ProcessedMods / TotalMods * 100 : 0;
    }

    // ── MOD STRUCTURE MODELS ──

    public class ModStructure
    {
        public string RootPath { get; set; } = "";
        public string ContentPath { get; set; } = "";
        public ModType ModType { get; set; } = ModType.Unknown;
        public List<string> Files { get; set; } = new();
        public List<UE4SSModInfo> UE4SSMods { get; set; } = new();
        public List<PalSchemaModInfo> PalSchemaMods { get; set; } = new();
        public List<PakFileInfo> PakFiles { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
        public string ReadmeContent { get; set; } = "";
        public long TotalSize { get; set; }
        public string Author { get; set; } = "";
        public string Version { get; set; } = "";
        public string Description { get; set; } = "";
        public List<string> Dependencies { get; set; } = new();
        public List<string> Conflicts { get; set; } = new();
        public List<string> Tags { get; set; } = new();
    }

    // ── ADVANCED MOD STRUCTURE ──

    public class AdvancedModStructure : ModStructure
    {
        public DateTime AnalysisDate { get; set; } = DateTime.Now;
        public string Name { get; set; } = "";

        // Structure Analysis
        public int TotalDirectories { get; set; }
        public int TotalFiles { get; set; }
        public int MaxDirectoryDepth { get; set; }
        public Dictionary<string, int> FileTypeDistribution { get; set; } = new();

        // File Collections
        public List<string> LuaFiles { get; set; } = new();
        public List<string> JsonFiles { get; set; } = new();
        public List<string> ConfigFiles { get; set; } = new();
        public List<string> DocumentationFiles { get; set; } = new();

        // Compatibility Information
        public ModCompatibilityInfo CompatibilityInfo { get; set; } = new();

        // Framework Detection
        public List<string> DetectedFrameworks { get; set; } = new();

        // Complexity Analysis
        public int ComplexityScore { get; set; }
        public string ComplexityLevel { get; set; } = "Unknown";

        // Integrity Validation
        public List<string> IntegrityIssues { get; set; } = new();

        // Detailed Counts
        public bool HasContentFolder { get; set; }
        public int ContentPakCount { get; set; }
        public int UE4SSModCount { get; set; }
        public int PalSchemaModCount { get; set; }
        public int PakFileCount { get; set; }
        public long TotalPakSize { get; set; }
        public bool HasVanillaPakFiles { get; set; }
    }

    public class ModCompatibilityInfo
    {
        public string RequiredGameVersion { get; set; } = "";
        public bool RequiresUE4SS { get; set; }
        public string RequiredUE4SSVersion { get; set; } = "";
        public bool RequiresPalSchema { get; set; }
        public string RequiredPalSchemaVersion { get; set; } = "";
        public List<string> ConflictingMods { get; set; } = new();
        public List<string> RecommendedMods { get; set; } = new();
        public CompatibilityLevel Compatibility { get; set; } = CompatibilityLevel.Unknown;
    }

    public enum CompatibilityLevel
    {
        Unknown,
        FullyCompatible,
        MostlyCompatible,
        PartiallyCompatible,
        Incompatible,
        RequiresPatching
    }

    // ── ADVANCED CONFLICT DETECTION ──

    public class ConflictDetectionResult
    {
        public string ModName { get; set; } = "";
        public DateTime AnalysisDate { get; set; } = DateTime.Now;
        public List<AdvancedModConflict> Conflicts { get; set; } = new();
        public InstallationConflictSeverity OverallSeverity { get; set; } = InstallationConflictSeverity.Low;
        public bool IsInstallationSafe { get; set; } = true;
        public bool RequiresUserIntervention { get; set; } = false;
        public List<string> Recommendations { get; set; } = new();
    }

    public class AdvancedModConflict
    {
        public InstallationConflictType Type { get; set; }
        public string Description { get; set; } = "";
        public string AffectedFile { get; set; } = "";
        public InstallationConflictSeverity Severity { get; set; } = InstallationConflictSeverity.Low;
        public List<string> ResolutionOptions { get; set; } = new();
        public string RecommendedResolution { get; set; } = "";
    }

    public class UE4SSModInfo
    {
        public string Name { get; set; } = "";
        public string Path { get; set; } = "";
        public bool HasLuaScripts { get; set; }
        public bool HasEnabledFile { get; set; }
        public bool IsValid { get; set; }
        public string Version { get; set; } = "";
        public string Author { get; set; } = "";
        public string Description { get; set; } = "";
        public List<string> LuaFiles { get; set; } = new();
        public Dictionary<string, object> Settings { get; set; } = new();
    }

    public class PalSchemaModInfo
    {
        public string Name { get; set; } = "";
        public string Path { get; set; } = "";
        public bool HasBlueprints { get; set; }
        public bool HasItems { get; set; }
        public bool HasRaw { get; set; }
        public bool IsValid { get; set; }
        public string Version { get; set; } = "";
        public string Author { get; set; } = "";
        public int BlueprintCount { get; set; }
        public int ItemCount { get; set; }
        public int RawFileCount { get; set; }
    }

    public class PakFileInfo
    {
        public string Name { get; set; } = "";
        public string Path { get; set; } = "";
        public long Size { get; set; }
        public bool IsVanilla { get; set; }
        public string Hash { get; set; } = "";
        public DateTime CreationDate { get; set; }
        public bool IsCompressed { get; set; }
    }

    // ── COMPATIBILITY AND CONFLICT MODELS ──

    public class CompatibilityResult
    {
        public bool IsCompatible { get; set; } = true;
        public string Reason { get; set; } = "";
        public List<string> Issues { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public bool RequiresUE4SS { get; set; }
        public bool RequiresUE4SSRepair { get; set; }
        public bool RequiresPalSchema { get; set; }
        public string MinimumUE4SSVersion { get; set; } = "";
        public string MinimumPalSchemaVersion { get; set; } = "";
        public List<string> MissingDependencies { get; set; } = new();
        public List<string> ConflictingMods { get; set; } = new();
    }

    public class InstallationConflict
    {
        public string FilePath { get; set; } = "";
        public InstallationConflictType Type { get; set; }
        public DateTime ExistingFileDate { get; set; }
        public DateTime NewFileDate { get; set; }
        public long ExistingFileSize { get; set; }
        public long NewFileSize { get; set; }
        public ConflictResolution Resolution { get; set; } = ConflictResolution.Ask;
        public string Description { get; set; } = "";
        public InstallationConflictSeverity Severity { get; set; } = InstallationConflictSeverity.Medium;
        public bool CanAutoResolve { get; set; }
        public string RecommendedAction { get; set; } = "";
    }

    // ── UNINSTALLATION MODELS ──

    public class UninstallationOptions
    {
        public bool CreateBackupBeforeUninstall { get; set; } = true;
        public bool RemoveEmptyDirectories { get; set; } = true;
        public bool CleanupOrphanedFiles { get; set; } = false;
        public bool ValidateBeforeRemoval { get; set; } = true;
        public List<string> KeepPatterns { get; set; } = new();
    }

    public class UninstallationResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = "";
        public List<string> RemovedFiles { get; set; } = new();
        public List<string> FailedToRemove { get; set; } = new();
        public string? BackupPath { get; set; }
        public TimeSpan Duration { get; set; }
    }

    public class RollbackResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = "";
        public List<string> RestoredFiles { get; set; } = new();
        public List<string> FailedToRestore { get; set; } = new();
        public TimeSpan Duration { get; set; }
    }

    // ── PROGRESS REPORTING MODELS ──

    public class InstallationProgress
    {
        public string OperationId { get; set; } = "";
        public InstallationStatus Status { get; set; } = InstallationStatus.Pending;
        public string CurrentOperation { get; set; } = "";
        public string CurrentFile { get; set; } = "";
        public int ProcessedFiles { get; set; }
        public int TotalFiles { get; set; }
        public long ProcessedBytes { get; set; }
        public long TotalBytes { get; set; }
        public DateTime StartTime { get; set; } = DateTime.Now;
        public DateTime LastUpdateTime { get; set; } = DateTime.Now;
        public TimeSpan ElapsedTime => DateTime.Now - StartTime;
        public double FileProgressPercent => TotalFiles > 0 ? (double)ProcessedFiles / TotalFiles * 100 : 0;
        public double ByteProgressPercent => TotalBytes > 0 ? (double)ProcessedBytes / TotalBytes * 100 : 0;
        public double FilesPerSecond => ElapsedTime.TotalSeconds > 0 ? ProcessedFiles / ElapsedTime.TotalSeconds : 0;
        public double BytesPerSecond => ElapsedTime.TotalSeconds > 0 ? ProcessedBytes / ElapsedTime.TotalSeconds : 0;
        public TimeSpan EstimatedTimeRemaining
        {
            get
            {
                if (FilesPerSecond <= 0 || ProcessedFiles >= TotalFiles)
                    return TimeSpan.Zero;

                var remainingFiles = TotalFiles - ProcessedFiles;
                var secondsRemaining = remainingFiles / FilesPerSecond;
                return TimeSpan.FromSeconds(Math.Max(0, secondsRemaining));
            }
        }
        public string FormattedSpeed => FormatBytesPerSecond(BytesPerSecond);
        public string FormattedETA => EstimatedTimeRemaining.TotalSeconds > 0 ?
            $"{EstimatedTimeRemaining:mm\\:ss}" : "Calculating...";

        private static string FormatBytesPerSecond(double bytesPerSecond)
        {
            if (bytesPerSecond < 1024) return $"{bytesPerSecond:F1} B/s";
            if (bytesPerSecond < 1024 * 1024) return $"{bytesPerSecond / 1024:F1} KB/s";
            if (bytesPerSecond < 1024 * 1024 * 1024) return $"{bytesPerSecond / (1024 * 1024):F1} MB/s";
            return $"{bytesPerSecond / (1024 * 1024 * 1024):F1} GB/s";
        }
    }

    public class BatchInstallationProgress
    {
        public int CurrentModIndex { get; set; }
        public int TotalMods { get; set; }
        public string CurrentModName { get; set; } = "";
        public InstallationProgress? CurrentModProgress { get; set; }
        public List<InstallationResult> CompletedInstallations { get; set; } = new();
        public DateTime StartTime { get; set; } = DateTime.Now;
        public TimeSpan ElapsedTime => DateTime.Now - StartTime;
        public double OverallProgressPercent => TotalMods > 0 ? (double)CurrentModIndex / TotalMods * 100 : 0;
        public double ModsPerSecond => ElapsedTime.TotalSeconds > 0 ? CurrentModIndex / ElapsedTime.TotalSeconds : 0;
        public TimeSpan EstimatedTimeRemaining
        {
            get
            {
                if (ModsPerSecond <= 0 || CurrentModIndex >= TotalMods)
                    return TimeSpan.Zero;

                var remainingMods = TotalMods - CurrentModIndex;
                var secondsRemaining = remainingMods / ModsPerSecond;
                return TimeSpan.FromSeconds(Math.Max(0, secondsRemaining));
            }
        }
        public string FormattedETA => EstimatedTimeRemaining.TotalSeconds > 0 ?
            $"{EstimatedTimeRemaining:mm\\:ss}" : "Calculating...";
    }

    // ── ENUMERATIONS ──

    public enum InstallationStatus
    {
        Pending,
        Extracting,
        Analyzing,
        ValidatingCompatibility,
        CheckingConflicts,
        AwaitingConflictResolution,
        CreatingBackup,
        Installing,
        PostProcessing,
        Completed,
        Failed,
        Cancelled
    }

    public enum ModType
    {
        Unknown,
        TraditionalMod,     // Contains Pal folder structure
        UE4SSMod,          // UE4SS Lua mods
        PalSchemaMod,      // PalSchema configuration mods
        PakMod,            // Loose PAK files
        HybridMod          // Combination of the above
    }

    public enum InstallationConflictType
    {
        FileExists,
        FileNewer,
        FileOlder,
        FileSizeDifferent,
        ModNameConflict,
        CoreModConflict,
        DependencyConflict,
        VersionConflict,
        FileOverwrite,
        SystemFileConflict,
        MissingDependency,
        FrameworkConflict,
        PakFileConflict
    }

    public enum InstallationConflictSeverity
    {
        None,      // No conflicts
        Low,       // Can be auto-resolved
        Medium,    // User input recommended
        High,      // User input required
        Critical   // Should not proceed
    }

    // ── ARCHIVE EXTRACTION INTERFACES ──

    public interface IArchiveExtractor
    {
        bool CanExtract(string filePath);
        Task ExtractAsync(string archivePath, string extractPath);
        Task<List<string>> ListContentsAsync(string archivePath);
        bool ValidateArchive(string archivePath);
    }

    // ── INSTALLATION RECORDS ──

    public class InstallationRecord
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string ModName { get; set; } = "";
        public string ArchiveName { get; set; } = "";
        public string ArchivePath { get; set; } = "";
        public DateTime InstallDate { get; set; } = DateTime.Now;
        public ModType ModType { get; set; }
        public string Version { get; set; } = "";
        public string Author { get; set; } = "";
        public List<string> InstalledFiles { get; set; } = new();
        public List<string> UE4SSMods { get; set; } = new();
        public List<string> PalSchemaMods { get; set; } = new();
        public List<string> PakFiles { get; set; } = new();
        public string? BackupPath { get; set; }
        public bool CanUninstall { get; set; } = true;
        public bool CanRollback { get; set; } = false;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public class InstallationModDependency
    {
        public string Name { get; set; } = "";
        public string Version { get; set; } = "";
        public bool IsRequired { get; set; } = true;
        public string Url { get; set; } = "";
        public string Description { get; set; } = "";
    }

    public class InstallationModConflict
    {
        public string Name { get; set; } = "";
        public string Reason { get; set; } = "";
        public InstallationConflictSeverity Severity { get; set; } = InstallationConflictSeverity.Medium;
        public string Resolution { get; set; } = "";
    }
}