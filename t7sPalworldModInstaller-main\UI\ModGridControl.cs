using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;
using ModInstallerApp.Models;

namespace ModInstallerApp.UI
{
    /// <summary>
    /// Modern tile-based mod display control with drag-and-drop support
    /// </summary>
    public class ModGridControl : Panel
    {
        private readonly List<ModGridItem> _gridItems = new();
        private readonly List<ModItem> _mods = new();
        private ModGridSettings _settings = new();
        private ModGridItem? _selectedItem;
        private ModGridItem? _hoveredItem;
        private ModGridItem? _draggingItem;
        private Point _lastMousePosition;
        private bool _isDragging;

        public event EventHandler<ModItem>? ModSelected;
        public event EventHandler<ModItem>? ModDoubleClicked;
        public event EventHandler<ModItem>? ModStateChanged;
        public event EventHandler<(ModItem source, ModItem target)>? ModReordered;

        public ModGridSettings Settings
        {
            get => _settings;
            set
            {
                _settings = value;
                RecalculateLayout();
                Invalidate();
            }
        }

        public List<ModItem> Mods
        {
            get => _mods;
            set
            {
                _mods.Clear();
                _mods.AddRange(value);
                RefreshGrid();
            }
        }

        public ModItem? SelectedMod => _selectedItem?.Mod;

        public ModGridControl()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | 
                     ControlStyles.DoubleBuffer | ControlStyles.ResizeRedraw, true);
            
            BackColor = Theme.Surface;
            AutoScroll = true;
            Padding = new Padding(10);
            
            MouseClick += OnMouseClick;
            MouseDoubleClick += OnMouseDoubleClick;
            MouseDown += OnMouseDown;
            MouseMove += OnMouseMove;
            MouseUp += OnMouseUp;
            MouseLeave += OnMouseLeave;
        }

        public void RefreshGrid()
        {
            _gridItems.Clear();
            
            foreach (var mod in _mods)
            {
                _gridItems.Add(new ModGridItem { Mod = mod });
            }
            
            RecalculateLayout();
            Invalidate();
        }

        public void SelectMod(string modId)
        {
            var item = _gridItems.FirstOrDefault(i => i.Mod.Id == modId);
            if (item != null)
            {
                SelectItem(item);
            }
        }

        private void RecalculateLayout()
        {
            if (_gridItems.Count == 0) return;

            var clientArea = ClientRectangle;
            clientArea.Inflate(-Padding.Left, -Padding.Top);
            
            int tileSize = _settings.TileSize;
            int spacing = _settings.TileSpacing;
            int columnsPerRow = _settings.ColumnsPerRow;
            
            if (columnsPerRow <= 0)
            {
                columnsPerRow = Math.Max(1, (clientArea.Width + spacing) / (tileSize + spacing));
            }

            int x = Padding.Left;
            int y = Padding.Top;
            int column = 0;

            foreach (var item in _gridItems)
            {
                item.Bounds = new Rectangle(x, y, tileSize, tileSize);
                
                column++;
                if (column >= columnsPerRow)
                {
                    column = 0;
                    x = Padding.Left;
                    y += tileSize + spacing;
                }
                else
                {
                    x += tileSize + spacing;
                }
            }

            // Update scroll size
            int totalHeight = y + tileSize + Padding.Bottom;
            AutoScrollMinSize = new Size(0, totalHeight);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            
            e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
            e.Graphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;

            var clipRect = e.ClipRectangle;
            clipRect.Offset(AutoScrollPosition);

            foreach (var item in _gridItems)
            {
                if (item.Bounds.IntersectsWith(clipRect))
                {
                    DrawModTile(e.Graphics, item);
                }
            }
        }

        private void DrawModTile(Graphics g, ModGridItem item)
        {
            var bounds = item.Bounds;
            bounds.Offset(AutoScrollPosition);
            
            var mod = item.Mod;
            
            // Determine tile colors based on state
            Color backgroundColor = GetTileBackgroundColor(mod, item);
            Color borderColor = GetTileBorderColor(mod, item);
            
            // Draw tile background
            using var backgroundBrush = new SolidBrush(backgroundColor);
            using var borderPen = new Pen(borderColor, item.IsSelected ? 3 : 1);
            
            var tileBounds = new Rectangle(bounds.X + 2, bounds.Y + 2, bounds.Width - 4, bounds.Height - 4);
            g.FillRoundedRectangle(backgroundBrush, tileBounds, 8);
            g.DrawRoundedRectangle(borderPen, tileBounds, 8);

            // Draw thumbnail or placeholder
            var thumbnailBounds = new Rectangle(tileBounds.X + 8, tileBounds.Y + 8, tileBounds.Width - 16, 80);
            DrawThumbnail(g, mod, thumbnailBounds);

            // Draw mod name
            var nameBounds = new Rectangle(tileBounds.X + 8, thumbnailBounds.Bottom + 4, tileBounds.Width - 16, 20);
            using var nameFont = new Font(Theme.BodyFont.FontFamily, 9, FontStyle.Bold);
            TextRenderer.DrawText(g, mod.DisplayName.Length > 0 ? mod.DisplayName : mod.Name, nameFont,
                nameBounds, Theme.TextLight, TextFormatFlags.EndEllipsis | TextFormatFlags.HorizontalCenter);

            // Draw version and author
            var infoBounds = new Rectangle(tileBounds.X + 8, nameBounds.Bottom + 2, tileBounds.Width - 16, 14);
            var infoText = $"v{mod.Version} by {mod.Author}";
            TextRenderer.DrawText(g, infoText, Theme.SmallFont,
                infoBounds, Color.LightGray, TextFormatFlags.EndEllipsis | TextFormatFlags.HorizontalCenter);

            // Draw status indicator
            var statusBounds = new Rectangle(tileBounds.X + 8, infoBounds.Bottom + 4, tileBounds.Width - 16, 16);
            DrawStatusIndicator(g, mod, statusBounds);

            // Draw enabled/disabled overlay
            if (!mod.IsEnabled)
            {
                using var overlayBrush = new SolidBrush(Color.FromArgb(128, Color.Black));
                g.FillRoundedRectangle(overlayBrush, tileBounds, 8);
                
                var disabledText = "DISABLED";
                using var disabledFont = new Font(Theme.BodyFont.FontFamily, 10, FontStyle.Bold);
                var textSize = TextRenderer.MeasureText(disabledText, disabledFont);
                var textPos = new Point(
                    tileBounds.X + (tileBounds.Width - textSize.Width) / 2,
                    tileBounds.Y + (tileBounds.Height - textSize.Height) / 2
                );
                TextRenderer.DrawText(g, disabledText, disabledFont, textPos, Color.White);
            }

            // Draw load order if enabled
            if (_settings.ShowLoadOrder && mod.LoadOrder > 0)
            {
                var orderBounds = new Rectangle(tileBounds.Right - 24, tileBounds.Y + 4, 20, 20);
                using var orderBrush = new SolidBrush(Theme.Accent);
                g.FillEllipse(orderBrush, orderBounds);
                
                var orderText = mod.LoadOrder.ToString();
                using var orderFont = new Font(Theme.SmallFont.FontFamily, 8, FontStyle.Bold);
                TextRenderer.DrawText(g, orderText, orderFont, orderBounds, Theme.TextDark,
                    TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
            }
        }

        private void DrawThumbnail(Graphics g, ModItem mod, Rectangle bounds)
        {
            if (_settings.ShowThumbnails && mod.Thumbnail != null)
            {
                g.DrawImage(mod.Thumbnail, bounds);
            }
            else
            {
                // Draw placeholder
                using var placeholderBrush = new SolidBrush(Theme.Track);
                g.FillRectangle(placeholderBrush, bounds);
                
                // Draw category icon or text
                var categoryText = mod.Category.ToString();
                using var categoryFont = new Font(Theme.SmallFont.FontFamily, 8);
                TextRenderer.DrawText(g, categoryText, categoryFont, bounds, Theme.TextLight,
                    TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
            }
        }

        private void DrawStatusIndicator(Graphics g, ModItem mod, Rectangle bounds)
        {
            Color statusColor = GetStatusColor(mod);
            string statusText = mod.StatusText;
            
            // Draw status dot
            var dotBounds = new Rectangle(bounds.X, bounds.Y + (bounds.Height - 8) / 2, 8, 8);
            using var statusBrush = new SolidBrush(statusColor);
            g.FillEllipse(statusBrush, dotBounds);
            
            // Draw status text
            var textBounds = new Rectangle(bounds.X + 12, bounds.Y, bounds.Width - 12, bounds.Height);
            TextRenderer.DrawText(g, statusText, Theme.SmallFont, textBounds, Theme.TextLight,
                TextFormatFlags.VerticalCenter | TextFormatFlags.Left);
        }

        private Color GetTileBackgroundColor(ModItem mod, ModGridItem item)
        {
            if (item.IsSelected) return Theme.Accent.ChangeAlpha(50);
            if (item.IsHovered) return Theme.Track.Lighten(20);
            if (mod.HasConflicts) return Theme.Error.ChangeAlpha(30);
            return Theme.Track;
        }

        private Color GetTileBorderColor(ModItem mod, ModGridItem item)
        {
            if (item.IsSelected) return Theme.Accent;
            if (mod.HasConflicts) return Theme.Error;
            if (item.IsHovered) return Theme.Accent2;
            return Color.Transparent;
        }

        private Color GetStatusColor(ModItem mod)
        {
            return mod.State switch
            {
                ModState.Installed => mod.IsEnabled ? Theme.Success : Color.Gray,
                ModState.Disabled => Color.Gray,
                ModState.Conflicted => Theme.Error,
                ModState.MissingDependencies => Theme.Warning,
                ModState.UpdateAvailable => Theme.Accent2,
                ModState.Corrupted => Theme.Error,
                ModState.Loading => Theme.Warning,
                ModState.Error => Theme.Error,
                _ => Theme.TextLight
            };
        }

        // ── MOUSE HANDLING ──

        private void OnMouseClick(object? sender, MouseEventArgs e)
        {
            var item = GetItemAtPoint(e.Location);
            if (item != null)
            {
                SelectItem(item);
                ModSelected?.Invoke(this, item.Mod);

                // Right-click to toggle mod state
                if (e.Button == MouseButtons.Right)
                {
                    ModStateChanged?.Invoke(this, item.Mod);
                }
            }
            else
            {
                ClearSelection();
            }
        }

        private void OnMouseDoubleClick(object? sender, MouseEventArgs e)
        {
            var item = GetItemAtPoint(e.Location);
            if (item != null)
            {
                ModDoubleClicked?.Invoke(this, item.Mod);
            }
        }

        private void OnMouseDown(object? sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                var item = GetItemAtPoint(e.Location);
                if (item != null)
                {
                    _draggingItem = item;
                    _lastMousePosition = e.Location;
                    item.DragOffset = new Point(
                        e.Location.X - item.Bounds.X,
                        e.Location.Y - item.Bounds.Y
                    );
                }
            }
        }

        private void OnMouseMove(object? sender, MouseEventArgs e)
        {
            var item = GetItemAtPoint(e.Location);

            // Update hover state
            if (_hoveredItem != item)
            {
                if (_hoveredItem != null)
                {
                    _hoveredItem.IsHovered = false;
                    InvalidateItem(_hoveredItem);
                }

                _hoveredItem = item;

                if (_hoveredItem != null)
                {
                    _hoveredItem.IsHovered = true;
                    InvalidateItem(_hoveredItem);
                }
            }

            // Handle dragging
            if (_draggingItem != null && e.Button == MouseButtons.Left)
            {
                if (!_isDragging)
                {
                    var distance = Math.Sqrt(
                        Math.Pow(e.Location.X - _lastMousePosition.X, 2) +
                        Math.Pow(e.Location.Y - _lastMousePosition.Y, 2)
                    );

                    if (distance > 5) // Start drag threshold
                    {
                        _isDragging = true;
                        _draggingItem.IsDragging = true;
                        Cursor = Cursors.Hand;
                    }
                }

                if (_isDragging)
                {
                    // Update drag position and check for reorder
                    var targetItem = GetItemAtPoint(e.Location);
                    if (targetItem != null && targetItem != _draggingItem)
                    {
                        // Trigger reorder event
                        ModReordered?.Invoke(this, (_draggingItem.Mod, targetItem.Mod));
                    }

                    Invalidate();
                }
            }
        }

        private void OnMouseUp(object? sender, MouseEventArgs e)
        {
            if (_isDragging && _draggingItem != null)
            {
                _isDragging = false;
                _draggingItem.IsDragging = false;
                Cursor = Cursors.Default;
                Invalidate();
            }

            _draggingItem = null;
        }

        private void OnMouseLeave(object? sender, EventArgs e)
        {
            if (_hoveredItem != null)
            {
                _hoveredItem.IsHovered = false;
                InvalidateItem(_hoveredItem);
                _hoveredItem = null;
            }
        }

        private ModGridItem? GetItemAtPoint(Point point)
        {
            point.Offset(-AutoScrollPosition.X, -AutoScrollPosition.Y);
            return _gridItems.FirstOrDefault(item => item.Bounds.Contains(point));
        }

        private void SelectItem(ModGridItem item)
        {
            if (_selectedItem != null)
            {
                _selectedItem.IsSelected = false;
                InvalidateItem(_selectedItem);
            }

            _selectedItem = item;
            item.IsSelected = true;
            InvalidateItem(item);
        }

        private void ClearSelection()
        {
            if (_selectedItem != null)
            {
                _selectedItem.IsSelected = false;
                InvalidateItem(_selectedItem);
                _selectedItem = null;
            }
        }

        private void InvalidateItem(ModGridItem item)
        {
            var bounds = item.Bounds;
            bounds.Offset(AutoScrollPosition);
            bounds.Inflate(5, 5); // Add some padding for border effects
            Invalidate(bounds);
        }

        protected override void OnResize(EventArgs eventargs)
        {
            base.OnResize(eventargs);
            RecalculateLayout();
        }
    }
}
