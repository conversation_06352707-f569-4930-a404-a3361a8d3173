using System;
using System.Collections.Generic;
using System.Drawing;

namespace ModInstallerApp.Models
{
    public class ApplicationSettings
    {
        public string? LastPalworldPath { get; set; }
        public List<PalworldInstallation> RecentInstallations { get; set; } = new();
        public string? LastBackupLocation { get; set; }
        public WindowState WindowState { get; set; } = new();
        public UserPreferences Preferences { get; set; } = new();
        public DateTime LastStartup { get; set; } = DateTime.Now;
        public int StartupCount { get; set; } = 0;
        public bool IsFirstRun { get; set; } = true;
        public string AppVersion { get; set; } = "1.4.0";
        public BackupSettings BackupSettings { get; set; } = new();
    }

    public class PalworldInstallation
    {
        public string Path { get; set; } = "";
        public string DisplayName { get; set; } = "";
        public DateTime LastAccessed { get; set; } = DateTime.Now;
        public bool IsValid { get; set; } = true;
        public string InstallationType { get; set; } = "Unknown"; // Steam, Epic, Manual
        public long GameVersion { get; set; } = 0;
        public int ModCount { get; set; } = 0;
        public UE4SSStatus UE4SSStatus { get; set; } = new();
        public PalSchemaStatus PalSchemaStatus { get; set; } = new();
    }

    public class WindowState
    {
        public Point Location { get; set; } = new Point(100, 100);
        public Size Size { get; set; } = new Size(740, 560);
        public bool IsMaximized { get; set; } = false;
    }

    public class UserPreferences
    {
        public bool AutoDetectInstallations { get; set; } = true;
        public bool RememberWindowPosition { get; set; } = true;
        public bool ShowWelcomeOnStartup { get; set; } = true;
        public int BackupRetentionDays { get; set; } = 30;
        public bool EnableCaching { get; set; } = true;
        public string PreferredTheme { get; set; } = "Dark";
        public List<string> RecentSearches { get; set; } = new();
    }
}