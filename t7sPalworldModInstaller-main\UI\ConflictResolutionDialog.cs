using System;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using ModInstallerApp.Models;

namespace ModInstallerApp.UI
{
    public class ConflictResolutionDialog : Form
    {
        public BackupConflictResolution Resolution { get; private set; } = BackupConflictResolution.Cancel;

        private readonly UE4SSStatus _backupUE4SS;
        private readonly UE4SSStatus _currentUE4SS;
        private readonly PalSchemaStatus _backupPalSchema;
        private readonly PalSchemaStatus _currentPalSchema;
        private readonly BackupMetadata _metadata;

        public ConflictResolutionDialog(BackupMetadata metadata, UE4SSStatus currentUE4SS, PalSchemaStatus currentPalSchema)
        {
            _metadata = metadata;
            _backupUE4SS = metadata.UE4SSStatus;
            _currentUE4SS = currentUE4SS;
            _backupPalSchema = metadata.PalSchemaStatus;
            _currentPalSchema = currentPalSchema;

            InitializeDialog();
        }

        private void InitializeDialog()
        {
            Text = "Restore Conflicts Detected";
            Size = new Size(600, 500);
            StartPosition = FormStartPosition.CenterParent;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;

            Theme.Apply(this);

            var layout = new TableLayoutPanel { Dock = DockStyle.Fill, RowCount = 5, ColumnCount = 1 };
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // Title
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 60)); // Conflicts list
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // Resolution options title
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 40)); // Resolution options
            layout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // Buttons

            // Title
            var lblTitle = new Label
            {
                Text = "The game state has changed since this backup was created:",
                AutoSize = true,
                Font = new Font(Theme.BodyFont.FontFamily, 12, FontStyle.Bold),
                Padding = new Padding(10)
            };

            // Conflicts list
            var conflictsBox = new RichTextBox
            {
                Dock = DockStyle.Fill,
                ReadOnly = true,
                BackColor = Theme.Track,
                ForeColor = Theme.TextLight,
                Font = new Font("Consolas", 9),
                BorderStyle = BorderStyle.FixedSingle
            };

            var conflicts = BuildConflictsList();
            conflictsBox.Text = conflicts;

            // Resolution options title
            var lblResolution = new Label
            {
                Text = "How would you like to proceed?",
                AutoSize = true,
                Font = new Font(Theme.BodyFont.FontFamily, 11, FontStyle.Bold),
                Padding = new Padding(10, 10, 10, 5)
            };

            // Resolution options
            var optionsPanel = new Panel { Dock = DockStyle.Fill };
            var rbBackupFirst = new RadioButton
            {
                Text = "Create backup of current state first, then restore (Recommended)",
                AutoSize = true,
                Checked = true,
                Location = new Point(15, 10),
                ForeColor = Theme.Success
            };

            var rbSmartMerge = new RadioButton
            {
                Text = "Smart merge: Restore only user mods, preserve UE4SS/PalSchema core",
                AutoSize = true,
                Location = new Point(15, 40),
                ForeColor = Theme.Accent2
            };

            var rbForceRestore = new RadioButton
            {
                Text = "Force complete restore (may break current UE4SS/PalSchema setup)",
                AutoSize = true,
                Location = new Point(15, 70),
                ForeColor = Theme.Warning
            };

            var rbCancel = new RadioButton
            {
                Text = "Cancel restore operation",
                AutoSize = true,
                Location = new Point(15, 100),
                ForeColor = Theme.Error
            };

            optionsPanel.Controls.AddRange(new Control[] { rbBackupFirst, rbSmartMerge, rbForceRestore, rbCancel });

            // Buttons
            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft,
                AutoSize = true,
                Padding = new Padding(10)
            };

            var btnOK = new ThemedButton { Text = "Proceed" };
            var btnCancel = new ThemedButton { Text = "Cancel" };

            btnOK.Click += (s, e) =>
            {
                if (rbBackupFirst.Checked)
                    Resolution = BackupConflictResolution.BackupFirst;
                else if (rbSmartMerge.Checked)
                    Resolution = BackupConflictResolution.SmartMerge;
                else if (rbForceRestore.Checked)
                    Resolution = BackupConflictResolution.ForceRestore;
                else
                    Resolution = BackupConflictResolution.Cancel;

                DialogResult = DialogResult.OK;
            };

            btnCancel.Click += (s, e) =>
            {
                Resolution = BackupConflictResolution.Cancel;
                DialogResult = DialogResult.Cancel;
            };

            buttonPanel.Controls.AddRange(new Control[] { btnCancel, btnOK });

            // Add all to layout
            layout.Controls.Add(lblTitle, 0, 0);
            layout.Controls.Add(conflictsBox, 0, 1);
            layout.Controls.Add(lblResolution, 0, 2);
            layout.Controls.Add(optionsPanel, 0, 3);
            layout.Controls.Add(buttonPanel, 0, 4);

            Controls.Add(layout);
        }

        private string BuildConflictsList()
        {
            var sb = new StringBuilder();
            sb.AppendLine($"Backup Date: {_metadata.BackupDate:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"Backup Reason: {_metadata.BackupReason}");
            sb.AppendLine();

            // UE4SS conflicts
            if (_backupUE4SS.Status != _currentUE4SS.Status)
            {
                sb.AppendLine("🔧 UE4SS Status Changed:");
                sb.AppendLine($"   Backup time: {_backupUE4SS.Status}");
                sb.AppendLine($"   Current:     {_currentUE4SS.Status}");
                sb.AppendLine();
            }

            if (_backupUE4SS.CoreModsPresent != _currentUE4SS.CoreModsPresent)
            {
                sb.AppendLine("📦 UE4SS Core Mods Changed:");
                sb.AppendLine($"   Backup time: {_backupUE4SS.CoreModsPresent}/{_backupUE4SS.CoreModsExpected}");
                sb.AppendLine($"   Current:     {_currentUE4SS.CoreModsPresent}/{_currentUE4SS.CoreModsExpected}");
                sb.AppendLine();
            }

            if (_backupUE4SS.UserMods.Count != _currentUE4SS.UserMods.Count)
            {
                sb.AppendLine("🎮 User Mods Changed:");
                sb.AppendLine($"   Backup time: {_backupUE4SS.UserMods.Count} mods");
                sb.AppendLine($"   Current:     {_currentUE4SS.UserMods.Count} mods");

                var addedMods = _currentUE4SS.UserMods.Except(_backupUE4SS.UserMods).ToList();
                var removedMods = _backupUE4SS.UserMods.Except(_currentUE4SS.UserMods).ToList();

                if (addedMods.Count > 0)
                    sb.AppendLine($"   Added: {string.Join(", ", addedMods)}");
                if (removedMods.Count > 0)
                    sb.AppendLine($"   Removed: {string.Join(", ", removedMods)}");
                sb.AppendLine();
            }

            // PalSchema conflicts
            if (_backupPalSchema.IsInstalled != _currentPalSchema.IsInstalled)
            {
                sb.AppendLine("🔮 PalSchema Status Changed:");
                sb.AppendLine($"   Backup time: {(_backupPalSchema.IsInstalled ? "Installed" : "Not Installed")}");
                sb.AppendLine($"   Current:     {(_currentPalSchema.IsInstalled ? "Installed" : "Not Installed")}");
                sb.AppendLine();
            }

            if (_backupPalSchema.Mods.Count != _currentPalSchema.Mods.Count)
            {
                sb.AppendLine("✨ PalSchema Mods Changed:");
                sb.AppendLine($"   Backup time: {_backupPalSchema.Mods.Count} mods");
                sb.AppendLine($"   Current:     {_currentPalSchema.Mods.Count} mods");
                sb.AppendLine();
            }

            if (sb.Length == 0)
            {
                sb.AppendLine("No significant conflicts detected.");
            }

            return sb.ToString();
        }
    }
}