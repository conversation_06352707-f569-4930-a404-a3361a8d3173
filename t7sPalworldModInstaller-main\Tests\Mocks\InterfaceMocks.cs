using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ModInstallerApp.Interfaces;
using ModInstallerApp.Models;

namespace ModInstallerApp.Tests.Mocks
{
    /// <summary>
    /// Mock implementation of ICacheManager for testing
    /// </summary>
    public class MockCacheManager : ICacheManager
    {
        private readonly ConcurrentDictionary<string, object> _cache = new();
        private readonly ConcurrentDictionary<string, DateTime> _expiry = new();
        private long _hits = 0;
        private long _misses = 0;
        private bool _disposed = false;

        public T? Get<T>(string key)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(MockCacheManager));
            
            if (string.IsNullOrWhiteSpace(key))
            {
                System.Threading.Interlocked.Increment(ref _misses);
                return default(T);
            }

            // Check expiry
            if (_expiry.TryGetValue(key, out var expiry) && DateTime.Now > expiry)
            {
                Remove(key);
                System.Threading.Interlocked.Increment(ref _misses);
                return default(T);
            }

            if (_cache.TryGetValue(key, out var value) && value is T typedValue)
            {
                System.Threading.Interlocked.Increment(ref _hits);
                return typedValue;
            }

            System.Threading.Interlocked.Increment(ref _misses);
            return default(T);
        }

        public void Set<T>(string key, T value, TimeSpan expiration)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(MockCacheManager));
            
            if (string.IsNullOrWhiteSpace(key)) return;
            
            _cache.AddOrUpdate(key, value!, (k, v) => value!);
            _expiry.AddOrUpdate(key, DateTime.Now.Add(expiration), (k, v) => DateTime.Now.Add(expiration));
        }

        public void Remove(string key)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(MockCacheManager));
            
            _cache.TryRemove(key, out _);
            _expiry.TryRemove(key, out _);
        }

        public void InvalidateCache(string key)
        {
            Remove(key);
        }

        public void ClearAll()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(MockCacheManager));
            
            _cache.Clear();
            _expiry.Clear();
            _hits = 0;
            _misses = 0;
        }

        public void ClearExpired()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(MockCacheManager));
            
            var now = DateTime.Now;
            var expiredKeys = new List<string>();
            
            foreach (var kvp in _expiry)
            {
                if (kvp.Value <= now)
                {
                    expiredKeys.Add(kvp.Key);
                }
            }
            
            foreach (var key in expiredKeys)
            {
                Remove(key);
            }
        }

        public int Count => _cache.Count;

        public double HitRatio
        {
            get
            {
                var total = _hits + _misses;
                return total == 0 ? 0.0 : (double)_hits / total;
            }
        }

        public void Dispose()
        {
            _disposed = true;
            _cache.Clear();
            _expiry.Clear();
        }
        
        // Additional test helper methods
        public bool ContainsKey(string key)
        {
            return _cache.ContainsKey(key);
        }
        
        public void ResetStatistics()
        {
            _hits = 0;
            _misses = 0;
        }
    }

    /// <summary>
    /// Mock implementation of IEnhancedLogger for testing
    /// </summary>
    public class MockEnhancedLogger : IEnhancedLogger
    {
        private readonly List<MockLogEntry> _logEntries = new();
        private bool _disposed = false;

        public Interfaces.LogLevel MinimumLogLevel { get; set; } = Interfaces.LogLevel.Verbose;

        public void LogInfo(string message)
        {
            if (_disposed) return;
            if (MinimumLogLevel <= Interfaces.LogLevel.Info)
                _logEntries.Add(new MockLogEntry { Level = Interfaces.LogLevel.Info, Message = message });
        }

        public void LogWarning(string message)
        {
            if (_disposed) return;
            if (MinimumLogLevel <= Interfaces.LogLevel.Warning)
                _logEntries.Add(new MockLogEntry { Level = Interfaces.LogLevel.Warning, Message = message });
        }

        public void LogError(string message)
        {
            if (_disposed) return;
            if (MinimumLogLevel <= Interfaces.LogLevel.Error)
                _logEntries.Add(new MockLogEntry { Level = Interfaces.LogLevel.Error, Message = message });
        }

        public void LogError(string message, Exception exception)
        {
            if (_disposed) return;
            if (MinimumLogLevel <= Interfaces.LogLevel.Error)
                _logEntries.Add(new MockLogEntry { Level = Interfaces.LogLevel.Error, Message = message, Exception = exception });
        }

        public void LogDebug(string message)
        {
            if (_disposed) return;
            if (MinimumLogLevel <= Interfaces.LogLevel.Debug)
                _logEntries.Add(new MockLogEntry { Level = Interfaces.LogLevel.Debug, Message = message });
        }

        public void LogVerbose(string message)
        {
            if (_disposed) return;
            if (MinimumLogLevel <= Interfaces.LogLevel.Verbose)
                _logEntries.Add(new MockLogEntry { Level = Interfaces.LogLevel.Verbose, Message = message });
        }

        public void LogSuccess(string message)
        {
            if (_disposed) return;
            if (MinimumLogLevel <= Interfaces.LogLevel.Info)
                _logEntries.Add(new MockLogEntry { Level = Interfaces.LogLevel.Info, Message = $"SUCCESS: {message}" });
        }

        public void LogCritical(string message)
        {
            if (_disposed) return;
            if (MinimumLogLevel <= Interfaces.LogLevel.Critical)
                _logEntries.Add(new MockLogEntry { Level = Interfaces.LogLevel.Critical, Message = message });
        }

        public void LogCritical(string message, Exception exception)
        {
            if (_disposed) return;
            if (MinimumLogLevel <= Interfaces.LogLevel.Critical)
                _logEntries.Add(new MockLogEntry { Level = Interfaces.LogLevel.Critical, Message = message, Exception = exception });
        }

        public void Flush()
        {
            // No-op for mock
        }

        public void Dispose()
        {
            _disposed = true;
            _logEntries.Clear();
        }

        // Test helper methods
        public IReadOnlyList<MockLogEntry> GetLogEntries() => _logEntries.AsReadOnly();
        
        public void ClearLogs() => _logEntries.Clear();
        
        public bool HasLogEntry(Interfaces.LogLevel level, string messageContains)
        {
            return _logEntries.Any(e => e.Level == level && e.Message.Contains(messageContains));
        }
    }

    /// <summary>
    /// Mock implementation of IUE4SSDetector for testing
    /// </summary>
    public class MockUE4SSDetector : IUE4SSDetector
    {
        private UE4SSStatus _detectionResult;
        private bool _disposed = false;

        public string PalworldRoot { get; }

        public MockUE4SSDetector(string palworldRoot)
        {
            PalworldRoot = palworldRoot;
            _detectionResult = new UE4SSStatus
            {
                Status = UE4SSInstallStatus.FullyInstalled,
                LastChecked = DateTime.UtcNow,
                CoreModsPresent = 9,
                CoreModsExpected = 9,
                HasProxyDll = true,
                HasUE4SSFolder = true,
                HasUE4SSDll = true,
                HasSettingsIni = true,
                HasLicense = true,
                HasModsJson = true,
                HasModsTxt = true
            };
        }

        public Task<UE4SSStatus> DetectUE4SSAsync()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(MockUE4SSDetector));
            return Task.FromResult(_detectionResult);
        }

        public void InvalidateCache()
        {
            // No-op for mock
        }

        // Test helper methods
        public void SetDetectionResult(UE4SSStatus result)
        {
            _detectionResult = result;
        }

        public void SetStatus(UE4SSInstallStatus status)
        {
            _detectionResult.Status = status;
            _detectionResult.LastChecked = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Log entry for testing logger behavior
    /// </summary>
    public class MockLogEntry
    {
        public Interfaces.LogLevel Level { get; set; }
        public string Message { get; set; } = string.Empty;
        public Exception? Exception { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
}
