using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    public class MaintenanceEngine
    {
        private readonly string _palRoot;
        private readonly UE4SSDetector _detector;
        private readonly Action<string> _logger;

        private static readonly Dictionary<string, string> ExpectedCoreFileHashes = new()
        {
            // These would be actual hashes of known good UE4SS files
            ["dwmapi.dll"] = "placeholder_hash_1",
            ["UE4SS.dll"] = "placeholder_hash_2",
            ["UE4SS-settings.ini"] = "placeholder_hash_3"
        };

        public MaintenanceEngine(string palRoot, UE4SSDetector detector, Action<string> logger)
        {
            _palRoot = palRoot;
            _detector = detector;
            _logger = logger;
        }

        public MaintenanceReport PerformFullMaintenance()
        {
            _logger("Starting full maintenance scan...");
            
            var report = new MaintenanceReport
            {
                ScanDate = DateTime.Now,
                PalRootPath = _palRoot
            };

            // Check UE4SS integrity
            report.UE4SSIntegrity = CheckUE4SSIntegrity();
            
            // Check PalSchema integrity
            report.PalSchemaIntegrity = CheckPalSchemaIntegrity();
            
            // Find orphaned files
            report.OrphanedFiles = FindOrphanedFiles();
            
            // Check for corrupted mod files
            report.CorruptedMods = FindCorruptedMods();
            
            // Analyze disk usage
            report.DiskUsage = AnalyzeDiskUsage();
            
            // Generate recommendations
            report.Recommendations = GenerateRecommendations(report);

            _logger($"Maintenance scan completed. Found {report.Issues.Count} issues.");
            return report;
        }

        public IntegrityCheckResult CheckUE4SSIntegrity()
        {
            var result = new IntegrityCheckResult { ComponentName = "UE4SS" };
            var status = _detector.DetectUE4SS();

            if (status.Status == UE4SSInstallStatus.NotInstalled)
            {
                result.OverallStatus = IntegrityStatus.NotInstalled;
                result.Issues.Add("UE4SS is not installed");
                return result;
            }

            // Check core files
            var binariesPath = Path.Combine(_palRoot, "Pal", "Binaries", "Win64");
            var ue4ssPath = Path.Combine(binariesPath, "ue4ss");

            CheckFile(result, Path.Combine(binariesPath, "dwmapi.dll"), "UE4SS Proxy DLL", true);
            CheckFile(result, Path.Combine(ue4ssPath, "UE4SS.dll"), "UE4SS Core Library", true);
            CheckFile(result, Path.Combine(ue4ssPath, "UE4SS-settings.ini"), "UE4SS Configuration", true);
            CheckFile(result, Path.Combine(ue4ssPath, "LICENSE"), "UE4SS License", false);

            // Check mods folder structure
            var modsPath = Path.Combine(ue4ssPath, "Mods");
            if (!Directory.Exists(modsPath))
            {
                result.Issues.Add("UE4SS Mods folder is missing");
                result.OverallStatus = IntegrityStatus.Corrupted;
            }
            else
            {
                CheckFile(result, Path.Combine(modsPath, "mods.json"), "Mods Configuration JSON", true);
                CheckFile(result, Path.Combine(modsPath, "mods.txt"), "Mods Configuration TXT", true);

                // Check core mods
                var missingCoreMods = new List<string>();
                foreach (var coreMod in UE4SSDetector.RequiredCoreMods)
                {
                    var modPath = Path.Combine(modsPath, coreMod);
                    if (!Directory.Exists(modPath))
                    {
                        missingCoreMods.Add(coreMod);
                    }
                }

                if (missingCoreMods.Count > 0)
                {
                    result.Issues.Add($"Missing core mods: {string.Join(", ", missingCoreMods)}");
                    result.OverallStatus = IntegrityStatus.Incomplete;
                }
            }

            // Determine overall status
            if (result.OverallStatus == IntegrityStatus.Unknown)
            {
                result.OverallStatus = result.Issues.Count == 0 ? IntegrityStatus.Good : IntegrityStatus.MinorIssues;
            }

            return result;
        }

        public IntegrityCheckResult CheckPalSchemaIntegrity()
        {
            var result = new IntegrityCheckResult { ComponentName = "PalSchema" };
            var status = _detector.DetectPalSchema();

            if (!status.IsInstalled)
            {
                result.OverallStatus = IntegrityStatus.NotInstalled;
                result.Issues.Add("PalSchema is not installed");
                return result;
            }

            var palSchemaPath = status.FolderPath;
            
            // Check core PalSchema files
            CheckFile(result, Path.Combine(palSchemaPath, "enabled.txt"), "PalSchema Enabled Flag", false);
            
            var modsPath = Path.Combine(palSchemaPath, "mods");
            if (!Directory.Exists(modsPath))
            {
                result.Issues.Add("PalSchema mods folder is missing");
                result.OverallStatus = IntegrityStatus.Corrupted;
            }
            else
            {
                // Check individual PalSchema mods
                foreach (var mod in status.Mods)
                {
                    if (!mod.IsValid)
                    {
                        result.Issues.Add($"Invalid PalSchema mod: {mod.Name}");
                        result.OverallStatus = IntegrityStatus.MinorIssues;
                    }
                    
                    // Check for empty mod folders
                    if (!mod.HasBlueprints && !mod.HasItems && !mod.HasRaw)
                    {
                        result.Issues.Add($"Empty PalSchema mod folder: {mod.Name}");
                        result.OverallStatus = IntegrityStatus.MinorIssues;
                    }
                }
            }

            if (result.OverallStatus == IntegrityStatus.Unknown)
            {
                result.OverallStatus = result.Issues.Count == 0 ? IntegrityStatus.Good : IntegrityStatus.MinorIssues;
            }

            return result;
        }

        public List<OrphanedFile> FindOrphanedFiles()
        {
            var orphans = new List<OrphanedFile>();
            var ue4ssStatus = _detector.DetectUE4SS();

            if (ue4ssStatus.Status == UE4SSInstallStatus.NotInstalled)
                return orphans;

            var modsPath = Path.Combine(_palRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods");
            if (!Directory.Exists(modsPath))
                return orphans;

            // Find folders that aren't valid mods
            foreach (var directory in Directory.GetDirectories(modsPath))
            {
                var modName = Path.GetFileName(directory);
                
                // Skip known core mods and user mods
                if (ue4ssStatus.CoreMods.Contains(modName) || ue4ssStatus.UserMods.Contains(modName))
                    continue;

                // Check if it's a valid mod folder
                if (IsValidModFolder(directory))
                    continue;

                orphans.Add(new OrphanedFile
                {
                    Path = directory,
                    Type = OrphanedFileType.InvalidModFolder,
                    Size = GetDirectorySize(directory),
                    LastModified = Directory.GetLastWriteTime(directory),
                    Reason = "Folder exists in Mods directory but doesn't contain valid mod files"
                });
            }

            // Find loose files in mods directory
            foreach (var file in Directory.GetFiles(modsPath))
            {
                var fileName = Path.GetFileName(file);
                if (fileName != "mods.json" && fileName != "mods.txt")
                {
                    orphans.Add(new OrphanedFile
                    {
                        Path = file,
                        Type = OrphanedFileType.LooseFile,
                        Size = new FileInfo(file).Length,
                        LastModified = File.GetLastWriteTime(file),
                        Reason = "Unexpected file in Mods directory"
                    });
                }
            }

            return orphans;
        }

        public List<CorruptedMod> FindCorruptedMods()
        {
            var corrupted = new List<CorruptedMod>();
            var ue4ssStatus = _detector.DetectUE4SS();

            if (ue4ssStatus.Status == UE4SSInstallStatus.NotInstalled)
                return corrupted;

            var modsPath = Path.Combine(_palRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods");
            if (!Directory.Exists(modsPath))
                return corrupted;

            // Check all user mods for corruption
            foreach (var mod in ue4ssStatus.UserMods)
            {
                var modPath = Path.Combine(modsPath, mod);
                if (!Directory.Exists(modPath))
                    continue;

                var issues = new List<string>();
                
                // Check for required mod files
                var hasScripts = Directory.GetFiles(modPath, "*.lua", SearchOption.AllDirectories).Length > 0;
                var hasEnabled = File.Exists(Path.Combine(modPath, "enabled.txt"));
                
                if (!hasScripts && !hasEnabled)
                {
                    issues.Add("No Lua scripts or enabled.txt found");
                }

                // Check for inaccessible files
                try
                {
                    foreach (var file in Directory.GetFiles(modPath, "*", SearchOption.AllDirectories))
                    {
                        using var stream = File.OpenRead(file);
                        // Try to read first byte to check accessibility
                        stream.ReadByte();
                    }
                }
                catch (Exception ex)
                {
                    issues.Add($"File access error: {ex.Message}");
                }

                if (issues.Count > 0)
                {
                    corrupted.Add(new CorruptedMod
                    {
                        Name = mod,
                        Path = modPath,
                        Issues = issues,
                        Size = GetDirectorySize(modPath),
                        LastModified = Directory.GetLastWriteTime(modPath)
                    });
                }
            }

            return corrupted;
        }

        public DiskUsageInfo AnalyzeDiskUsage()
        {
            var info = new DiskUsageInfo();
            
            var ue4ssPath = Path.Combine(_palRoot, "Pal", "Binaries", "Win64", "ue4ss");
            if (Directory.Exists(ue4ssPath))
            {
                info.UE4SSSize = GetDirectorySize(ue4ssPath);
            }

            var contentModsPath = Path.Combine(_palRoot, "Pal", "Content", "Paks");
            if (Directory.Exists(contentModsPath))
            {
                var pakFiles = Directory.GetFiles(contentModsPath, "*.pak");
                info.ContentModsSize = pakFiles.Where(f => !Path.GetFileName(f).Equals("Pal-Windows.pak", StringComparison.OrdinalIgnoreCase))
                                                .Sum(f => new FileInfo(f).Length);
            }

            var palSchemaStatus = _detector.DetectPalSchema();
            if (palSchemaStatus.IsInstalled)
            {
                info.PalSchemaSize = GetDirectorySize(palSchemaStatus.FolderPath);
            }

            info.TotalModsSize = info.UE4SSSize + info.ContentModsSize + info.PalSchemaSize;
            
            return info;
        }

        public List<MaintenanceRecommendation> GenerateRecommendations(MaintenanceReport report)
        {
            var recommendations = new List<MaintenanceRecommendation>();

            // UE4SS recommendations
            if (report.UE4SSIntegrity.OverallStatus == IntegrityStatus.Corrupted)
            {
                recommendations.Add(new MaintenanceRecommendation
                {
                    Priority = RecommendationPriority.High,
                    Category = "UE4SS",
                    Title = "Reinstall UE4SS",
                    Description = "UE4SS installation is corrupted and should be reinstalled",
                    Action = "Download and install the latest UE4SS version"
                });
            }
            else if (report.UE4SSIntegrity.OverallStatus == IntegrityStatus.Incomplete)
            {
                recommendations.Add(new MaintenanceRecommendation
                {
                    Priority = RecommendationPriority.Medium,
                    Category = "UE4SS",
                    Title = "Repair UE4SS Installation",
                    Description = "Some UE4SS components are missing",
                    Action = "Reinstall missing UE4SS components"
                });
            }

            // Orphaned files recommendations
            if (report.OrphanedFiles.Count > 0)
            {
                var totalOrphanSize = report.OrphanedFiles.Sum(o => o.Size);
                recommendations.Add(new MaintenanceRecommendation
                {
                    Priority = RecommendationPriority.Low,
                    Category = "Cleanup",
                    Title = "Remove Orphaned Files",
                    Description = $"Found {report.OrphanedFiles.Count} orphaned files ({FormatFileSize(totalOrphanSize)})",
                    Action = "Clean up orphaned mod files to free disk space"
                });
            }

            // Corrupted mods recommendations
            if (report.CorruptedMods.Count > 0)
            {
                recommendations.Add(new MaintenanceRecommendation
                {
                    Priority = RecommendationPriority.Medium,
                    Category = "Mods",
                    Title = "Fix Corrupted Mods",
                    Description = $"Found {report.CorruptedMods.Count} mods with issues",
                    Action = "Review and repair or remove corrupted mod installations"
                });
            }

            // Disk usage recommendations
            if (report.DiskUsage.TotalModsSize > 1024 * 1024 * 1024) // > 1GB
            {
                recommendations.Add(new MaintenanceRecommendation
                {
                    Priority = RecommendationPriority.Low,
                    Category = "Storage",
                    Title = "Consider Mod Cleanup",
                    Description = $"Mods are using {FormatFileSize(report.DiskUsage.TotalModsSize)} of disk space",
                    Action = "Review installed mods and remove unused ones"
                });
            }

            return recommendations.OrderByDescending(r => r.Priority).ToList();
        }

        public void CleanupOrphanedFiles(List<OrphanedFile> orphans, bool dryRun = true)
        {
            foreach (var orphan in orphans)
            {
                try
                {
                    if (dryRun)
                    {
                        _logger($"Would delete: {orphan.Path}");
                    }
                    else
                    {
                        if (Directory.Exists(orphan.Path))
                        {
                            Directory.Delete(orphan.Path, true);
                            _logger($"Deleted orphaned directory: {orphan.Path}");
                        }
                        else if (File.Exists(orphan.Path))
                        {
                            File.Delete(orphan.Path);
                            _logger($"Deleted orphaned file: {orphan.Path}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger($"Failed to delete {orphan.Path}: {ex.Message}");
                }
            }
        }

        private void CheckFile(IntegrityCheckResult result, string filePath, string description, bool required)
        {
            if (!File.Exists(filePath))
            {
                if (required)
                {
                    result.Issues.Add($"Missing required file: {description}");
                    result.OverallStatus = IntegrityStatus.Corrupted;
                }
                else
                {
                    result.Issues.Add($"Missing optional file: {description}");
                    if (result.OverallStatus == IntegrityStatus.Unknown)
                        result.OverallStatus = IntegrityStatus.MinorIssues;
                }
            }
            else
            {
                // Check file accessibility
                try
                {
                    using var stream = File.OpenRead(filePath);
                    stream.ReadByte();
                }
                catch (Exception ex)
                {
                    result.Issues.Add($"Cannot access {description}: {ex.Message}");
                    result.OverallStatus = IntegrityStatus.Corrupted;
                }
            }
        }

        private bool IsValidModFolder(string folderPath)
        {
            // A valid mod folder should contain Lua scripts or an enabled.txt
            var hasLuaFiles = Directory.GetFiles(folderPath, "*.lua", SearchOption.AllDirectories).Length > 0;
            var hasEnabledFile = File.Exists(Path.Combine(folderPath, "enabled.txt"));
            
            return hasLuaFiles || hasEnabledFile;
        }

        private long GetDirectorySize(string path)
        {
            try
            {
                return Directory.GetFiles(path, "*", SearchOption.AllDirectories)
                                .Sum(f => new FileInfo(f).Length);
            }
            catch
            {
                return 0;
            }
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len /= 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }

    // Supporting models for maintenance system
    public class MaintenanceReport
    {
        public DateTime ScanDate { get; set; }
        public string PalRootPath { get; set; } = "";
        public IntegrityCheckResult UE4SSIntegrity { get; set; } = new();
        public IntegrityCheckResult PalSchemaIntegrity { get; set; } = new();
        public List<OrphanedFile> OrphanedFiles { get; set; } = new();
        public List<CorruptedMod> CorruptedMods { get; set; } = new();
        public DiskUsageInfo DiskUsage { get; set; } = new();
        public List<MaintenanceRecommendation> Recommendations { get; set; } = new();
        
        public List<string> Issues => UE4SSIntegrity.Issues.Concat(PalSchemaIntegrity.Issues).ToList();
        public bool HasCriticalIssues => UE4SSIntegrity.OverallStatus == IntegrityStatus.Corrupted || 
                                         PalSchemaIntegrity.OverallStatus == IntegrityStatus.Corrupted;
    }

    public class IntegrityCheckResult
    {
        public string ComponentName { get; set; } = "";
        public IntegrityStatus OverallStatus { get; set; } = IntegrityStatus.Unknown;
        public List<string> Issues { get; set; } = new();
    }

    public class OrphanedFile
    {
        public string Path { get; set; } = "";
        public OrphanedFileType Type { get; set; }
        public long Size { get; set; }
        public DateTime LastModified { get; set; }
        public string Reason { get; set; } = "";
    }

    public class CorruptedMod
    {
        public string Name { get; set; } = "";
        public string Path { get; set; } = "";
        public List<string> Issues { get; set; } = new();
        public long Size { get; set; }
        public DateTime LastModified { get; set; }
    }

    public class DiskUsageInfo
    {
        public long UE4SSSize { get; set; }
        public long ContentModsSize { get; set; }
        public long PalSchemaSize { get; set; }
        public long TotalModsSize { get; set; }
    }

    public class MaintenanceRecommendation
    {
        public RecommendationPriority Priority { get; set; }
        public string Category { get; set; } = "";
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Action { get; set; } = "";
    }

    public enum IntegrityStatus
    {
        Unknown,
        Good,
        MinorIssues,
        Incomplete,
        Corrupted,
        NotInstalled
    }

    public enum OrphanedFileType
    {
        LooseFile,
        InvalidModFolder,
        BackupFile,
        TempFile
    }

    public enum RecommendationPriority
    {
        Low,
        Medium,
        High,
        Critical
    }
}