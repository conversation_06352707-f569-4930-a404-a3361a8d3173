name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: windows-latest
    
    strategy:
      matrix:
        dotnet-version: ['8.0.x']
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ matrix.dotnet-version }}
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Build
      run: dotnet build --no-restore --configuration Release
    
    - name: Run Unit Tests
      run: dotnet test --no-build --configuration Release --logger trx --results-directory TestResults --collect:"XPlat Code Coverage"
    
    - name: Upload Test Results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-${{ matrix.dotnet-version }}
        path: TestResults
    
    - name: Upload Code Coverage
      uses: codecov/codecov-action@v3
      if: always()
      with:
        files: TestResults/*/coverage.cobertura.xml
        fail_ci_if_error: false

  integration-test:
    runs-on: windows-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Build
      run: dotnet build --no-restore --configuration Release
    
    - name: Run Integration Tests
      run: dotnet test --no-build --configuration Release --filter "Category=Integration" --logger trx --results-directory IntegrationTestResults
    
    - name: Upload Integration Test Results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: integration-test-results
        path: IntegrationTestResults

  performance-test:
    runs-on: windows-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Build
      run: dotnet build --no-restore --configuration Release
    
    - name: Run Performance Tests
      run: dotnet test --no-build --configuration Release --filter "Category=Performance" --logger trx --results-directory PerformanceTestResults
    
    - name: Upload Performance Test Results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: performance-test-results
        path: PerformanceTestResults

  security-scan:
    runs-on: windows-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Run Security Scan
      run: |
        dotnet list package --vulnerable --include-transitive
        dotnet list package --deprecated --include-transitive

  build-release:
    runs-on: windows-latest
    needs: [test, integration-test]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Build Release
      run: dotnet build --no-restore --configuration Release
    
    - name: Publish
      run: dotnet publish --no-build --configuration Release --output ./publish
    
    - name: Upload Release Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: release-build
        path: ./publish
