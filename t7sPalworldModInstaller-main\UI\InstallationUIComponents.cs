using System;
using System.Drawing;
using System.Windows.Forms;
using ModInstallerApp.Models;

namespace ModInstallerApp.UI
{
    // This class provides UI components for the installation process
    public static class InstallationUIHelpers
    {
        // Utility methods for installation UI

        /// <summary>
        /// Creates a progress panel for installation operations
        /// </summary>
        public static Panel CreateProgressPanel()
        {
            var panel = new Panel { Dock = DockStyle.Fill };
            var progressBar = new ProgressBar { Dock = DockStyle.Top, Height = 24 };
            var label = new Label { Dock = DockStyle.Top, Text = "Installation Progress", Height = 20 };

            panel.Controls.Add(progressBar);
            panel.Controls.Add(label);

            return panel;
        }

        /// <summary>
        /// Creates an enhanced progress panel with detailed progress information
        /// </summary>
        public static EnhancedProgressPanel CreateEnhancedProgressPanel()
        {
            return new EnhancedProgressPanel();
        }
    }

    /// <summary>
    /// Enhanced progress panel with detailed installation progress display
    /// </summary>
    public class EnhancedProgressPanel : Panel
    {
        private readonly ProgressBar _fileProgressBar;
        private readonly ProgressBar _byteProgressBar;
        private readonly Label _statusLabel;
        private readonly Label _currentFileLabel;
        private readonly Label _progressStatsLabel;
        private readonly Label _speedLabel;
        private readonly Label _etaLabel;

        public EnhancedProgressPanel()
        {
            Height = 120;
            Dock = DockStyle.Top;
            BackColor = Color.FromArgb(45, 45, 48);
            Padding = new Padding(10);

            // Status label
            _statusLabel = new Label
            {
                Text = "Ready",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                AutoSize = true,
                Location = new Point(10, 10)
            };

            // Current file label
            _currentFileLabel = new Label
            {
                Text = "",
                ForeColor = Color.LightGray,
                Font = new Font("Segoe UI", 8),
                AutoSize = false,
                Size = new Size(400, 16),
                Location = new Point(10, 30),
                AutoEllipsis = true
            };

            // File progress bar
            _fileProgressBar = new ProgressBar
            {
                Size = new Size(400, 20),
                Location = new Point(10, 50),
                Style = ProgressBarStyle.Continuous
            };

            // Byte progress bar
            _byteProgressBar = new ProgressBar
            {
                Size = new Size(400, 20),
                Location = new Point(10, 75),
                Style = ProgressBarStyle.Continuous
            };

            // Progress stats label
            _progressStatsLabel = new Label
            {
                Text = "0 / 0 files (0 / 0 bytes)",
                ForeColor = Color.LightGray,
                Font = new Font("Segoe UI", 8),
                AutoSize = true,
                Location = new Point(420, 50)
            };

            // Speed label
            _speedLabel = new Label
            {
                Text = "0 B/s",
                ForeColor = Color.LightGray,
                Font = new Font("Segoe UI", 8),
                AutoSize = true,
                Location = new Point(420, 65)
            };

            // ETA label
            _etaLabel = new Label
            {
                Text = "ETA: --:--",
                ForeColor = Color.LightGray,
                Font = new Font("Segoe UI", 8),
                AutoSize = true,
                Location = new Point(420, 80)
            };

            Controls.AddRange(new Control[] {
                _statusLabel, _currentFileLabel, _fileProgressBar, _byteProgressBar,
                _progressStatsLabel, _speedLabel, _etaLabel
            });
        }

        public void UpdateProgress(InstallationProgress progress)
        {
            if (InvokeRequired)
            {
                Invoke(() => UpdateProgress(progress));
                return;
            }

            _statusLabel.Text = progress.CurrentOperation;
            _currentFileLabel.Text = string.IsNullOrEmpty(progress.CurrentFile) ?
                "Preparing..." : $"Processing: {progress.CurrentFile}";

            // Update file progress
            _fileProgressBar.Maximum = Math.Max(1, progress.TotalFiles);
            _fileProgressBar.Value = Math.Min(progress.ProcessedFiles, _fileProgressBar.Maximum);

            // Update byte progress
            if (progress.TotalBytes > 0)
            {
                _byteProgressBar.Maximum = 100;
                _byteProgressBar.Value = Math.Min(100, (int)progress.ByteProgressPercent);
            }

            // Update stats
            _progressStatsLabel.Text = $"{progress.ProcessedFiles} / {progress.TotalFiles} files " +
                                     $"({FormatBytes(progress.ProcessedBytes)} / {FormatBytes(progress.TotalBytes)})";

            _speedLabel.Text = progress.FormattedSpeed;
            _etaLabel.Text = $"ETA: {progress.FormattedETA}";
        }

        private static string FormatBytes(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024.0 * 1024):F1} MB";
            return $"{bytes / (1024.0 * 1024 * 1024):F1} GB";
        }

        /// <summary>
        /// Creates a status label for installation feedback
        /// </summary>
        public static Label CreateStatusLabel()
        {
            return new Label
            {
                Dock = DockStyle.Bottom,
                Height = 24,
                TextAlign = ContentAlignment.MiddleLeft,
                Text = "Ready to install"
            };
        }

        /// <summary>
        /// Formats a file size for display
        /// </summary>
        public static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len /= 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}