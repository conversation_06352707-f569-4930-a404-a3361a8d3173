using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using ModInstallerApp.Models;
using ModInstallerApp.Services;

namespace ModInstallerApp.UI
{
    /// <summary>
    /// Dialog for managing mod collection sharing, export, and import
    /// </summary>
    public partial class ModCollectionSharingDialog : Form
    {
        private readonly ModCollectionService _collectionService;
        private readonly CommunityRatingService _ratingService;
        private readonly EnhancedLogger _logger;

        // UI Controls
        private TabControl tabControl = null!;
        private TabPage exportTab = null!;
        private TabPage importTab = null!;
        private TabPage manageTab = null!;

        // Export Tab Controls
        private ComboBox collectionComboBox = null!;
        private TextBox exportPathTextBox = null!;
        private Button browseExportButton = null!;
        private CheckBox includeModFilesCheckBox = null!;
        private CheckBox includeConfigurationsCheckBox = null!;
        private CheckBox includeProfilesCheckBox = null!;
        private Button exportButton = null!;
        private ProgressBar exportProgressBar = null!;
        private Label exportStatusLabel = null!;

        // Import Tab Controls
        private TextBox importPathTextBox = null!;
        private Button browseImportButton = null!;
        private CheckBox installModsCheckBox = null!;
        private CheckBox applyProfilesCheckBox = null!;
        private Button importButton = null!;
        private ProgressBar importProgressBar = null!;
        private Label importStatusLabel = null!;
        private RichTextBox importPreviewTextBox = null!;

        // Manage Tab Controls
        private ListView collectionsListView = null!;
        private Button deleteCollectionButton = null!;
        private Button viewCollectionButton = null!;
        private Button rateCollectionButton = null!;

        public ModCollectionSharingDialog(ModCollectionService collectionService, 
            CommunityRatingService ratingService, EnhancedLogger logger)
        {
            _collectionService = collectionService ?? throw new ArgumentNullException(nameof(collectionService));
            _ratingService = ratingService ?? throw new ArgumentNullException(nameof(ratingService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeComponent();
            LoadCollections();
        }

        private void InitializeComponent()
        {
            Text = "Mod Collection Sharing";
            Size = new Size(800, 600);
            StartPosition = FormStartPosition.CenterParent;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;

            // Create tab control
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 9F)
            };

            // Create tabs
            CreateExportTab();
            CreateImportTab();
            CreateManageTab();

            Controls.Add(tabControl);

            // Create bottom panel with buttons
            var bottomPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom,
                BackColor = SystemColors.Control
            };

            var closeButton = new Button
            {
                Text = "Close",
                Size = new Size(80, 30),
                Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
                Location = new Point(bottomPanel.Width - 90, 10),
                DialogResult = DialogResult.Cancel
            };
            closeButton.Click += (s, e) => Close();

            bottomPanel.Controls.Add(closeButton);
            Controls.Add(bottomPanel);

            CancelButton = closeButton;
        }

        private void CreateExportTab()
        {
            exportTab = new TabPage("Export Collection");
            tabControl.TabPages.Add(exportTab);

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 8,
                Padding = new Padding(10)
            };

            // Collection selection
            panel.Controls.Add(new Label { Text = "Collection:", Anchor = AnchorStyles.Left }, 0, 0);
            collectionComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                Width = 300
            };
            panel.Controls.Add(collectionComboBox, 1, 0);

            // Export path
            panel.Controls.Add(new Label { Text = "Export Path:", Anchor = AnchorStyles.Left }, 0, 1);
            var pathPanel = new Panel { Height = 25, Anchor = AnchorStyles.Left | AnchorStyles.Right };
            exportPathTextBox = new TextBox
            {
                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                Width = pathPanel.Width - 80
            };
            browseExportButton = new Button
            {
                Text = "Browse...",
                Size = new Size(75, 23),
                Anchor = AnchorStyles.Right
            };
            browseExportButton.Click += BrowseExportPath_Click;
            pathPanel.Controls.AddRange(new Control[] { exportPathTextBox, browseExportButton });
            panel.Controls.Add(pathPanel, 1, 1);

            // Options
            panel.Controls.Add(new Label { Text = "Include:", Anchor = AnchorStyles.Left }, 0, 2);
            var optionsPanel = new Panel { Height = 80, Anchor = AnchorStyles.Left | AnchorStyles.Right };
            includeModFilesCheckBox = new CheckBox
            {
                Text = "Mod Files (archives)",
                Checked = true,
                Location = new Point(0, 0)
            };
            includeConfigurationsCheckBox = new CheckBox
            {
                Text = "Configurations",
                Checked = true,
                Location = new Point(0, 25)
            };
            includeProfilesCheckBox = new CheckBox
            {
                Text = "Profiles",
                Checked = true,
                Location = new Point(0, 50)
            };
            optionsPanel.Controls.AddRange(new Control[] { 
                includeModFilesCheckBox, includeConfigurationsCheckBox, includeProfilesCheckBox 
            });
            panel.Controls.Add(optionsPanel, 1, 2);

            // Export button
            exportButton = new Button
            {
                Text = "Export Collection",
                Size = new Size(120, 30),
                Anchor = AnchorStyles.Left
            };
            exportButton.Click += ExportButton_Click;
            panel.Controls.Add(exportButton, 1, 3);

            // Progress
            exportProgressBar = new ProgressBar
            {
                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                Visible = false
            };
            panel.Controls.Add(exportProgressBar, 1, 4);

            // Status
            exportStatusLabel = new Label
            {
                Text = "",
                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                ForeColor = Color.Green
            };
            panel.Controls.Add(exportStatusLabel, 1, 5);

            exportTab.Controls.Add(panel);
        }

        private void CreateImportTab()
        {
            importTab = new TabPage("Import Collection");
            tabControl.TabPages.Add(importTab);

            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 8,
                Padding = new Padding(10)
            };

            // Import path
            panel.Controls.Add(new Label { Text = "Package File:", Anchor = AnchorStyles.Left }, 0, 0);
            var pathPanel = new Panel { Height = 25, Anchor = AnchorStyles.Left | AnchorStyles.Right };
            importPathTextBox = new TextBox
            {
                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                Width = pathPanel.Width - 80
            };
            importPathTextBox.TextChanged += ImportPath_TextChanged;
            browseImportButton = new Button
            {
                Text = "Browse...",
                Size = new Size(75, 23),
                Anchor = AnchorStyles.Right
            };
            browseImportButton.Click += BrowseImportPath_Click;
            pathPanel.Controls.AddRange(new Control[] { importPathTextBox, browseImportButton });
            panel.Controls.Add(pathPanel, 1, 0);

            // Options
            panel.Controls.Add(new Label { Text = "Actions:", Anchor = AnchorStyles.Left }, 0, 1);
            var optionsPanel = new Panel { Height = 50, Anchor = AnchorStyles.Left | AnchorStyles.Right };
            installModsCheckBox = new CheckBox
            {
                Text = "Install mods automatically",
                Location = new Point(0, 0)
            };
            applyProfilesCheckBox = new CheckBox
            {
                Text = "Apply included profiles",
                Location = new Point(0, 25)
            };
            optionsPanel.Controls.AddRange(new Control[] { installModsCheckBox, applyProfilesCheckBox });
            panel.Controls.Add(optionsPanel, 1, 1);

            // Preview
            panel.Controls.Add(new Label { Text = "Preview:", Anchor = AnchorStyles.Left }, 0, 2);
            importPreviewTextBox = new RichTextBox
            {
                Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom,
                Height = 200,
                ReadOnly = true,
                Font = new Font("Consolas", 9F)
            };
            panel.Controls.Add(importPreviewTextBox, 1, 2);

            // Import button
            importButton = new Button
            {
                Text = "Import Collection",
                Size = new Size(120, 30),
                Anchor = AnchorStyles.Left,
                Enabled = false
            };
            importButton.Click += ImportButton_Click;
            panel.Controls.Add(importButton, 1, 3);

            // Progress
            importProgressBar = new ProgressBar
            {
                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                Visible = false
            };
            panel.Controls.Add(importProgressBar, 1, 4);

            // Status
            importStatusLabel = new Label
            {
                Text = "",
                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                ForeColor = Color.Green
            };
            panel.Controls.Add(importStatusLabel, 1, 5);

            importTab.Controls.Add(panel);
        }

        private void CreateManageTab()
        {
            manageTab = new TabPage("Manage Collections");
            tabControl.TabPages.Add(manageTab);

            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // Collections list
            collectionsListView = new ListView
            {
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                Location = new Point(10, 10),
                Size = new Size(panel.Width - 20, panel.Height - 60),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
            };

            collectionsListView.Columns.AddRange(new[]
            {
                new ColumnHeader { Text = "Name", Width = 200 },
                new ColumnHeader { Text = "Author", Width = 120 },
                new ColumnHeader { Text = "Mods", Width = 80 },
                new ColumnHeader { Text = "Created", Width = 120 },
                new ColumnHeader { Text = "Rating", Width = 80 },
                new ColumnHeader { Text = "Size", Width = 100 }
            });

            collectionsListView.SelectedIndexChanged += CollectionsList_SelectedIndexChanged;

            // Buttons
            var buttonPanel = new Panel
            {
                Height = 40,
                Dock = DockStyle.Bottom
            };

            viewCollectionButton = new Button
            {
                Text = "View Details",
                Size = new Size(100, 30),
                Location = new Point(10, 5),
                Enabled = false
            };
            viewCollectionButton.Click += ViewCollection_Click;

            rateCollectionButton = new Button
            {
                Text = "Rate & Review",
                Size = new Size(100, 30),
                Location = new Point(120, 5),
                Enabled = false
            };
            rateCollectionButton.Click += RateCollection_Click;

            deleteCollectionButton = new Button
            {
                Text = "Delete",
                Size = new Size(80, 30),
                Location = new Point(230, 5),
                Enabled = false
            };
            deleteCollectionButton.Click += DeleteCollection_Click;

            buttonPanel.Controls.AddRange(new Control[] { 
                viewCollectionButton, rateCollectionButton, deleteCollectionButton 
            });

            panel.Controls.AddRange(new Control[] { collectionsListView, buttonPanel });
            manageTab.Controls.Add(panel);
        }

        private void LoadCollections()
        {
            try
            {
                var collections = _collectionService.GetCollections();
                
                // Load export combo box
                collectionComboBox.Items.Clear();
                foreach (var collection in collections)
                {
                    collectionComboBox.Items.Add(new CollectionItem(collection));
                }

                // Load manage list view
                collectionsListView.Items.Clear();
                foreach (var collection in collections)
                {
                    var rating = _ratingService.GetRating(collection.Id);
                    var item = new ListViewItem(new[]
                    {
                        collection.Name,
                        collection.Author,
                        collection.Mods.Count.ToString(),
                        collection.CreatedDate.ToString("yyyy-MM-dd"),
                        rating.AverageRating > 0 ? $"{rating.AverageRating:F1} ★" : "Not rated",
                        FormatFileSize(collection.TotalSize)
                    })
                    {
                        Tag = collection
                    };
                    collectionsListView.Items.Add(item);
                }

                // Set default export path
                if (string.IsNullOrEmpty(exportPathTextBox.Text))
                {
                    exportPathTextBox.Text = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "ModCollections");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to load collections", "UI", ex);
                MessageBox.Show($"Failed to load collections: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void ExportButton_Click(object? sender, EventArgs e)
        {
            if (collectionComboBox.SelectedItem is not CollectionItem selectedItem)
            {
                MessageBox.Show("Please select a collection to export.", "No Collection Selected", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(exportPathTextBox.Text))
            {
                MessageBox.Show("Please specify an export path.", "No Export Path", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                exportButton.Enabled = false;
                exportProgressBar.Visible = true;
                exportProgressBar.Style = ProgressBarStyle.Marquee;
                exportStatusLabel.Text = "Exporting collection...";

                Directory.CreateDirectory(exportPathTextBox.Text);

                var exportedPath = await _collectionService.ExportCollectionAsync(
                    selectedItem.Collection.Id,
                    exportPathTextBox.Text,
                    includeModFilesCheckBox.Checked,
                    includeConfigurationsCheckBox.Checked,
                    includeProfilesCheckBox.Checked);

                exportStatusLabel.Text = $"Collection exported successfully to: {exportedPath}";
                exportStatusLabel.ForeColor = Color.Green;

                MessageBox.Show($"Collection exported successfully!\n\nFile: {exportedPath}", 
                    "Export Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to export collection", "UI", ex);
                exportStatusLabel.Text = $"Export failed: {ex.Message}";
                exportStatusLabel.ForeColor = Color.Red;
                MessageBox.Show($"Failed to export collection: {ex.Message}", "Export Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                exportButton.Enabled = true;
                exportProgressBar.Visible = false;
            }
        }

        private async void ImportButton_Click(object? sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(importPathTextBox.Text) || !File.Exists(importPathTextBox.Text))
            {
                MessageBox.Show("Please select a valid package file to import.", "Invalid File", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                importButton.Enabled = false;
                importProgressBar.Visible = true;
                importProgressBar.Style = ProgressBarStyle.Marquee;
                importStatusLabel.Text = "Importing collection...";

                var result = await _collectionService.ImportCollectionAsync(
                    importPathTextBox.Text,
                    installModsCheckBox.Checked,
                    applyProfilesCheckBox.Checked);

                switch (result)
                {
                    case ImportResult.Success:
                        importStatusLabel.Text = "Collection imported successfully!";
                        importStatusLabel.ForeColor = Color.Green;
                        LoadCollections(); // Refresh the collections list
                        MessageBox.Show("Collection imported successfully!", "Import Complete", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        break;
                    case ImportResult.PartialSuccess:
                        importStatusLabel.Text = "Collection imported with some issues.";
                        importStatusLabel.ForeColor = Color.Orange;
                        LoadCollections();
                        MessageBox.Show("Collection imported with some issues. Check the logs for details.", 
                            "Import Partial Success", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        break;
                    case ImportResult.Failed:
                        importStatusLabel.Text = "Import failed.";
                        importStatusLabel.ForeColor = Color.Red;
                        MessageBox.Show("Failed to import collection. Check the logs for details.", 
                            "Import Failed", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to import collection", "UI", ex);
                importStatusLabel.Text = $"Import failed: {ex.Message}";
                importStatusLabel.ForeColor = Color.Red;
                MessageBox.Show($"Failed to import collection: {ex.Message}", "Import Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                importButton.Enabled = true;
                importProgressBar.Visible = false;
            }
        }

        private void BrowseExportPath_Click(object? sender, EventArgs e)
        {
            using var dialog = new FolderBrowserDialog
            {
                Description = "Select export folder",
                SelectedPath = exportPathTextBox.Text
            };

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                exportPathTextBox.Text = dialog.SelectedPath;
            }
        }

        private void BrowseImportPath_Click(object? sender, EventArgs e)
        {
            using var dialog = new OpenFileDialog
            {
                Title = "Select mod collection package",
                Filter = "Mod Collection Packages (*.pmcx)|*.pmcx|All Files (*.*)|*.*",
                InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            };

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                importPathTextBox.Text = dialog.FileName;
            }
        }

        private void ImportPath_TextChanged(object? sender, EventArgs e)
        {
            importButton.Enabled = !string.IsNullOrWhiteSpace(importPathTextBox.Text) && 
                                   File.Exists(importPathTextBox.Text);

            // TODO: Load and display package preview
            if (importButton.Enabled)
            {
                importPreviewTextBox.Text = "Package preview will be shown here...";
            }
            else
            {
                importPreviewTextBox.Text = "";
            }
        }

        private void CollectionsList_SelectedIndexChanged(object? sender, EventArgs e)
        {
            var hasSelection = collectionsListView.SelectedItems.Count > 0;
            viewCollectionButton.Enabled = hasSelection;
            rateCollectionButton.Enabled = hasSelection;
            deleteCollectionButton.Enabled = hasSelection;
        }

        private void ViewCollection_Click(object? sender, EventArgs e)
        {
            if (collectionsListView.SelectedItems.Count > 0)
            {
                var collection = collectionsListView.SelectedItems[0].Tag as ShareableModCollection;
                if (collection != null)
                {
                    // TODO: Show collection details dialog
                    MessageBox.Show($"Collection: {collection.Name}\nMods: {collection.Mods.Count}\nAuthor: {collection.Author}",
                        "Collection Details", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void RateCollection_Click(object? sender, EventArgs e)
        {
            if (collectionsListView.SelectedItems.Count > 0)
            {
                var collection = collectionsListView.SelectedItems[0].Tag as ShareableModCollection;
                if (collection != null)
                {
                    // TODO: Show rating dialog
                    MessageBox.Show($"Rating dialog for: {collection.Name}", "Rate Collection",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private async void DeleteCollection_Click(object? sender, EventArgs e)
        {
            if (collectionsListView.SelectedItems.Count > 0)
            {
                var collection = collectionsListView.SelectedItems[0].Tag as ShareableModCollection;
                if (collection != null)
                {
                    var result = MessageBox.Show($"Are you sure you want to delete the collection '{collection.Name}'?",
                        "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        try
                        {
                            await _collectionService.DeleteCollectionAsync(collection.Id);
                            LoadCollections();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("Failed to delete collection", "UI", ex);
                            MessageBox.Show($"Failed to delete collection: {ex.Message}", "Delete Error",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
        }

        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 B";
            
            string[] sizes = { "B", "KB", "MB", "GB" };
            int order = 0;
            double size = bytes;
            
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }
            
            return $"{size:0.##} {sizes[order]}";
        }

        private class CollectionItem
        {
            public ShareableModCollection Collection { get; }

            public CollectionItem(ShareableModCollection collection)
            {
                Collection = collection;
            }

            public override string ToString()
            {
                return $"{Collection.Name} ({Collection.Mods.Count} mods)";
            }
        }
    }
}
