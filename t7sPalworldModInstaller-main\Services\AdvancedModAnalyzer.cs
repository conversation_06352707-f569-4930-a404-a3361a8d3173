using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    public class AdvancedModAnalyzer
    {
        private readonly EnhancedLogger _logger;
        private readonly UE4SSDetector _detector;

        public AdvancedModAnalyzer(EnhancedLogger logger, UE4SSDetector detector)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _detector = detector ?? throw new ArgumentNullException(nameof(detector));
        }

        public async Task<AdvancedModStructure> AnalyzeModStructureAsync(string extractedPath, CancellationToken cancellationToken = default)
        {
            return await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                var structure = new AdvancedModStructure 
                { 
                    RootPath = extractedPath,
                    AnalysisDate = DateTime.Now
                };

                // Perform deep structure analysis
                AnalyzeDirectoryStructure(structure, extractedPath);
                AnalyzeFileTypes(structure, extractedPath);
                ExtractAdvancedMetadata(structure);
                AnalyzeDependencies(structure);
                AnalyzeCompatibility(structure);
                DetectModFrameworks(structure);
                AnalyzeModComplexity(structure);
                ValidateModIntegrity(structure);

                _logger.LogInfo($"Advanced mod analysis completed: {structure.ModType}, " +
                              $"Complexity: {structure.ComplexityScore}, " +
                              $"Dependencies: {structure.Dependencies.Count}", "AdvancedModAnalysis");

                return structure;
            }, cancellationToken);
        }

        private void AnalyzeDirectoryStructure(AdvancedModStructure structure, string rootPath)
        {
            var directories = Directory.GetDirectories(rootPath, "*", SearchOption.AllDirectories);
            var files = Directory.GetFiles(rootPath, "*", SearchOption.AllDirectories);

            structure.TotalDirectories = directories.Length;
            structure.TotalFiles = files.Length;
            structure.TotalSize = files.Sum(f => new FileInfo(f).Length);

            // Analyze directory patterns
            var palFolders = directories.Where(d => Path.GetFileName(d).Equals("Pal", StringComparison.OrdinalIgnoreCase)).ToList();
            var ue4ssFolders = directories.Where(d => d.Contains("ue4ss", StringComparison.OrdinalIgnoreCase)).ToList();
            var palSchemaFolders = directories.Where(d => d.Contains("palschema", StringComparison.OrdinalIgnoreCase)).ToList();

            // Determine primary mod type
            if (palFolders.Any())
            {
                structure.ModType = ModType.TraditionalMod;
                structure.ContentPath = palFolders.First();
                AnalyzeTraditionalModStructure(structure, palFolders.First());
            }

            if (ue4ssFolders.Any())
            {
                structure.ModType = structure.ModType == ModType.Unknown ? ModType.UE4SSMod : ModType.HybridMod;
                AnalyzeUE4SSModStructure(structure, ue4ssFolders);
            }

            if (palSchemaFolders.Any())
            {
                structure.ModType = structure.ModType == ModType.Unknown ? ModType.PalSchemaMod : ModType.HybridMod;
                AnalyzePalSchemaModStructure(structure, palSchemaFolders);
            }

            // Check for loose PAK files
            var pakFiles = files.Where(f => f.EndsWith(".pak", StringComparison.OrdinalIgnoreCase)).ToList();
            if (pakFiles.Any())
            {
                structure.ModType = structure.ModType == ModType.Unknown ? ModType.PakMod : ModType.HybridMod;
                AnalyzePakFiles(structure, pakFiles);
            }

            // Calculate directory depth
            structure.MaxDirectoryDepth = directories.Any() ? 
                directories.Max(d => d.Split(Path.DirectorySeparatorChar).Length - rootPath.Split(Path.DirectorySeparatorChar).Length) : 0;
        }

        private void AnalyzeFileTypes(AdvancedModStructure structure, string rootPath)
        {
            var files = Directory.GetFiles(rootPath, "*", SearchOption.AllDirectories);
            var fileTypes = new Dictionary<string, int>();

            foreach (var file in files)
            {
                var extension = Path.GetExtension(file).ToLowerInvariant();
                if (string.IsNullOrEmpty(extension)) extension = "[no extension]";
                
                fileTypes[extension] = fileTypes.GetValueOrDefault(extension, 0) + 1;
            }

            structure.FileTypeDistribution = fileTypes;

            // Analyze specific file types
            structure.LuaFiles = files.Where(f => f.EndsWith(".lua", StringComparison.OrdinalIgnoreCase)).ToList();
            structure.JsonFiles = files.Where(f => f.EndsWith(".json", StringComparison.OrdinalIgnoreCase)).ToList();
            structure.ConfigFiles = files.Where(f => f.EndsWith(".ini", StringComparison.OrdinalIgnoreCase) || 
                                                    f.EndsWith(".cfg", StringComparison.OrdinalIgnoreCase)).ToList();
            structure.DocumentationFiles = files.Where(f => f.EndsWith(".md", StringComparison.OrdinalIgnoreCase) ||
                                                           f.EndsWith(".txt", StringComparison.OrdinalIgnoreCase) ||
                                                           Path.GetFileName(f).StartsWith("readme", StringComparison.OrdinalIgnoreCase)).ToList();
        }

        private void ExtractAdvancedMetadata(AdvancedModStructure structure)
        {
            // Look for various metadata files
            var metadataFiles = new[] { "mod.json", "package.json", "info.json", "metadata.json", "mod.txt", "readme.md", "readme.txt" };
            
            foreach (var metadataFile in metadataFiles)
            {
                var found = Directory.GetFiles(structure.RootPath, metadataFile, SearchOption.AllDirectories);
                if (found.Length > 0)
                {
                    try
                    {
                        var content = File.ReadAllText(found[0]);
                        
                        if (metadataFile.EndsWith(".json"))
                        {
                            var metadata = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(content);
                            if (metadata != null)
                            {
                                ExtractMetadataFields(structure, metadata);
                            }
                        }
                        else
                        {
                            structure.ReadmeContent = content;
                            ExtractMetadataFromText(structure, content);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to parse metadata file {metadataFile}: {ex.Message}", "MetadataExtraction");
                    }
                }
            }
        }

        private void ExtractMetadataFields(AdvancedModStructure structure, Dictionary<string, JsonElement> metadata)
        {
            if (metadata.TryGetValue("name", out var name) && name.ValueKind == JsonValueKind.String)
                structure.Name = name.GetString() ?? "";

            if (metadata.TryGetValue("version", out var version) && version.ValueKind == JsonValueKind.String)
                structure.Version = version.GetString() ?? "";

            if (metadata.TryGetValue("author", out var author) && author.ValueKind == JsonValueKind.String)
                structure.Author = author.GetString() ?? "";

            if (metadata.TryGetValue("description", out var description) && description.ValueKind == JsonValueKind.String)
                structure.Description = description.GetString() ?? "";

            if (metadata.TryGetValue("dependencies", out var deps) && deps.ValueKind == JsonValueKind.Array)
            {
                foreach (var dep in deps.EnumerateArray())
                {
                    if (dep.ValueKind == JsonValueKind.String)
                    {
                        var depName = dep.GetString();
                        if (!string.IsNullOrEmpty(depName))
                            structure.Dependencies.Add(depName);
                    }
                }
            }

            if (metadata.TryGetValue("tags", out var tags) && tags.ValueKind == JsonValueKind.Array)
            {
                foreach (var tag in tags.EnumerateArray())
                {
                    if (tag.ValueKind == JsonValueKind.String)
                    {
                        var tagName = tag.GetString();
                        if (!string.IsNullOrEmpty(tagName))
                            structure.Tags.Add(tagName);
                    }
                }
            }
        }

        private void ExtractMetadataFromText(AdvancedModStructure structure, string content)
        {
            // Extract metadata from text using regex patterns
            var patterns = new Dictionary<string, string>
            {
                ["author"] = @"(?:author|by|created by):\s*(.+)",
                ["version"] = @"(?:version|ver|v):\s*([^\s\n]+)",
                ["description"] = @"(?:description|desc):\s*(.+)",
                ["dependencies"] = @"(?:requires|dependencies|deps):\s*(.+)"
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(content, pattern.Value, RegexOptions.IgnoreCase | RegexOptions.Multiline);
                if (match.Success)
                {
                    var value = match.Groups[1].Value.Trim();
                    switch (pattern.Key)
                    {
                        case "author":
                            structure.Author = value;
                            break;
                        case "version":
                            structure.Version = value;
                            break;
                        case "description":
                            structure.Description = value;
                            break;
                        case "dependencies":
                            structure.Dependencies.AddRange(value.Split(',', ';').Select(d => d.Trim()).Where(d => !string.IsNullOrEmpty(d)));
                            break;
                    }
                }
            }
        }

        private void AnalyzeDependencies(AdvancedModStructure structure)
        {
            // Analyze Lua files for require() statements
            foreach (var luaFile in structure.LuaFiles)
            {
                try
                {
                    var content = File.ReadAllText(luaFile);
                    var requireMatches = Regex.Matches(content, @"require\s*\(\s*[""']([^""']+)[""']\s*\)", RegexOptions.IgnoreCase);

                    foreach (Match match in requireMatches)
                    {
                        var dependency = match.Groups[1].Value;
                        if (!structure.Dependencies.Contains(dependency))
                        {
                            structure.Dependencies.Add(dependency);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"Failed to analyze Lua file {luaFile}: {ex.Message}", "DependencyAnalysis");
                }
            }

            // Check for UE4SS core mod dependencies
            var ue4ssCoreMods = new[] { "LineTrace", "ConsoleCommandsMod", "ActorDumperMod", "ObjectDumperMod", "BPModLoaderMod" };
            foreach (var coreMod in ue4ssCoreMods)
            {
                if (structure.LuaFiles.Any(f => f.Contains(coreMod, StringComparison.OrdinalIgnoreCase)))
                {
                    if (!structure.Dependencies.Contains(coreMod))
                    {
                        structure.Dependencies.Add(coreMod);
                    }
                }
            }
        }

        private void AnalyzeCompatibility(AdvancedModStructure structure)
        {
            structure.CompatibilityInfo = new ModCompatibilityInfo();

            // Check for game version requirements
            var versionPatterns = new[] { @"game\s*version:\s*([^\s\n]+)", @"palworld\s*([0-9.]+)", @"version\s*([0-9.]+)" };
            var allText = structure.ReadmeContent + " " + string.Join(" ", structure.DocumentationFiles.Take(3).Select(f =>
            {
                try { return File.ReadAllText(f); } catch { return ""; }
            }));

            foreach (var pattern in versionPatterns)
            {
                var match = Regex.Match(allText, pattern, RegexOptions.IgnoreCase);
                if (match.Success)
                {
                    structure.CompatibilityInfo.RequiredGameVersion = match.Groups[1].Value.Trim();
                    break;
                }
            }

            // Check for UE4SS version requirements
            if (structure.ModType == ModType.UE4SSMod || structure.ModType == ModType.HybridMod)
            {
                structure.CompatibilityInfo.RequiresUE4SS = true;

                // Look for UE4SS version in metadata
                var ue4ssVersionMatch = Regex.Match(allText, @"ue4ss\s*(?:version\s*)?([0-9.]+)", RegexOptions.IgnoreCase);
                if (ue4ssVersionMatch.Success)
                {
                    structure.CompatibilityInfo.RequiredUE4SSVersion = ue4ssVersionMatch.Groups[1].Value;
                }
            }

            // Check for PalSchema requirements
            if (structure.ModType == ModType.PalSchemaMod || structure.ModType == ModType.HybridMod)
            {
                structure.CompatibilityInfo.RequiresPalSchema = true;
            }
        }

        private void DetectModFrameworks(AdvancedModStructure structure)
        {
            structure.DetectedFrameworks = new List<string>();

            // Check for common modding frameworks
            var frameworkIndicators = new Dictionary<string, string[]>
            {
                ["UE4SS"] = new[] { "ue4ss", "main.lua", "enabled.txt" },
                ["PalSchema"] = new[] { "palschema", "blueprints", "items", "raw" },
                ["BepInEx"] = new[] { "bepinex", "plugins", ".dll" },
                ["MelonLoader"] = new[] { "melonloader", "mods", "userlibs" },
                ["Custom Framework"] = new[] { "framework", "loader", "injector" }
            };

            foreach (var framework in frameworkIndicators)
            {
                var indicators = framework.Value;
                var allPaths = Directory.GetFileSystemEntries(structure.RootPath, "*", SearchOption.AllDirectories);

                if (indicators.Any(indicator => allPaths.Any(path => path.Contains(indicator, StringComparison.OrdinalIgnoreCase))))
                {
                    structure.DetectedFrameworks.Add(framework.Key);
                }
            }
        }

        private void AnalyzeModComplexity(AdvancedModStructure structure)
        {
            var complexity = 0;

            // File count complexity
            complexity += Math.Min(structure.TotalFiles / 10, 20);

            // Directory depth complexity
            complexity += Math.Min(structure.MaxDirectoryDepth * 2, 10);

            // Lua script complexity
            complexity += Math.Min(structure.LuaFiles.Count * 3, 15);

            // Dependency complexity
            complexity += Math.Min(structure.Dependencies.Count * 2, 10);

            // Framework complexity
            complexity += structure.DetectedFrameworks.Count * 5;

            // File type diversity
            complexity += Math.Min(structure.FileTypeDistribution.Count, 10);

            structure.ComplexityScore = Math.Min(complexity, 100);

            structure.ComplexityLevel = structure.ComplexityScore switch
            {
                < 20 => "Simple",
                < 40 => "Moderate",
                < 70 => "Complex",
                _ => "Very Complex"
            };
        }

        private void ValidateModIntegrity(AdvancedModStructure structure)
        {
            structure.IntegrityIssues = new List<string>();

            // Check for required files based on mod type
            switch (structure.ModType)
            {
                case ModType.UE4SSMod:
                    if (!structure.LuaFiles.Any())
                        structure.IntegrityIssues.Add("UE4SS mod missing Lua scripts");
                    break;

                case ModType.PalSchemaMod:
                    if (!Directory.GetDirectories(structure.RootPath, "*", SearchOption.AllDirectories)
                        .Any(d => d.Contains("blueprints") || d.Contains("items") || d.Contains("raw")))
                        structure.IntegrityIssues.Add("PalSchema mod missing required directories");
                    break;

                case ModType.PakMod:
                    if (!Directory.GetFiles(structure.RootPath, "*.pak", SearchOption.AllDirectories).Any())
                        structure.IntegrityIssues.Add("PAK mod missing .pak files");
                    break;
            }

            // Check for common issues
            if (string.IsNullOrEmpty(structure.Name) && string.IsNullOrEmpty(structure.Description))
                structure.IntegrityIssues.Add("Mod missing name and description");

            if (structure.TotalSize > 1024 * 1024 * 1024) // 1GB
                structure.IntegrityIssues.Add("Mod size is unusually large (>1GB)");

            if (structure.MaxDirectoryDepth > 15)
                structure.IntegrityIssues.Add("Directory structure is unusually deep");
        }

        private void AnalyzeTraditionalModStructure(AdvancedModStructure structure, string palFolder)
        {
            // Analyze traditional Pal folder structure
            var contentPath = Path.Combine(palFolder, "Content");
            if (Directory.Exists(contentPath))
            {
                structure.HasContentFolder = true;
                var pakFiles = Directory.GetFiles(contentPath, "*.pak", SearchOption.AllDirectories);
                structure.ContentPakCount = pakFiles.Length;
            }
        }

        private void AnalyzeUE4SSModStructure(AdvancedModStructure structure, List<string> ue4ssFolders)
        {
            structure.UE4SSModCount = 0;
            foreach (var folder in ue4ssFolders)
            {
                var modDirs = Directory.GetDirectories(folder, "*", SearchOption.AllDirectories)
                    .Where(d => Directory.GetFiles(d, "*.lua", SearchOption.AllDirectories).Any() ||
                               File.Exists(Path.Combine(d, "enabled.txt")));
                structure.UE4SSModCount += modDirs.Count();
            }
        }

        private void AnalyzePalSchemaModStructure(AdvancedModStructure structure, List<string> palSchemaFolders)
        {
            structure.PalSchemaModCount = 0;
            foreach (var folder in palSchemaFolders)
            {
                var modDirs = Directory.GetDirectories(folder, "*", SearchOption.AllDirectories)
                    .Where(d => Directory.Exists(Path.Combine(d, "blueprints")) ||
                               Directory.Exists(Path.Combine(d, "items")) ||
                               Directory.Exists(Path.Combine(d, "raw")));
                structure.PalSchemaModCount += modDirs.Count();
            }
        }

        private void AnalyzePakFiles(AdvancedModStructure structure, List<string> pakFiles)
        {
            structure.PakFileCount = pakFiles.Count;
            structure.TotalPakSize = pakFiles.Sum(f => new FileInfo(f).Length);

            // Check for vanilla game files
            structure.HasVanillaPakFiles = pakFiles.Any(f =>
                Path.GetFileName(f).Equals("Pal-Windows.pak", StringComparison.OrdinalIgnoreCase));
        }
    }
}
