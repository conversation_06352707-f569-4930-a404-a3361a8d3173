using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    public class AppDataManager : IDisposable
    {
        private static AppDataManager? _instance;
        private static readonly object _instanceLock = new();
        
        public static AppDataManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_instanceLock)
                    {
                        _instance ??= new AppDataManager();
                    }
                }
                return _instance;
            }
        }

        private readonly string _appDataPath;
        private readonly string _settingsPath;
        private readonly string _secureSettingsPath;
        private ApplicationSettings _settings;
        private readonly CacheManager _cache;
        private readonly ReaderWriterLockSlim _settingsLock = new();
        private readonly Timer _autoSaveTimer;
        private bool _disposed = false;
        private bool _settingsChanged = false;

        // ✅ Configuration encryption
        private readonly byte[] _encryptionKey;
        private readonly byte[] _machineEntropy;

        private AppDataManager()
        {
            _appDataPath = Path.GetFullPath(Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "PalworldModManager"));
            
            Directory.CreateDirectory(_appDataPath);
            
            _settingsPath = Path.Combine(_appDataPath, "settings.json");
            _secureSettingsPath = Path.Combine(_appDataPath, "secure_settings.dat");
            
            // Initialize encryption
            _machineEntropy = GenerateMachineEntropy();
            _encryptionKey = DeriveEncryptionKey();
            
            _cache = new CacheManager(_appDataPath);
            _settings = LoadSettings();
            
            // Setup auto-save timer (every 5 minutes)
            _autoSaveTimer = new Timer(AutoSaveCallback, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        }

        public ApplicationSettings Settings 
        { 
            get
            {
                _settingsLock.EnterReadLock();
                try
                {
                    return _settings;
                }
                finally
                {
                    _settingsLock.ExitReadLock();
                }
            }
        }
        
        public CacheManager Cache => _cache;
        public string AppDataPath => _appDataPath;

        // ── SETTINGS MANAGEMENT ──
        
        private ApplicationSettings LoadSettings()
        {
            _settingsLock.EnterWriteLock();
            try
            {
                ApplicationSettings? settings = LoadSecureSettings();
                if (settings != null)
                {
                    // Migrate from secure to regular if it's not sensitive data
                    if (!HasSensitiveData(settings))
                    {
                        // Save to regular settings and remove secure file
                        SaveSettingsToFile(settings, _settingsPath);
                        DeleteSecureSettings();
                    }
                    
                    settings.IsFirstRun = false;
                    settings.StartupCount++;
                    settings.LastStartup = DateTime.Now;
                    return settings;
                }
                
                // Fallback to regular settings file
                if (File.Exists(_settingsPath))
                {
                    try
                    {
                        var json = File.ReadAllText(_settingsPath);
                        settings = JsonSerializer.Deserialize<ApplicationSettings>(json) ?? new ApplicationSettings();
                        
                        // Check if we need to move to secure storage
                        if (HasSensitiveData(settings))
                        {
                            SaveSecureSettings(settings);
                            DeleteRegularSettings();
                        }
                        
                        settings.IsFirstRun = false;
                        settings.StartupCount++;
                        settings.LastStartup = DateTime.Now;
                        return settings;
                    }
                    catch (Exception ex)
                    {
                        LogError($"Failed to load settings: {ex.Message}");
                        // Create backup of corrupted settings
                        var backupPath = _settingsPath + $".backup.{DateTime.Now:yyyyMMdd_HHmmss}";
                        try
                        {
                            File.Copy(_settingsPath, backupPath);
                        }
                        catch { }
                    }
                }

                return new ApplicationSettings { IsFirstRun = true };
            }
            finally
            {
                _settingsLock.ExitWriteLock();
            }
        }

        public void SaveSettings()
        {
            if (_disposed) return;
            
            _settingsLock.EnterWriteLock();
            try
            {
                if (HasSensitiveData(_settings))
                {
                    SaveSecureSettings(_settings);
                    DeleteRegularSettings();
                }
                else
                {
                    SaveSettingsToFile(_settings, _settingsPath);
                    DeleteSecureSettings();
                }
                
                _settingsChanged = false;
            }
            finally
            {
                _settingsLock.ExitWriteLock();
            }
        }

        public async Task SaveSettingsAsync()
        {
            await Task.Run(() => SaveSettings());
        }

        private void SaveSettingsToFile(ApplicationSettings settings, string filePath)
        {
            try
            {
                var options = new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                
                var json = JsonSerializer.Serialize(settings, options);
                
                // Atomic write operation
                var tempPath = filePath + ".tmp";
                File.WriteAllText(tempPath, json);
                
                if (File.Exists(filePath))
                {
                    File.Replace(tempPath, filePath, null);
                }
                else
                {
                    File.Move(tempPath, filePath);
                }
            }
            catch (Exception ex)
            {
                LogError($"Failed to save settings to {filePath}: {ex.Message}");
                throw;
            }
        }

        // ✅ Secure settings storage with encryption
        private void SaveSecureSettings(ApplicationSettings settings)
        {
            try
            {
                var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions { WriteIndented = true });
                var encryptedData = EncryptData(Encoding.UTF8.GetBytes(json));
                
                // Atomic write operation
                var tempPath = _secureSettingsPath + ".tmp";
                File.WriteAllBytes(tempPath, encryptedData);
                
                if (File.Exists(_secureSettingsPath))
                {
                    File.Replace(tempPath, _secureSettingsPath, null);
                }
                else
                {
                    File.Move(tempPath, _secureSettingsPath);
                }
            }
            catch (Exception ex)
            {
                LogError($"Failed to save secure settings: {ex.Message}");
                throw;
            }
        }

        private ApplicationSettings? LoadSecureSettings()
        {
            try
            {
                if (!File.Exists(_secureSettingsPath))
                    return null;
                
                var encryptedData = File.ReadAllBytes(_secureSettingsPath);
                var decryptedData = DecryptData(encryptedData);
                var json = Encoding.UTF8.GetString(decryptedData);
                
                return JsonSerializer.Deserialize<ApplicationSettings>(json);
            }
            catch (Exception ex)
            {
                LogError($"Failed to load secure settings: {ex.Message}");
                // Don't throw - fallback to regular settings
                return null;
            }
        }

        private void DeleteRegularSettings()
        {
            try
            {
                if (File.Exists(_settingsPath))
                {
                    File.Delete(_settingsPath);
                }
            }
            catch (Exception ex)
            {
                LogError($"Failed to delete regular settings: {ex.Message}");
            }
        }

        private void DeleteSecureSettings()
        {
            try
            {
                if (File.Exists(_secureSettingsPath))
                {
                    // Securely overwrite before deletion
                    var fileSize = new FileInfo(_secureSettingsPath).Length;
                    var randomData = new byte[fileSize];
                    using (var rng = RandomNumberGenerator.Create())
                    {
                        rng.GetBytes(randomData);
                    }
                    File.WriteAllBytes(_secureSettingsPath, randomData);
                    File.Delete(_secureSettingsPath);
                }
            }
            catch (Exception ex)
            {
                LogError($"Failed to delete secure settings: {ex.Message}");
            }
        }

        private bool HasSensitiveData(ApplicationSettings settings)
        {
            // Check if settings contain sensitive data that should be encrypted
            // For now, we don't have sensitive data in ApplicationSettings
            // This could be extended for API keys, tokens, etc.
            return false;
        }

        // ── ENCRYPTION METHODS ──
        
        private byte[] GenerateMachineEntropy()
        {
            // Generate machine-specific entropy
            var entropy = new List<byte>();
            
            // Add machine name
            entropy.AddRange(Encoding.UTF8.GetBytes(Environment.MachineName));
            
            // Add user name
            entropy.AddRange(Encoding.UTF8.GetBytes(Environment.UserName));
            
            // Add OS version
            entropy.AddRange(Encoding.UTF8.GetBytes(Environment.OSVersion.ToString()));
            
            // Add processor count
            entropy.AddRange(BitConverter.GetBytes(Environment.ProcessorCount));
            
            // Hash the combined entropy
            using var sha256 = SHA256.Create();
            return sha256.ComputeHash(entropy.ToArray());
        }

        private byte[] DeriveEncryptionKey()
        {
            // Derive a key from machine entropy and application-specific data
            var keyData = new List<byte>();
            keyData.AddRange(_machineEntropy);
            keyData.AddRange(Encoding.UTF8.GetBytes("PalworldModManager_v1.4.0"));
            keyData.AddRange(Encoding.UTF8.GetBytes(_appDataPath));
            
            using var sha256 = SHA256.Create();
            return sha256.ComputeHash(keyData.ToArray());
        }

        private byte[] EncryptData(byte[] data)
        {
            using var aes = Aes.Create();
            aes.Key = _encryptionKey;
            aes.GenerateIV();
            
            using var encryptor = aes.CreateEncryptor();
            using var msEncrypt = new MemoryStream();
            
            // Write IV first
            msEncrypt.Write(aes.IV, 0, aes.IV.Length);
            
            using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
            {
                csEncrypt.Write(data, 0, data.Length);
            }
            
            return msEncrypt.ToArray();
        }

        private byte[] DecryptData(byte[] encryptedData)
        {
            using var aes = Aes.Create();
            aes.Key = _encryptionKey;
            
            // Extract IV
            var iv = new byte[aes.IV.Length];
            Array.Copy(encryptedData, 0, iv, 0, iv.Length);
            aes.IV = iv;
            
            using var decryptor = aes.CreateDecryptor();
            using var msDecrypt = new MemoryStream(encryptedData, iv.Length, encryptedData.Length - iv.Length);
            using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using var msResult = new MemoryStream();
            
            csDecrypt.CopyTo(msResult);
            return msResult.ToArray();
        }

        // ── PALWORLD INSTALLATION MANAGEMENT ──
        
        public void SetLastPalworldPath(string path)
        {
            _settingsLock.EnterWriteLock();
            try
            {
                _settings.LastPalworldPath = path;
                AddRecentInstallation(path);
                _settingsChanged = true;
            }
            finally
            {
                _settingsLock.ExitWriteLock();
            }
        }

        public string? GetLastPalworldPath()
        {
            _settingsLock.EnterReadLock();
            try
            {
                // Validate the cached path still exists
                if (!string.IsNullOrEmpty(_settings.LastPalworldPath) && 
                    File.Exists(_settings.LastPalworldPath))
                {
                    return _settings.LastPalworldPath;
                }

                // Try the most recent valid installation
                var validInstall = _settings.RecentInstallations
                    .Where(i => i.IsValid && File.Exists(i.Path))
                    .OrderByDescending(i => i.LastAccessed)
                    .FirstOrDefault();

                if (validInstall != null)
                {
                    // Update the last path atomically
                    _settingsLock.ExitReadLock();
                    _settingsLock.EnterWriteLock();
                    try
                    {
                        _settings.LastPalworldPath = validInstall.Path;
                        _settingsChanged = true;
                        return validInstall.Path;
                    }
                    finally
                    {
                        _settingsLock.ExitWriteLock();
                        _settingsLock.EnterReadLock();
                    }
                }

                return null;
            }
            finally
            {
                _settingsLock.ExitReadLock();
            }
        }

        public void AddRecentInstallation(string path)
        {
            if (string.IsNullOrEmpty(path) || !File.Exists(path)) return;

            _settingsLock.EnterWriteLock();
            try
            {
                // Remove existing entry for this path
                _settings.RecentInstallations.RemoveAll(i => 
                    string.Equals(i.Path, path, StringComparison.OrdinalIgnoreCase));

                // Create new installation with UE4SS detection
                var palRoot = Path.GetDirectoryName(path);
                if (string.IsNullOrEmpty(palRoot)) return;
                
                var detector = new UE4SSDetector(palRoot, _cache);
                
                var installation = new PalworldInstallation
                {
                    Path = path,
                    DisplayName = GetInstallationDisplayName(path),
                    LastAccessed = DateTime.Now,
                    IsValid = true,
                    InstallationType = DetectInstallationType(path),
                    UE4SSStatus = detector.DetectUE4SS(),
                    PalSchemaStatus = detector.DetectPalSchema()
                };

                _settings.RecentInstallations.Insert(0, installation);

                // Keep only the 10 most recent
                if (_settings.RecentInstallations.Count > 10)
                {
                    _settings.RecentInstallations.RemoveRange(10, _settings.RecentInstallations.Count - 10);
                }

                _settingsChanged = true;
                detector.Dispose();
            }
            finally
            {
                _settingsLock.ExitWriteLock();
            }
        }

        public List<PalworldInstallation> GetValidInstallations()
        {
            _settingsLock.EnterWriteLock();
            try
            {
                var valid = new List<PalworldInstallation>();
                bool anyChanges = false;

                foreach (var install in _settings.RecentInstallations)
                {
                    if (File.Exists(install.Path))
                    {
                        if (!install.IsValid)
                        {
                            install.IsValid = true;
                            anyChanges = true;
                        }
                        valid.Add(install);
                    }
                    else
                    {
                        if (install.IsValid)
                        {
                            install.IsValid = false;
                            anyChanges = true;
                        }
                    }
                }

                if (anyChanges)
                {
                    _settingsChanged = true;
                }

                return valid.OrderByDescending(i => i.LastAccessed).ToList();
            }
            finally
            {
                _settingsLock.ExitWriteLock();
            }
        }

        // ── AUTO-DETECTION ──
        
        public List<string> AutoDetectPalworldInstallations()
        {
            var installations = new List<string>();
            var cached = _cache.Get<List<string>>("auto_detected_installations");
            
            if (cached != null && cached.Count > 0)
            {
                // Validate cached results
                var stillValid = cached.Where(File.Exists).ToList();
                if (stillValid.Count == cached.Count)
                {
                    return stillValid;
                }
            }

            // Common Steam locations
            var steamPaths = new[]
            {
                @"C:\Program Files (x86)\Steam\steamapps\common\Palworld\Palworld.exe",
                @"C:\Program Files\Steam\steamapps\common\Palworld\Palworld.exe",
                @"D:\Steam\steamapps\common\Palworld\Palworld.exe",
                @"E:\Steam\steamapps\common\Palworld\Palworld.exe"
            };

            // Epic Games locations
            var epicPaths = new[]
            {
                @"C:\Program Files\Epic Games\Palworld\Palworld.exe",
                @"D:\Epic Games\Palworld\Palworld.exe"
            };

            // Check all potential paths securely
            foreach (var path in steamPaths.Concat(epicPaths))
            {
                try
                {
                    if (File.Exists(path))
                    {
                        // Validate it's actually Palworld
                        var fileInfo = new FileInfo(path);
                        if (fileInfo.Length > 0) // Basic validation
                        {
                            installations.Add(path);
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogError($"Error checking path {path}: {ex.Message}");
                }
            }

            // Cache results for 1 hour
            _cache.Set("auto_detected_installations", installations, TimeSpan.FromHours(1));
            
            return installations;
        }

        // ── WINDOW STATE MANAGEMENT ──
        
        public void SaveWindowState(Point location, Size size, bool maximized)
        {
            _settingsLock.EnterWriteLock();
            try
            {
                if (_settings.Preferences.RememberWindowPosition)
                {
                    _settings.WindowState.Location = location;
                    _settings.WindowState.Size = size;
                    _settings.WindowState.IsMaximized = maximized;
                    _settingsChanged = true;
                }
            }
            finally
            {
                _settingsLock.ExitWriteLock();
            }
        }

        public WindowState GetWindowState()
        {
            _settingsLock.EnterReadLock();
            try
            {
                return _settings.WindowState;
            }
            finally
            {
                _settingsLock.ExitReadLock();
            }
        }

        // ── HELPER METHODS ──
        
        private string GetInstallationDisplayName(string path)
        {
            var dir = Path.GetDirectoryName(path);
            if (dir == null) return "Unknown";

            if (dir.Contains("Steam", StringComparison.OrdinalIgnoreCase))
                return "Palworld (Steam)";
            if (dir.Contains("Epic", StringComparison.OrdinalIgnoreCase))
                return "Palworld (Epic Games)";
            
            return $"Palworld ({Path.GetFileName(dir) ?? "Unknown"})";
        }

        private string DetectInstallationType(string path)
        {
            var dir = Path.GetDirectoryName(path) ?? "";
            
            if (dir.Contains("Steam", StringComparison.OrdinalIgnoreCase))
                return "Steam";
            if (dir.Contains("Epic", StringComparison.OrdinalIgnoreCase))
                return "Epic Games";
            
            return "Manual";
        }

        private void AutoSaveCallback(object? state)
        {
            if (_disposed || !_settingsChanged) return;
            
            try
            {
                SaveSettings();
            }
            catch (Exception ex)
            {
                LogError($"Auto-save failed: {ex.Message}");
            }
        }

        private void LogError(string message)
        {
            // Simple error logging to prevent circular dependencies
            try
            {
                var logPath = Path.Combine(_appDataPath, "error.log");
                var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} - [AppDataManager] {message}{Environment.NewLine}";
                File.AppendAllText(logPath, logEntry);
            }
            catch
            {
                // Ignore logging errors to prevent recursion
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Save any pending changes
                if (_settingsChanged)
                {
                    try
                    {
                        SaveSettings();
                    }
                    catch (Exception ex)
                    {
                        LogError($"Failed to save settings during disposal: {ex.Message}");
                    }
                }
                
                // Dispose timers
                _autoSaveTimer?.Dispose();
                
                // Dispose cache
                _cache?.Dispose();
                
                // Dispose locks
                _settingsLock?.Dispose();
                
                _disposed = true;
            }
        }

        ~AppDataManager()
        {
            Dispose(false);
        }
    }
}