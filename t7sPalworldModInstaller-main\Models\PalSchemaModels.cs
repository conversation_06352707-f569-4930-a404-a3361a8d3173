using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace ModInstallerApp.Models
{
    /// <summary>
    /// Represents a PalSchema configuration with validation and metadata
    /// </summary>
    public class PalSchemaConfig
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Version { get; set; } = "1.0.0";
        public string Author { get; set; } = "";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime ModifiedDate { get; set; } = DateTime.Now;
        public Dictionary<string, object> Configuration { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public PalSchemaConfigType ConfigType { get; set; } = PalSchemaConfigType.Custom;
        public bool IsTemplate { get; set; } = false;
        public string? TemplateCategory { get; set; }
        public List<PalSchemaValidationResult> ValidationResults { get; set; } = new();
        public PalSchemaPerformanceMetrics? PerformanceMetrics { get; set; }
    }

    /// <summary>
    /// Represents a named profile containing multiple PalSchema configurations
    /// </summary>
    public class PalSchemaProfile
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime ModifiedDate { get; set; } = DateTime.Now;
        public List<PalSchemaConfig> Configurations { get; set; } = new();
        public Dictionary<string, object> ProfileSettings { get; set; } = new();
        public bool IsActive { get; set; } = false;
        public string? BackupPath { get; set; }
        public List<string> ConflictingMods { get; set; } = new();
        public PalSchemaProfileValidation ValidationStatus { get; set; } = new();
    }

    /// <summary>
    /// Configuration validation result with detailed feedback
    /// </summary>
    public class PalSchemaValidationResult
    {
        public string PropertyPath { get; set; } = "";
        public PalSchemaValidationSeverity Severity { get; set; } = PalSchemaValidationSeverity.Info;
        public string Message { get; set; } = "";
        public string? SuggestedFix { get; set; }
        public List<string> RelatedProperties { get; set; } = new();
        public string? DocumentationLink { get; set; }
    }

    /// <summary>
    /// Performance metrics for PalSchema configurations
    /// </summary>
    public class PalSchemaPerformanceMetrics
    {
        public double EstimatedMemoryUsage { get; set; } // MB
        public double EstimatedCpuImpact { get; set; } // Percentage
        public int ConfigurationComplexity { get; set; } // 1-10 scale
        public List<string> PerformanceWarnings { get; set; } = new();
        public Dictionary<string, double> DetailedMetrics { get; set; } = new();
    }

    /// <summary>
    /// Profile validation status with comprehensive checks
    /// </summary>
    public class PalSchemaProfileValidation
    {
        public bool IsValid { get; set; } = true;
        public List<PalSchemaValidationResult> ValidationResults { get; set; } = new();
        public List<string> MissingDependencies { get; set; } = new();
        public List<string> ConflictingConfigurations { get; set; } = new();
        public DateTime LastValidated { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Configuration comparison result for profile diffing
    /// </summary>
    public class PalSchemaConfigComparison
    {
        public string PropertyPath { get; set; } = "";
        public object? LeftValue { get; set; }
        public object? RightValue { get; set; }
        public PalSchemaComparisonType ComparisonType { get; set; } = PalSchemaComparisonType.Modified;
        public string? Description { get; set; }
    }

    /// <summary>
    /// Template for common PalSchema configurations
    /// </summary>
    public class PalSchemaTemplate
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Category { get; set; } = "";
        public Dictionary<string, object> DefaultConfiguration { get; set; } = new();
        public List<PalSchemaTemplateParameter> Parameters { get; set; } = new();
        public string? PreviewImagePath { get; set; }
        public List<string> RequiredMods { get; set; } = new();
        public PalSchemaDifficultyLevel DifficultyLevel { get; set; } = PalSchemaDifficultyLevel.Beginner;
    }

    /// <summary>
    /// Template parameter for customizable configuration values
    /// </summary>
    public class PalSchemaTemplateParameter
    {
        public string Name { get; set; } = "";
        public string DisplayName { get; set; } = "";
        public string Description { get; set; } = "";
        public PalSchemaParameterType ParameterType { get; set; } = PalSchemaParameterType.String;
        public object? DefaultValue { get; set; }
        public object? MinValue { get; set; }
        public object? MaxValue { get; set; }
        public List<object> AllowedValues { get; set; } = new();
        public bool IsRequired { get; set; } = false;
        public string? ValidationPattern { get; set; }
    }

    /// <summary>
    /// Configuration optimization suggestion
    /// </summary>
    public class PalSchemaOptimizationSuggestion
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public PalSchemaOptimizationType OptimizationType { get; set; } = PalSchemaOptimizationType.Performance;
        public double EstimatedImpact { get; set; } // Percentage improvement
        public string PropertyPath { get; set; } = "";
        public object? SuggestedValue { get; set; }
        public object? CurrentValue { get; set; }
        public List<string> Benefits { get; set; } = new();
        public List<string> Risks { get; set; } = new();
        public bool IsAutoApplicable { get; set; } = false;
    }

    // Enums for PalSchema management
    public enum PalSchemaConfigType
    {
        Custom,
        Template,
        Imported,
        Generated,
        Backup
    }

    public enum PalSchemaValidationSeverity
    {
        Info,
        Warning,
        Error,
        Critical
    }

    public enum PalSchemaComparisonType
    {
        Added,
        Removed,
        Modified,
        Unchanged
    }

    public enum PalSchemaDifficultyLevel
    {
        Beginner,
        Intermediate,
        Advanced,
        Expert
    }

    public enum PalSchemaParameterType
    {
        String,
        Integer,
        Float,
        Boolean,
        Enum,
        Array,
        Object
    }

    public enum PalSchemaOptimizationType
    {
        Performance,
        Memory,
        Compatibility,
        Stability,
        Features
    }

    /// <summary>
    /// Configuration export/import settings
    /// </summary>
    public class PalSchemaExportSettings
    {
        public bool IncludeMetadata { get; set; } = true;
        public bool IncludeValidationResults { get; set; } = false;
        public bool IncludePerformanceMetrics { get; set; } = false;
        public bool CompressOutput { get; set; } = true;
        public PalSchemaExportFormat Format { get; set; } = PalSchemaExportFormat.Json;
        public string? EncryptionPassword { get; set; }
    }

    public enum PalSchemaExportFormat
    {
        Json,
        Yaml,
        Xml,
        Binary
    }

    /// <summary>
    /// Configuration backup metadata
    /// </summary>
    public class PalSchemaBackupMetadata
    {
        public string BackupId { get; set; } = Guid.NewGuid().ToString();
        public DateTime BackupDate { get; set; } = DateTime.Now;
        public string BackupReason { get; set; } = "";
        public string? ProfileId { get; set; }
        public string? ConfigurationId { get; set; }
        public string BackupPath { get; set; } = "";
        public long BackupSize { get; set; }
        public string ChecksumHash { get; set; } = "";
        public bool IsAutoBackup { get; set; } = false;
        public DateTime? ExpirationDate { get; set; }
    }
}
