using System;
using System.Drawing;
using System.Windows.Forms;

namespace ModInstallerApp.UI
{
    internal static class Theme
    {
        public static readonly Color Surface   = ColorTranslator.FromHtml("#1E1B2E");
        public static readonly Color Track     = ColorTranslator.FromHtml("#2A2740");
        public static readonly Color Accent    = ColorTranslator.FromHtml("#FF5DB1");
        public static readonly Color Accent2   = ColorTranslator.FromHtml("#11D0FF");
        public static readonly Color TextLight = ColorTranslator.FromHtml("#F2F2F2");
        public static readonly Color TextDark  = ColorTranslator.FromHtml("#1B1B1B");
        public static readonly Color Success   = ColorTranslator.FromHtml("#2ECC71");
        public static readonly Color Warning   = ColorTranslator.FromHtml("#F39C12");
        public static readonly Color Error     = ColorTranslator.FromHtml("#E74C3C");

        public static readonly Font BodyFont    = new("Segoe UI Semibold", 10F);
        public static readonly Font HeadingFont = new("Segoe UI Black", 18F);
        public static readonly Font SmallFont   = new("Segoe UI", 8F);

        public static void Apply(Control c)
        {
            c.BackColor = Surface;
            c.ForeColor = TextLight;
            c.Font      = BodyFont;
            
            // Skip theming for ComboBox as we handle it manually
            foreach (Control child in c.Controls) 
            {
                if (child is not ComboBox)
                    Apply(child);
            }
        }
    }
}