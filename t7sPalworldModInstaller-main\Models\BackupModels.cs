using System;
using System.Collections.Generic;

namespace ModInstallerApp.Models
{
    public enum BackupCategory
    {
        UE4SSCore,
        UE4SSCoreMods,
        UserMods,
        GameContentMods,
        PalSchemaSystem,
        PalSchemaMods
    }

    public class BackupSettings
    {
        public bool IncludeUE4SSCore { get; set; } = false;
        public bool IncludeUE4SSCoreMods { get; set; } = false;
        public bool IncludeUserMods { get; set; } = true;
        public bool IncludeGameContentMods { get; set; } = true;
        public bool IncludePalSchemaSystem { get; set; } = false;
        public bool IncludePalSchemaMods { get; set; } = true;
    }

    public class BackupMetadata
    {
        public DateTime BackupDate { get; set; } = DateTime.Now;
        public string BackupReason { get; set; } = "";
        public UE4SSStatus UE4SSStatus { get; set; } = new();
        public PalSchemaStatus PalSchemaStatus { get; set; } = new();
        public BackupSettings BackupSettings { get; set; } = new();
        public List<string> BackedUpFiles { get; set; } = new();
        public long TotalSize { get; set; }
        public string PalworldVersion { get; set; } = "";
        public string BackupVersion { get; set; } = "1.4.0";
        public string BackupPath { get; set; } = "";
        public string GameInstallPath { get; set; } = "";
    }

    public class BackupInfo
    {
        public string Name { get; set; } = "";
        public string Path { get; set; } = "";
        public DateTime Created { get; set; } = DateTime.Now;
        public long Size { get; set; }
        public BackupMetadata Metadata { get; set; } = new();
        public bool IsValid { get; set; } = true;
        public string DisplayName => $"{Name} ({Created:yyyy-MM-dd HH:mm})";
        public string FormattedSize => FormatFileSize(Size);

        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len /= 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }

    public class BackupOperation
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = "";
        public string Reason { get; set; } = "";
        public DateTime StartTime { get; set; } = DateTime.Now;
        public DateTime? EndTime { get; set; }
        public BackupOperationStatus Status { get; set; } = BackupOperationStatus.Pending;
        public int FilesProcessed { get; set; }
        public int TotalFiles { get; set; }
        public long BytesProcessed { get; set; }
        public long TotalBytes { get; set; }
        public string? ErrorMessage { get; set; }
        public List<string> ProcessedFiles { get; set; } = new();
        public List<string> FailedFiles { get; set; } = new();
        public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? DateTime.Now.Subtract(StartTime);
        public double ProgressPercent => TotalFiles > 0 ? (double)FilesProcessed / TotalFiles * 100 : 0;
    }

    public enum BackupOperationStatus
    {
        Pending,
        InProgress,
        Completed,
        Failed,
        Cancelled
    }

    public class RestoreOperation
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string BackupPath { get; set; } = "";
        public string TargetPath { get; set; } = "";
        public DateTime StartTime { get; set; } = DateTime.Now;
        public DateTime? EndTime { get; set; }
        public RestoreOperationStatus Status { get; set; } = RestoreOperationStatus.Pending;
        public int FilesProcessed { get; set; }
        public int TotalFiles { get; set; }
        public long BytesProcessed { get; set; }
        public long TotalBytes { get; set; }
        public string? ErrorMessage { get; set; }
        public List<string> RestoredFiles { get; set; } = new();
        public List<string> FailedFiles { get; set; } = new();
        public List<string> ConflictFiles { get; set; } = new();
        public bool CreateBackupFirst { get; set; } = true;
        public string? PreRestoreBackupPath { get; set; }
        public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? DateTime.Now.Subtract(StartTime);
        public double ProgressPercent => TotalFiles > 0 ? (double)FilesProcessed / TotalFiles * 100 : 0;
    }

    public enum RestoreOperationStatus
    {
        Pending,
        AnalyzingConflicts,
        CreatingPreBackup,
        Restoring,
        Completed,
        Failed,
        Cancelled
    }

    public class BackupConflict
    {
        public string FilePath { get; set; } = "";
        public BackupConflictType Type { get; set; }
        public DateTime BackupFileDate { get; set; }
        public DateTime CurrentFileDate { get; set; }
        public long BackupFileSize { get; set; }
        public long CurrentFileSize { get; set; }
        public ConflictResolution Resolution { get; set; } = ConflictResolution.Ask;
    }

    public enum BackupConflictType
    {
        FileNewer,
        FileOlder,
        FileSizeDifferent,
        FileExists,
        FileDoesNotExist
    }

    public enum ConflictResolution
    {
        Ask,
        OverwriteWithBackup,
        KeepCurrent,
        Skip
    }
	
	public enum BackupConflictResolution
	{
		Cancel,
		BackupFirst,
		ForceRestore,
		SmartMerge
	}
}