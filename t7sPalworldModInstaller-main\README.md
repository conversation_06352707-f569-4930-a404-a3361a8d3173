# t7's Palworld Mod Installer

[![Build Status](https://github.com/tseven17/t7sPalworldModInstaller/workflows/CI/badge.svg)](https://github.com/tseven17/t7sPalworldModInstaller/actions)
[![Security Status](https://img.shields.io/badge/security-hardened-green.svg)](./SECURITY_FIXES_SUMMARY.md)
[![Version](https://img.shields.io/badge/version-1.3.0-blue.svg)](./CHANGELOG.md)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](./LICENSE)

A comprehensive, enterprise-grade mod management solution for Palworld with advanced UE4SS integration, PalSchema configuration management, and community sharing features.

## 🚀 Features

### Core Functionality
- **Automatic Palworld Detection**: Supports Steam and Epic Games installations
- **UE4SS Integration**: Complete UE4SS detection, installation, and management
- **Smart Backup System**: Automated backups with integrity verification
- **Mod Installation**: Support for multiple archive formats (ZIP, RAR, 7Z)
- **Conflict Detection**: Advanced mod conflict analysis and resolution

### Advanced Features
- **PalSchema Management**: Visual JSON editor with real-time validation
- **Profile System**: Named configuration profiles with comparison and diffing
- **Community Sharing**: Export/import mod collections with rating system
- **Diagnostic Tools**: Automated issue detection and auto-fix capabilities
- **Performance Optimization**: Async operations with progress tracking

### Security & Quality
- **Enterprise Security**: Comprehensive security hardening against common vulnerabilities
- **Thread Safety**: Concurrent operations with proper synchronization
- **Memory Management**: Optimized resource usage with automatic cleanup
- **Error Recovery**: Robust error handling with graceful degradation
- **Comprehensive Testing**: 166+ test cases covering all functionality

## 📋 Requirements

- **OS**: Windows 10/11 (x64)
- **Framework**: .NET 8.0 Runtime
- **Game**: Palworld (Steam or Epic Games)
- **Disk Space**: 50MB for application + space for mod backups
- **Memory**: 512MB RAM minimum, 1GB recommended

## 🔧 Installation

### Option 1: Download Release (Recommended)
1. Download the latest release from [Releases](https://github.com/tseven17/t7sPalworldModInstaller/releases)
2. Extract to your preferred directory
3. Run `PalworldModInstaller.exe`

### Option 2: Build from Source
```bash
# Clone the repository
git clone https://github.com/tseven17/t7sPalworldModInstaller.git
cd t7sPalworldModInstaller

# Build the application
dotnet build --configuration Release

# Run the application
dotnet run --project t7sPalworldModInstaller-main
```

## 🎮 Quick Start

1. **First Launch**: The application will automatically detect your Palworld installation
2. **UE4SS Setup**: If UE4SS is not detected, the installer will guide you through setup
3. **Install Mods**: Drag and drop mod archives or use the file browser
4. **Manage Profiles**: Create and switch between different mod configurations
5. **Backup & Restore**: Automatic backups ensure you can always revert changes

## 📖 Documentation

- **[User Guide](./USER_GUIDE.md)**: Comprehensive usage instructions
- **[Developer Guide](./DEVELOPMENT.md)**: Development setup and contribution guidelines
- **[Security Documentation](./SECURITY_FIXES_SUMMARY.md)**: Security features and fixes
- **[API Documentation](./docs/API.md)**: Service layer documentation
- **[Troubleshooting](./docs/TROUBLESHOOTING.md)**: Common issues and solutions

## 🛡️ Security

This application has been thoroughly security-hardened with fixes for:
- Path traversal vulnerabilities
- Command injection prevention
- Thread safety issues
- Memory leak prevention
- Secure configuration storage

See [SECURITY_FIXES_SUMMARY.md](./SECURITY_FIXES_SUMMARY.md) for detailed information.

## 🧪 Testing

The application includes comprehensive testing with 166+ test cases:

```bash
# Run all tests
dotnet test

# Run specific test categories
dotnet test --filter "Category=Security"
dotnet test --filter "Category=Performance"
dotnet test --filter "Category=Integration"
```

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](./CONTRIBUTING.md) for guidelines.

### Development Setup
1. Install .NET 8.0 SDK
2. Clone the repository
3. Run `dotnet restore`
4. Open in Visual Studio or VS Code

## 📊 Project Status

- ✅ **Core Features**: Complete and stable
- ✅ **Security Hardening**: Enterprise-grade security implemented
- ✅ **PalSchema Integration**: Advanced configuration management
- ✅ **Community Features**: Sharing and rating system
- ✅ **Testing Framework**: Comprehensive test coverage
- 🔄 **Documentation**: Ongoing improvements

## 🗺️ Roadmap

- **Phase 4.0**: Online mod repository integration
- **Phase 4.1**: AI-powered conflict resolution
- **Phase 4.2**: Plugin system for extensibility
- **Phase 4.3**: Accessibility and internationalization

See [roadmap.txt](./roadmap.txt) for detailed development plans.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

## 🙏 Acknowledgments

- UE4SS development team for the modding framework
- Palworld community for feedback and testing
- Contributors and beta testers

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/tseven17/t7sPalworldModInstaller/issues)
- **Discussions**: [GitHub Discussions](https://github.com/tseven17/t7sPalworldModInstaller/discussions)
- **Discord**: [Community Server](https://discord.gg/palworld-modding)

---

**Made with ❤️ for the Palworld modding community**
