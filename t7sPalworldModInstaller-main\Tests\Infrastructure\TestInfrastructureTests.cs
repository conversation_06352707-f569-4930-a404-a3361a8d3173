using System;
using System.Threading.Tasks;
using NUnit.Framework;
using FluentAssertions;
using ModInstallerApp.Models;
using ModInstallerApp.Tests.Mocks;

namespace ModInstallerApp.Tests.Infrastructure
{
    /// <summary>
    /// Tests for the improved test infrastructure
    /// </summary>
    [TestFixture]
    public class TestInfrastructureTests : IsolatedTestBase
    {
        [Test]
        public void MockCacheManager_ShouldProvideIsolatedCache()
        {
            // Arrange
            var key = "test-key";
            var value = "test-value";
            var expiration = TimeSpan.FromMinutes(5);

            // Act
            CacheManager.Set(key, value, expiration);
            var retrievedValue = CacheManager.Get<string>(key);

            // Assert
            retrievedValue.Should().Be(value);
            CacheManager.Count.Should().Be(1);
            CacheManager.HitRatio.Should().Be(0.5); // 1 hit, 1 miss (from the Get call)
        }

        [Test]
        public void MockCacheManager_ShouldHandleExpiration()
        {
            // Arrange
            var key = "expiring-key";
            var value = "expiring-value";
            var expiration = TimeSpan.FromMilliseconds(1);

            // Act
            CacheManager.Set(key, value, expiration);
            
            // Wait for expiration
            System.Threading.Thread.Sleep(10);
            
            var retrievedValue = CacheManager.Get<string>(key);

            // Assert
            retrievedValue.Should().BeNull();
            CacheManager.Count.Should().Be(0);
        }

        [Test]
        public void MockLogger_ShouldCaptureLogEntries()
        {
            // Arrange
            var mockLogger = Logger as MockEnhancedLogger;
            mockLogger.Should().NotBeNull();

            // Act
            Logger.LogInfo("Test info message");
            Logger.LogWarning("Test warning message");
            Logger.LogError("Test error message");

            // Assert
            var logEntries = mockLogger!.GetLogEntries();
            logEntries.Should().HaveCount(3);
            
            logEntries[0].Level.Should().Be(Interfaces.LogLevel.Info);
            logEntries[0].Message.Should().Be("Test info message");
            
            logEntries[1].Level.Should().Be(Interfaces.LogLevel.Warning);
            logEntries[1].Message.Should().Be("Test warning message");
            
            logEntries[2].Level.Should().Be(Interfaces.LogLevel.Error);
            logEntries[2].Message.Should().Be("Test error message");
        }

        [Test]
        public async Task MockUE4SSDetector_ShouldReturnConfigurableResults()
        {
            // Arrange
            var mockDetector = UE4SSDetector as MockUE4SSDetector;
            mockDetector.Should().NotBeNull();

            // Act - Test default result
            var defaultResult = await UE4SSDetector.DetectUE4SSAsync();

            // Assert
            defaultResult.Should().NotBeNull();
            defaultResult.Status.Should().Be(UE4SSInstallStatus.FullyInstalled);

            // Act - Test custom result
            var customResult = new UE4SSStatus
            {
                Status = UE4SSInstallStatus.NotInstalled,
                LastChecked = DateTime.UtcNow
            };
            mockDetector!.SetDetectionResult(customResult);

            var newResult = await UE4SSDetector.DetectUE4SSAsync();

            // Assert
            newResult.Status.Should().Be(UE4SSInstallStatus.NotInstalled);
        }

        [Test]
        public void TestEnvironment_ShouldProvideIsolatedDirectories()
        {
            // Arrange & Act
            var testDataDir = TestDataDirectory;
            var palworldRoot = TestPalworldRoot;

            // Assert
            testDataDir.Should().NotBeNullOrEmpty();
            palworldRoot.Should().NotBeNullOrEmpty();
            
            System.IO.Directory.Exists(testDataDir).Should().BeTrue();
            System.IO.Directory.Exists(palworldRoot).Should().BeTrue();
            
            // Each test should get a unique directory
            testDataDir.Should().Contain("PalworldModInstallerTests");
        }

        [Test]
        public void CacheManager_ShouldResetBetweenTests()
        {
            // This test verifies that the cache is properly reset between tests
            // If this test runs after MockCacheManager_ShouldProvideIsolatedCache,
            // the cache should be empty due to the reset in SetUp
            
            // Assert
            CacheManager.Count.Should().Be(0);
            CacheManager.HitRatio.Should().Be(0.0);
        }

        [Test]
        public void Logger_ShouldResetBetweenTests()
        {
            // This test verifies that the logger is properly reset between tests
            var mockLogger = Logger as MockEnhancedLogger;
            mockLogger.Should().NotBeNull();

            // Assert
            var logEntries = mockLogger!.GetLogEntries();
            logEntries.Should().BeEmpty();
        }
    }

    /// <summary>
    /// Integration tests demonstrating the integration test infrastructure
    /// </summary>
    [TestFixture]
    [Category("Integration")]
    public class IntegrationTestInfrastructureTests : IntegrationTestBase
    {
        [Test]
        public async Task RealServices_ShouldWorkTogether()
        {
            // This test demonstrates that real services work together properly
            // in the integration test environment
            
            // Arrange
            var cacheKey = "integration-test-key";
            var testValue = "integration-test-value";

            // Act
            CacheManager.Set(cacheKey, testValue, TimeSpan.FromMinutes(5));
            var retrievedValue = CacheManager.Get<string>(cacheKey);

            // Assert
            retrievedValue.Should().Be(testValue);
            
            // Test that UE4SS detector works with real cache
            var detectionResult = await UE4SSDetector.DetectUE4SSAsync();
            detectionResult.Should().NotBeNull();
        }

        [Test]
        public void IntegrationEnvironment_ShouldUseRealServices()
        {
            // Verify that we're using real services, not mocks
            CacheManager.Should().BeOfType<Services.CacheManager>();
            Logger.Should().BeOfType<Services.EnhancedLogger>();
            UE4SSDetector.Should().BeOfType<Services.UE4SSDetector>();
        }
    }

    /// <summary>
    /// Performance tests demonstrating performance test infrastructure
    /// </summary>
    [TestFixture]
    [Category("Performance")]
    public class PerformanceTestInfrastructureTests : IntegrationTestBase
    {
        [Test]
        public void CacheManager_PerformanceTest()
        {
            // Arrange
            const int iterations = 1000;
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            for (int i = 0; i < iterations; i++)
            {
                var key = $"perf-test-{i}";
                var value = $"value-{i}";
                
                CacheManager.Set(key, value, TimeSpan.FromMinutes(5));
                var retrieved = CacheManager.Get<string>(key);
                
                retrieved.Should().Be(value);
            }

            stopwatch.Stop();

            // Assert
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000, 
                $"Cache operations should complete within 1 second for {iterations} iterations");
            
            CacheManager.Count.Should().Be(iterations);
            CacheManager.HitRatio.Should().BeGreaterThan(0.4); // Should have reasonable hit ratio
        }
    }
}
