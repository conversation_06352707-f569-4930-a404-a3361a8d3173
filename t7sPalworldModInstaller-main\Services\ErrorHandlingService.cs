using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ModInstallerApp.Services
{
    public class ErrorHandlingService : IDisposable
    {
        private readonly EnhancedLogger _logger;
        private readonly string _crashReportPath;
        private bool _disposed = false;

        // ✅ Error categories for proper handling
        private static readonly Dictionary<Type, ErrorCategory> ErrorCategories = new()
        {
            [typeof(UnauthorizedAccessException)] = ErrorCategory.Permission,
            [typeof(DirectoryNotFoundException)] = ErrorCategory.FileSystem,
            [typeof(FileNotFoundException)] = ErrorCategory.FileSystem,
            [typeof(IOException)] = ErrorCategory.FileSystem,
            [typeof(SecurityException)] = ErrorCategory.Security,
            [typeof(ArgumentException)] = ErrorCategory.Validation,
            [typeof(InvalidOperationException)] = ErrorCategory.State,
            [typeof(NotSupportedException)] = ErrorCategory.Feature,
            [typeof(OutOfMemoryException)] = ErrorCategory.Resource,
            [typeof(OperationCanceledException)] = ErrorCategory.Cancellation,
            [typeof(TimeoutException)] = ErrorCategory.Network
        };

        // ✅ User-friendly error messages
        private static readonly Dictionary<ErrorCategory, string> UserMessages = new()
        {
            [ErrorCategory.Permission] = "Access denied. Please check file permissions or run as administrator.",
            [ErrorCategory.FileSystem] = "File operation failed. Please check if the file exists and is accessible.",
            [ErrorCategory.Security] = "Security validation failed. The operation was blocked for safety.",
            [ErrorCategory.Validation] = "Invalid input provided. Please check your settings and try again.",
            [ErrorCategory.State] = "Operation cannot be performed in the current state. Please try again later.",
            [ErrorCategory.Feature] = "This feature is not supported on your system.",
            [ErrorCategory.Resource] = "Insufficient system resources. Please close other applications and try again.",
            [ErrorCategory.Cancellation] = "Operation was cancelled by user request.",
            [ErrorCategory.Network] = "Network operation timed out. Please check your connection.",
            [ErrorCategory.Unknown] = "An unexpected error occurred. Please check the logs for details."
        };

        public ErrorHandlingService(EnhancedLogger logger, string appDataPath)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _crashReportPath = Path.Combine(appDataPath, "CrashReports");
            Directory.CreateDirectory(_crashReportPath);

            // Setup global exception handling
            SetupGlobalHandlers();
        }

        // ✅ Comprehensive error handling with user feedback
        public UserErrorResult HandleError(Exception ex, string context = "", bool showToUser = true)
        {
            if (_disposed) return new UserErrorResult { Handled = false };

            try
            {
                var errorInfo = AnalyzeError(ex, context);
                
                // Log the error with full details
                _logger.LogError(errorInfo.Message, errorInfo.Component, ex, new
                {
                    Context = context,
                    Category = errorInfo.Category.ToString(),
                    IsCritical = errorInfo.IsCritical,
                    UserMessage = errorInfo.UserMessage
                });

                // Create crash report for critical errors
                if (errorInfo.IsCritical)
                {
                    _ = Task.Run(() => CreateCrashReportAsync(ex, context, errorInfo));
                }

                // Show user-friendly message if requested
                if (showToUser)
                {
                    ShowUserErrorMessage(errorInfo);
                }

                return new UserErrorResult
                {
                    Handled = true,
                    Category = errorInfo.Category,
                    UserMessage = errorInfo.UserMessage,
                    IsCritical = errorInfo.IsCritical,
                    ShouldRestart = errorInfo.ShouldRestart
                };
            }
            catch (Exception handlingEx)
            {
                // Fallback error handling to prevent recursion
                try
                {
                    _logger?.LogCritical("Error in error handling", "ErrorHandler", new { 
                        OriginalError = ex.Message,
                        HandlingError = handlingEx.Message 
                    });
                }
                catch
                {
                    // Ultimate fallback - write to console
                    Console.WriteLine($"CRITICAL: Error handling failed - Original: {ex.Message}, Handler: {handlingEx.Message}");
                }

                return new UserErrorResult 
                { 
                    Handled = false, 
                    UserMessage = "A critical error occurred in error handling. Please restart the application." 
                };
            }
        }

        // ✅ Async error handling with proper context
        public async Task<UserErrorResult> HandleErrorAsync(Exception ex, string context = "", bool showToUser = true)
        {
            return await Task.Run(() => HandleError(ex, context, showToUser));
        }

        // ✅ Safe method execution with automatic error handling
        public T SafeExecute<T>(Func<T> operation, T defaultValue = default!, string context = "")
        {
            try
            {
                return operation();
            }
            catch (Exception ex)
            {
                HandleError(ex, context, false);
                return defaultValue;
            }
        }

        // ✅ Safe async method execution
        public async Task<T> SafeExecuteAsync<T>(Func<Task<T>> operation, T defaultValue = default!, string context = "")
        {
            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex, context, false);
                return defaultValue;
            }
        }

        // ✅ Safe void method execution
        public bool SafeExecute(Action operation, string context = "")
        {
            try
            {
                operation();
                return true;
            }
            catch (Exception ex)
            {
                HandleError(ex, context, false);
                return false;
            }
        }

        // ✅ Validation with error handling
        public ValidationResult ValidateInput<T>(T input, Func<T, bool> validator, string fieldName = "")
        {
            try
            {
                if (input == null)
                {
                    return new ValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = $"{fieldName} cannot be null",
                        ErrorCode = "VALIDATION_NULL"
                    };
                }

                if (!validator(input))
                {
                    return new ValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = $"{fieldName} validation failed",
                        ErrorCode = "VALIDATION_FAILED"
                    };
                }

                return new ValidationResult { IsValid = true };
            }
            catch (Exception ex)
            {
                HandleError(ex, $"Validation of {fieldName}", false);
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "Validation error occurred",
                    ErrorCode = "VALIDATION_EXCEPTION"
                };
            }
        }

        private ErrorInfo AnalyzeError(Exception ex, string context)
        {
            var category = GetErrorCategory(ex);
            var isCritical = IsCriticalError(ex, category);
            var shouldRestart = ShouldRecommendRestart(ex, category);

            return new ErrorInfo
            {
                Exception = ex,
                Context = context,
                Category = category,
                IsCritical = isCritical,
                ShouldRestart = shouldRestart,
                Message = SanitizeErrorMessage(ex.Message),
                UserMessage = GetUserFriendlyMessage(category, ex),
                Component = ExtractComponent(context, ex),
                Timestamp = DateTime.Now
            };
        }

        private ErrorCategory GetErrorCategory(Exception ex)
        {
            // Check direct type match
            if (ErrorCategories.TryGetValue(ex.GetType(), out var category))
                return category;

            // Check inheritance hierarchy
            foreach (var kvp in ErrorCategories)
            {
                if (kvp.Key.IsAssignableFrom(ex.GetType()))
                    return kvp.Value;
            }

            // Check inner exceptions
            if (ex.InnerException != null)
            {
                return GetErrorCategory(ex.InnerException);
            }

            return ErrorCategory.Unknown;
        }

        private bool IsCriticalError(Exception ex, ErrorCategory category)
        {
            return category switch
            {
                ErrorCategory.Security => true,
                ErrorCategory.Resource when ex is OutOfMemoryException => true,
                ErrorCategory.Unknown => true,
                _ => false
            };
        }

        private bool ShouldRecommendRestart(Exception ex, ErrorCategory category)
        {
            return category switch
            {
                ErrorCategory.Resource when ex is OutOfMemoryException => true,
                ErrorCategory.Unknown => true,
                _ => false
            };
        }

        private string SanitizeErrorMessage(string message)
        {
            if (string.IsNullOrWhiteSpace(message))
                return "Unknown error";

            // Remove potentially sensitive information
            var sanitized = message;
            
            // Remove file paths that might contain usernames
            if (sanitized.Contains(@"\Users\"))
            {
                sanitized = System.Text.RegularExpressions.Regex.Replace(
                    sanitized, @"[A-Za-z]:\\Users\\[^\\]+", @"[USER_DIRECTORY]");
            }

            // Remove other potentially sensitive paths
            sanitized = System.Text.RegularExpressions.Regex.Replace(
                sanitized, @"[A-Za-z]:\\[^\\]*\\[^\\]*\\", @"[DIRECTORY]\\");

            return sanitized;
        }

        private string GetUserFriendlyMessage(ErrorCategory category, Exception ex)
        {
            var baseMessage = UserMessages.GetValueOrDefault(category, UserMessages[ErrorCategory.Unknown]);

            // Add specific guidance for common scenarios
            return category switch
            {
                ErrorCategory.Permission when ex.Message.Contains("UnauthorizedAccessException") => 
                    "Access denied. Try running the application as administrator or check file permissions.",
                ErrorCategory.FileSystem when ex.Message.Contains("being used by another process") => 
                    "File is in use by another application. Please close Palworld and try again.",
                ErrorCategory.Security when ex.Message.Contains("path traversal") => 
                    "Security violation detected. The file operation was blocked for your protection.",
                _ => baseMessage
            };
        }

        private string ExtractComponent(string context, Exception ex)
        {
            if (!string.IsNullOrEmpty(context))
                return context;

            // Try to extract component from stack trace
            var stackTrace = ex.StackTrace;
            if (!string.IsNullOrEmpty(stackTrace))
            {
                var lines = stackTrace.Split('\n');
                foreach (var line in lines)
                {
                    if (line.Contains("ModInstallerApp.Services."))
                    {
                        var startIndex = line.IndexOf("ModInstallerApp.Services.") + "ModInstallerApp.Services.".Length;
                        var endIndex = line.IndexOf('.', startIndex);
                        if (endIndex > startIndex)
                        {
                            return line.Substring(startIndex, endIndex - startIndex);
                        }
                    }
                }
            }

            return "Unknown";
        }

        private void ShowUserErrorMessage(ErrorInfo errorInfo)
        {
            try
            {
                var icon = errorInfo.IsCritical ? MessageBoxIcon.Error : MessageBoxIcon.Warning;
                var title = errorInfo.IsCritical ? "Critical Error" : "Warning";

                var message = errorInfo.UserMessage;
                if (errorInfo.ShouldRestart)
                {
                    message += "\n\nIt is recommended to restart the application.";
                }

                MessageBox.Show(message, title, MessageBoxButtons.OK, icon);
            }
            catch (Exception ex)
            {
                // Fallback to console if MessageBox fails
                Console.WriteLine($"Error displaying message: {ex.Message}");
                Console.WriteLine($"Original error: {errorInfo.UserMessage}");
            }
        }

        private async Task CreateCrashReportAsync(Exception ex, string context, ErrorInfo errorInfo)
        {
            try
            {
                var crashReport = new CrashReport
                {
                    Timestamp = DateTime.Now,
                    Context = context,
                    Category = errorInfo.Category.ToString(),
                    Message = errorInfo.Message,
                    StackTrace = SanitizeStackTrace(ex.StackTrace),
                    InnerException = ex.InnerException?.Message,
                    TargetSite = ex.TargetSite?.Name,
                    Source = ex.Source,
                    SystemInfo = GatherSystemInfo(),
                    ApplicationInfo = GatherApplicationInfo()
                };

                var fileName = $"crash_{DateTime.Now:yyyyMMdd_HHmmss}_{Guid.NewGuid():N[..8]}.json";
                var filePath = Path.Combine(_crashReportPath, fileName);

                var json = JsonSerializer.Serialize(crashReport, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                await File.WriteAllTextAsync(filePath, json);

                _logger.LogInfo($"Crash report created: {fileName}", "ErrorHandler");

                // Clean up old crash reports (keep last 10)
                await CleanupOldCrashReports();
            }
            catch (Exception crashEx)
            {
                _logger?.LogError("Failed to create crash report", "ErrorHandler", crashEx);
            }
        }

        private string SanitizeStackTrace(string? stackTrace)
        {
            if (string.IsNullOrEmpty(stackTrace))
                return "";

            // Remove file paths and line numbers for privacy
            return System.Text.RegularExpressions.Regex.Replace(
                stackTrace, @" in [A-Za-z]:\\.*?:line \d+", " [LOCATION_REMOVED]");
        }

        private Dictionary<string, object> GatherSystemInfo()
        {
            return new Dictionary<string, object>
            {
                ["OS"] = Environment.OSVersion.ToString(),
                ["CLR"] = Environment.Version.ToString(),
                ["MachineName"] = Environment.MachineName,
                ["ProcessorCount"] = Environment.ProcessorCount,
                ["WorkingSet"] = Environment.WorkingSet,
                ["Is64BitOS"] = Environment.Is64BitOperatingSystem,
                ["Is64BitProcess"] = Environment.Is64BitProcess
            };
        }

        private Dictionary<string, object> GatherApplicationInfo()
        {
            return new Dictionary<string, object>
            {
                ["Version"] = "1.4.0",
                ["StartTime"] = DateTime.Now,
                ["CurrentDirectory"] = Environment.CurrentDirectory,
                ["CommandLine"] = Environment.CommandLine
            };
        }

        private async Task CleanupOldCrashReports()
        {
            try
            {
                var crashFiles = Directory.GetFiles(_crashReportPath, "crash_*.json")
                    .Select(f => new FileInfo(f))
                    .OrderByDescending(f => f.CreationTime)
                    .ToArray();

                // Keep only the 10 most recent crash reports
                for (int i = 10; i < crashFiles.Length; i++)
                {
                    await Task.Run(() => crashFiles[i].Delete());
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning($"Failed to cleanup old crash reports: {ex.Message}", "ErrorHandler");
            }
        }

        private void SetupGlobalHandlers()
        {
            // Setup global exception handlers
            Application.ThreadException += (sender, e) =>
            {
                HandleError(e.Exception, "UI Thread Exception", true);
            };

            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                if (e.ExceptionObject is Exception ex)
                {
                    HandleError(ex, "Unhandled Domain Exception", true);
                }
            };

            TaskScheduler.UnobservedTaskException += (sender, e) =>
            {
                HandleError(e.Exception, "Unobserved Task Exception", false);
                e.SetObserved(); // Prevent process termination
            };
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Cleanup resources
                _disposed = true;
            }
        }

        ~ErrorHandlingService()
        {
            Dispose(false);
        }
    }

    // ✅ Supporting models for error handling
    public class ErrorInfo
    {
        public Exception Exception { get; set; } = null!;
        public string Context { get; set; } = "";
        public ErrorCategory Category { get; set; }
        public bool IsCritical { get; set; }
        public bool ShouldRestart { get; set; }
        public string Message { get; set; } = "";
        public string UserMessage { get; set; } = "";
        public string Component { get; set; } = "";
        public DateTime Timestamp { get; set; }
    }

    public class UserErrorResult
    {
        public bool Handled { get; set; }
        public ErrorCategory Category { get; set; }
        public string UserMessage { get; set; } = "";
        public bool IsCritical { get; set; }
        public bool ShouldRestart { get; set; }
    }

    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = "";
        public string ErrorCode { get; set; } = "";
    }

    public class CrashReport
    {
        public DateTime Timestamp { get; set; }
        public string Context { get; set; } = "";
        public string Category { get; set; } = "";
        public string Message { get; set; } = "";
        public string StackTrace { get; set; } = "";
        public string? InnerException { get; set; }
        public string? TargetSite { get; set; }
        public string? Source { get; set; }
        public Dictionary<string, object> SystemInfo { get; set; } = new();
        public Dictionary<string, object> ApplicationInfo { get; set; } = new();
    }

    public enum ErrorCategory
    {
        Unknown,
        Permission,
        FileSystem,
        Security,
        Validation,
        State,
        Feature,
        Resource,
        Cancellation,
        Network
    }
}